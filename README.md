# Vibe Check

**Documentation Cycle: Strawberry | Updated: 28-06-2025**

A comprehensive Python project analysis tool that provides insights into code quality, dependencies, complexity, documentation, and more. Vibe Check uses a simple, efficient analysis engine to provide fast and accurate results.

## 📋 **Complete Project Overview**

**For comprehensive information about Vibe Check, see [VIBE_CHECK_COMPLETE_OVERVIEW.md](VIBE_CHECK_COMPLETE_OVERVIEW.md)**

This document provides the complete feature matrix, current status, roadmap, and detailed capabilities overview.

## 🚨 **DEVELOPMENT STATUS NOTICE**

**Current Status**: Phase 0 (Foundation) - INCOMPLETE
**Development**: Phase 1 development is BLOCKED until foundation issues are resolved

### Critical Issues Requiring Attention:
- ❌ **Production Print Statements**: Multiple print() calls found in production code
- ❌ **Actor System Remnants**: Incomplete removal of deprecated actor system
- ❌ **Test Coverage**: Test system broken, cannot verify coverage claims
- ⚠️ **File Size Management**: 35+ files over 600 lines requiring refactoring

### Verification Status:
```bash
# Run this command to check current status:
python scripts/verify_phase_0_completion.py
```

**For Contributors**: Please see [Phase 0 Completion Plan](docs/roadmap/PHASE_0_COMPLETION_PLAN.md) before making changes.

[![PyPI version](https://img.shields.io/pypi/v/vibe-check.svg)](https://pypi.org/project/vibe-check/)
[![Python versions](https://img.shields.io/pypi/pyversions/vibe-check.svg)](https://pypi.org/project/vibe-check/)
[![License](https://img.shields.io/github/license/ptzajac/vibe_check.svg)](https://github.com/ptzajac/vibe_check/blob/main/LICENSE)

## Features

- **Comprehensive Analysis**: Analyze Python projects for code quality, dependencies, complexity, type coverage, documentation quality, and more
- **Multiple Tool Integration**: Seamlessly integrate with various analysis tools like ruff, mypy, bandit, and more
- **Interactive Visualizations**: Generate visualizations of project structure, dependencies, complexity, and metrics
- **Contextual Adaptation**: Analysis adapts to each file's characteristics for more relevant results
- **Customizable**: Configure analysis scope, tools, and reporting options
- **Clean Architecture**: Follows Clean Architecture principles with clear separation of concerns
- **Simple Design**: Uses a straightforward analysis engine with clear component separation

## Installation

### Quick Start - Standalone Installation

**Vibe Check provides substantial value with zero external dependencies:**

```bash
pip install vibe-check
```

This gives you:
- ✅ **Comprehensive standalone analysis** using Python's built-in AST module
- ✅ **Code quality metrics** (complexity, maintainability, documentation coverage)
- ✅ **Style and formatting checks** (line length, whitespace, naming conventions)
- ✅ **Basic security pattern detection** (dangerous function calls, hardcoded secrets)
- ✅ **Import analysis** and dependency visualization
- ✅ **Rich reporting** with actionable insights

### Enhanced Installation - Optional Analysis Tools

**For even more comprehensive analysis, install optional tools:**

```bash
# Install with enhanced analysis capabilities
pip install vibe-check[analysis]

# Or install specific tool groups
pip install vibe-check[security]      # Security analysis tools
pip install vibe-check[visualization] # Enhanced visualizations
pip install vibe-check[web]          # Web interface
pip install vibe-check[tui]          # Terminal UI

# Install everything
pip install vibe-check[full]
```

**Optional analysis tools include:**
- `ruff` - Fast Python linter (enhances style checking)
- `mypy` - Static type checker (adds type analysis)
- `bandit` - Security linter (enhanced security scanning)
- `pylint` - Comprehensive linter (detailed code analysis)

**Key Principle:** External tools enhance Vibe Check's capabilities but are never required. Vibe Check provides unique value through:
- **Meta-analysis** - Cross-tool correlation and pattern detection
- **Intelligent insights** - Actionable recommendations based on combined findings
- **Graceful degradation** - Comprehensive fallback analysis when tools are missing

### From Source

```bash
# Clone the repository
git clone https://github.com/ptzajac/vibe_check.git
cd vibe_check

# Install in development mode
pip install -e .

# Install with extras
pip install -e ".[full]"
```

## Quick Start

### Command Line Interface

**Primary Entry Point**: `vibe-check` command

Analyze a project with the default configuration:

```bash
vibe-check analyze /path/to/your/project
```

Customize the analysis:

```bash
vibe-check analyze /path/to/your/project --output ./results --verbose --security-focused
```

Run with a custom configuration file:

```bash
vibe-check analyze /path/to/your/project --config ./my_config.yaml
```

Launch the text-based UI:

```bash
vibe-check tui /path/to/your/project
```

Run as a Python module:

```bash
python -m vibe_check analyze /path/to/your/project
```

### CLI Commands and Options

```
Usage: vibe-check [OPTIONS] COMMAND [ARGS]...

  Vibe Check - Project Analysis Tool

  A tool for analyzing Python projects with comprehensive reporting.

Options:
  --version  Show the version and exit.
  -h, --help  Show this message and exit.

Commands:
  analyze    Analyze a project with comprehensive reporting.
  tui        Launch the text-based UI for analysis.
  web        Launch the web dashboard for analysis.
  plugin     Manage analysis plugins.
```

#### Analyze Command Options

```
Usage: vibe-check analyze [OPTIONS] PROJECT_PATH

  Analyze a project with comprehensive reporting.

  This command will analyze the project at PROJECT_PATH using Vibe Check's
  efficient analysis engine, which provides comprehensive reporting and
  detailed insights into code quality.

Options:
  -c, --config PATH              Path to configuration file
  -o, --output PATH              Output directory for analysis results
  -v, --verbose                  Enable verbose output
  -q, --quiet                    Minimize output
  --security-focused             Prioritize security analysis
  --quality-focused              Prioritize code quality analysis
  -f, --format [text|json|html]  Output format
  -h, --help                     Show this message and exit.
```

## Python API

Vibe Check provides a comprehensive Python API for integration into your own tools and workflows.

### Basic Usage

```python
from vibe_check import analyze_project

# Simple usage with defaults
metrics = analyze_project("/path/to/your/project")

# With custom options
metrics = analyze_project(
    project_path="/path/to/your/project",
    output_dir="./results",
    config_path="./myconfig.yaml",
    config_override={"analyze_docs": True},
    show_progress=True
)

# Access the results
print(f"Total files analyzed: {metrics.total_file_count}")
print(f"Average complexity: {metrics.avg_complexity:.2f}")
```

### Advanced Usage

#### Custom Analysis Configuration

```python
from vibe_check import analyze_project
from vibe_check.core.models import AnalysisConfig

# Create a custom configuration
config = AnalysisConfig(
    file_extensions=[".py", ".pyx", ".pyi"],
    exclude_patterns=["**/venv/**", "**/tests/**", "**/.git/**"],
    tools={
        "ruff": {
            "enabled": True,
            "args": ["--select=E,F,W,I,C", "--ignore=E501"]
        },
        "mypy": {
            "enabled": True,
            "args": ["--ignore-missing-imports", "--disallow-untyped-defs"]
        },
        "bandit": {
            "enabled": True,
            "args": ["--recursive", "--severity-level=medium"]
        }
    },
    analyze_docs=True,
    max_workers=4
)

# Run analysis with custom configuration
metrics = analyze_project("/path/to/your/project", config=config)
```

#### Working with Results

```python
from vibe_check import analyze_project

metrics = analyze_project("/path/to/your/project")

# Access file metrics
for file_path, file_metrics in metrics.file_metrics.items():
    print(f"File: {file_path}")
    print(f"  Lines: {file_metrics.line_count}")
    print(f"  Complexity: {file_metrics.complexity}")
    print(f"  Issues: {len(file_metrics.issues)}")

    # Access issues
    for issue in file_metrics.issues:
        print(f"  - {issue.code}: {issue.message} (line {issue.line})")

# Access directory metrics
for dir_path, dir_metrics in metrics.directory_metrics.items():
    print(f"Directory: {dir_path}")
    print(f"  Files: {dir_metrics.file_count}")
    print(f"  Average Complexity: {dir_metrics.avg_complexity:.2f}")

# Access tool results
for tool_name, tool_results in metrics.tool_results.items():
    print(f"Tool: {tool_name}")
    print(f"  Issues: {len(tool_results.get('issues', []))}")
```

#### Using the Simple Analyzer Directly

For advanced use cases, you can use the simple analyzer directly:

```python
from vibe_check.core.simple_analyzer import simple_analyze_project
from pathlib import Path

# Configure the analysis
config = {
    "tools": {
        "ruff": {"enabled": True},
        "mypy": {"enabled": True}
    }
}

# Run the analysis
metrics = simple_analyze_project(
    project_path=Path("/path/to/your/project"),
    output_dir=Path("./results"),
    config=config
)

print(f"Analysis complete: {metrics.total_file_count} files analyzed")
```

## Reports and Visualizations

Vibe Check generates comprehensive reports and visualizations to help you understand your project's quality and structure.

### Generated Reports

- **Summary Report**: High-level overview of the project with key metrics and findings
- **Issue Report**: Detailed list of issues found in the project, categorized by severity and type
- **Metrics Report**: Comprehensive metrics for the project, including complexity, maintainability, and test coverage
- **Recommendation Report**: Actionable recommendations for improvement, prioritized by impact
- **Interactive Dashboard**: HTML dashboard with visualizations and interactive exploration

### Visualization Types

- **Dependency Graphs**: Visualize module dependencies and identify circular references
- **Complexity Heatmaps**: Identify hotspots of complexity in your codebase
- **Issue Distribution**: See where issues are concentrated in your project
- **Metrics Trends**: Track metrics over time (when multiple analyses are run)
- **Architecture Diagrams**: Visualize the high-level architecture of your project

### Sample Report Output

```markdown
# Project Analysis Summary

## Overview
- Project: my-python-project
- Files Analyzed: 127
- Total Lines of Code: 15,432
- Average Complexity: 4.7
- Total Issues: 342

## Key Findings
- 3 security vulnerabilities detected (1 high, 2 medium)
- 15 files exceed complexity threshold
- 7 circular dependencies identified
- Type coverage: 78%
- Documentation coverage: 65%

## Recommendations
1. Address high-severity security issues in auth.py
2. Refactor high-complexity modules (user_manager.py, data_processor.py)
3. Resolve circular dependencies between core and utils modules
4. Improve type annotations in the API module
5. Add missing docstrings to public functions
```

## Configuration

Vibe Check can be configured via a YAML configuration file, environment variables, or programmatically through the API.

### Configuration File

Create a `vibe_check_config.yaml` file:

```yaml
# Analysis scope
file_extensions:
  - .py
  - .pyx
  - .pyi
analyze_docs: true
exclude_patterns:
  - "**/venv/**"
  - "**/tests/**"
  - "**/.git/**"
  - "**/node_modules/**"

# Analysis settings
max_workers: 4
output_format: "html"
report_verbosity: "detailed"

# Tool configuration
tools:
  # Code quality tools
  ruff:
    enabled: true
    args:
      - "--select=E,F,W,I,C"
      - "--ignore=E501"  # Ignore line length errors

  black:
    enabled: true
    args:
      - "--line-length=100"

  # Type checking
  mypy:
    enabled: true
    args:
      - "--ignore-missing-imports"
      - "--disallow-untyped-defs"

  # Security checks
  bandit:
    enabled: true
    args:
      - "--recursive"
      - "--severity-level=medium"

  # Documentation analysis
  pydocstyle:
    enabled: true
    args: []

  # Complexity analysis
  complexity:
    enabled: true
    threshold: 10
```

### Environment Variables

You can also configure Vibe Check using environment variables:

```bash
# Set configuration options
export VIBE_CHECK_OUTPUT_DIR="./results"
export VIBE_CHECK_MAX_WORKERS=4
export VIBE_CHECK_ANALYZE_DOCS=true
export VIBE_CHECK_TOOLS_RUFF_ENABLED=true
export VIBE_CHECK_TOOLS_MYPY_ENABLED=true

# Run Vibe Check
vibe-check analyze /path/to/your/project
```

## Project Structure

Vibe Check follows modern Python packaging standards with a clean, organized structure:

```
vibe_check/                    # Main package
├── __init__.py               # Package initialization
├── __main__.py               # CLI entry point
├── cli/                      # Command-line interface
├── core/                     # Core functionality
│   ├── simple_analyzer.py   # Simple analysis engine
│   ├── analysis/             # Analysis engines
│   └── models/               # Data models
├── config/                   # Configuration files
├── plugins/                  # Plugin system
├── tools/                    # Analysis tool integrations
└── ui/                       # User interfaces

tests/                        # Test suite
├── unit/                     # Unit tests
├── integration/              # Integration tests
└── functional/               # Functional tests

docs/                         # Documentation
├── guides/                   # User guides
└── api/                      # API documentation

scripts/                      # Development scripts
examples/                     # Usage examples
test_projects/                # Test projects for analysis
legacy/                       # Legacy and experimental code
```

## Architecture Overview

Vibe Check uses a simple, efficient analysis engine that provides comprehensive code analysis with minimal complexity.

### Simple Analysis Engine

The analysis engine is designed for clarity and efficiency:

1. **Direct Analysis**: Straightforward file-by-file analysis without complex orchestration
2. **Tool Integration**: Clean integration with popular Python analysis tools
3. **Comprehensive Reporting**: Detailed reports with actionable insights
4. **Extensible Design**: Easy to add new analysis tools and report formats

### Analysis Components

The system is built around simple, focused components:

- **Project Analyzer**: Coordinates the analysis of the entire project
- **File Analyzer**: Analyzes individual files and extracts metadata
- **Tool Executor**: Runs specific analysis tools like ruff, mypy, bandit
- **Report Generator**: Generates reports from analysis results
- **Visualization Engine**: Creates visualizations of project metrics and structure

### System Components

![Vibe Check Architecture](https://example.com/vibe_check_architecture.png)

The architecture consists of several key components:

- **Core**: Contains the analysis engine, models, and coordination logic
- **Tools**: Integrates with various analysis tools through a plugin system
- **UI**: Provides multiple user interfaces (CLI, TUI, Web)
- **Config**: Manages configuration and settings
- **Reporting**: Generates reports and visualizations

### Key Benefits

- **Simple Design**: Clear, understandable architecture without unnecessary complexity
- **Reliable**: Straightforward execution path reduces potential failure points
- **Extensible**: Plugin system allows easy addition of new tools
- **Fast**: Efficient analysis engine provides quick results
- **Scalable**: Architecture scales from small projects to large codebases

## Contributing

We welcome contributions to Vibe Check! Here's how you can help:

### Getting Started

1. Fork the repository
2. Clone your fork: `git clone https://github.com/ptzajac/vibe_check.git`
3. Create a branch: `git checkout -b feature/your-feature-name`
4. Install development dependencies: `pip install -e ".[dev]"`

### Development Setup

After cloning and installing the development dependencies:

```bash
# Install pre-commit hooks (recommended)
pre-commit install

# Run the full development setup
pip install -e ".[dev]"

# Verify installation
vibe-check --version
```

### Development Workflow

1. Make your changes
2. Run tests: `pytest`
3. Format code: `black .`
4. Check types: `mypy .`
5. Lint code: `ruff check .`
6. Run all checks: `pre-commit run --all-files`
7. Commit your changes: `git commit -m "Add your feature"`
8. Push to your fork: `git push origin feature/your-feature-name`
9. Create a pull request

### Contribution Guidelines

- Follow the code style (PEP 8, Black formatting)
- Add tests for new features
- Update documentation for changes
- Keep pull requests focused on a single change
- Ensure all tests pass before submitting

For more detailed information, see [CONTRIBUTING.md](CONTRIBUTING.md).

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgements

- Thanks to all contributors who have helped make Vibe Check better
- Special thanks to the developers of the tools we integrate with
- Inspired by various code analysis tools and architectural patterns
