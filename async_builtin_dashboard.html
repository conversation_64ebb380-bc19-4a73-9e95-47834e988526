
        <!DOCTYPE html>
        <html>
        <head>
            <title>Async Dashboard</title>
            <style>
                .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; padding: 20px; }
                .metric, .chart, .panel { border: 1px solid #ddd; padding: 20px; border-radius: 8px; background: white; }
                .value { font-size: 2em; color: #007bff; text-align: center; }
                h1 { text-align: center; color: #333; }
                h3 { margin-top: 0; color: #666; }
            </style>
        </head>
        <body>
            <h1>Async Performance Dashboard</h1>
            <div class="dashboard">
                <div class="metric"><h3>files_analyzed</h3><div class="value">100</div></div><div class="metric"><h3>avg_complexity</h3><div class="value">15.2</div></div><div class="metric"><h3>quality_score</h3><div class="value">8.7</div></div><div class="metric"><h3>total_issues</h3><div class="value">23</div></div><div class="chart"><h3>complexity_chart</h3><canvas data-x="['Low', 'Med', 'High']" data-y="[45, 35, 20]"></canvas></div><div class="chart"><h3>quality_chart</h3><canvas data-x="['Dir1', 'Dir2', 'Dir3']" data-y="[8.5, 7.2, 9.1]"></canvas></div>
            </div>
        </body>
        </html>
        