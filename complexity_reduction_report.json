{"summary": {"total_files_analyzed": 286, "high_complexity_files": 65, "reductions_applied": 5, "old_average_complexity": 24.75874125874126, "new_average_complexity": 24.75874125874126, "complexity_improvement": 0.0}, "high_complexity_files": [{"file_path": "scripts/extract_high_priority.py", "total_complexity": 132, "total_lines": 435, "function_count": 7, "avg_function_complexity": 19.571428571428573, "functions": [{"name": "parse_args", "complexity": 1, "line": 39}, {"name": "load_pat_json", "complexity": 29, "line": 48}, {"name": "calculate_priority_score", "complexity": 32, "line": 134}, {"name": "rank_files", "complexity": 5, "line": 194}, {"name": "generate_prompt", "complexity": 59, "line": 221}, {"name": "extract_high_priority_files", "complexity": 8, "line": 365}, {"name": "main", "complexity": 3, "line": 414}], "complexity_per_line": 0.30344827586206896}, {"file_path": "vibe_check/cli/formatters.py", "total_complexity": 104, "total_lines": 392, "function_count": 11, "avg_function_complexity": 10.363636363636363, "functions": [{"name": "format_analysis_results", "complexity": 6, "line": 14}, {"name": "_format_vcs_tools_used", "complexity": 9, "line": 78}, {"name": "_format_file_count", "complexity": 14, "line": 126}, {"name": "_format_line_count", "complexity": 12, "line": 161}, {"name": "_format_complexity", "complexity": 5, "line": 198}, {"name": "_format_issue_count", "complexity": 25, "line": 214}, {"name": "_format_coverage", "complexity": 7, "line": 275}, {"name": "_format_issue_summary", "complexity": 10, "line": 289}, {"name": "_format_trend_summary", "complexity": 7, "line": 322}, {"name": "_format_progress_summary", "complexity": 7, "line": 338}, {"name": "_format_generated_reports", "complexity": 12, "line": 359}], "complexity_per_line": 0.2653061224489796}, {"file_path": "examples/pat_caw_actors_prototype.py", "total_complexity": 87, "total_lines": 1118, "function_count": 43, "avg_function_complexity": 2.953488372093023, "functions": [{"name": "propagate", "complexity": 2, "line": 54}, {"name": "_update_with_adaptation", "complexity": 1, "line": 77}, {"name": "_adapt_metadata", "complexity": 3, "line": 91}, {"name": "_adapt_configuration", "complexity": 1, "line": 103}, {"name": "_append_history", "complexity": 1, "line": 113}, {"name": "_adapt_parameters", "complexity": 10, "line": 123}, {"name": "__post_init__", "complexity": 2, "line": 165}, {"name": "to_dict", "complexity": 1, "line": 169}, {"name": "__init__", "complexity": 1, "line": 185}, {"name": "register_actor", "complexity": 1, "line": 198}, {"name": "send", "complexity": 2, "line": 208}, {"name": "receive", "complexity": 1, "line": 244}, {"name": "process_messages", "complexity": 3, "line": 253}, {"name": "_handle_message", "complexity": 2, "line": 263}, {"name": "handle_unknown", "complexity": 1, "line": 280}, {"name": "start", "complexity": 1, "line": 290}, {"name": "stop", "complexity": 1, "line": 296}, {"name": "__init__", "complexity": 1, "line": 312}, {"name": "start_analysis", "complexity": 2, "line": 327}, {"name": "handle_file_metadata", "complexity": 4, "line": 355}, {"name": "handle_analysis_result", "complexity": 5, "line": 396}, {"name": "handle_final_report", "complexity": 1, "line": 426}, {"name": "_select_tools", "complexity": 3, "line": 443}, {"name": "_adapt_tool_config", "complexity": 6, "line": 479}, {"name": "_all_tools_reported", "complexity": 2, "line": 509}, {"name": "__init__", "complexity": 1, "line": 536}, {"name": "handle_init_analysis", "complexity": 2, "line": 549}, {"name": "handle_execute_tool", "complexity": 2, "line": 570}, {"name": "_extract_metadata", "complexity": 8, "line": 591}, {"name": "_read_content", "complexity": 3, "line": 627}, {"name": "__init__", "complexity": 1, "line": 648}, {"name": "handle_request_analysis", "complexity": 1, "line": 659}, {"name": "handle_file_content", "complexity": 6, "line": 677}, {"name": "_run_tool", "complexity": 21, "line": 716}, {"name": "__init__", "complexity": 1, "line": 829}, {"name": "set_visualization_actor", "complexity": 1, "line": 839}, {"name": "handle_generate_report", "complexity": 3, "line": 849}, {"name": "_generate_report", "complexity": 5, "line": 884}, {"name": "_generate_recommendations", "complexity": 8, "line": 946}, {"name": "__init__", "complexity": 1, "line": 986}, {"name": "handle_request_visualization", "complexity": 2, "line": 995}, {"name": "_generate_visualization", "complexity": 2, "line": 1019}, {"name": "run_demo", "complexity": 1, "line": 1050}], "complexity_per_line": 0.0778175313059034}, {"file_path": "vibe_check/core/vcs/rules/framework_rules/fastapi_rules.py", "total_complexity": 87, "total_lines": 389, "function_count": 25, "avg_function_complexity": 4.44, "functions": [{"name": "__init__", "complexity": 1, "line": 23}, {"name": "analyze", "complexity": 4, "line": 32}, {"name": "_is_fastapi_endpoint", "complexity": 7, "line": 44}, {"name": "_check_async_usage", "complexity": 7, "line": 56}, {"name": "_is_blocking_operation", "complexity": 2, "line": 89}, {"name": "__init__", "complexity": 1, "line": 106}, {"name": "analyze", "complexity": 4, "line": 115}, {"name": "_is_fastapi_endpoint", "complexity": 7, "line": 127}, {"name": "_check_input_validation", "complexity": 8, "line": 139}, {"name": "_has_string_validation", "complexity": 8, "line": 169}, {"name": "__init__", "complexity": 1, "line": 187}, {"name": "analyze", "complexity": 7, "line": 196}, {"name": "_is_fastapi_endpoint", "complexity": 7, "line": 225}, {"name": "_has_authentication", "complexity": 8, "line": 237}, {"name": "__init__", "complexity": 1, "line": 260}, {"name": "analyze", "complexity": 5, "line": 269}, {"name": "_is_fastapi_endpoint", "complexity": 5, "line": 287}, {"name": "_has_response_model", "complexity": 5, "line": 296}, {"name": "__init__", "complexity": 1, "line": 309}, {"name": "analyze", "complexity": 5, "line": 318}, {"name": "_is_fastapi_endpoint", "complexity": 7, "line": 336}, {"name": "_has_error_handling", "complexity": 7, "line": 348}, {"name": "get_rules", "complexity": 1, "line": 366}, {"name": "get_rule_categories", "complexity": 1, "line": 377}, {"name": "get_framework_name", "complexity": 1, "line": 386}], "complexity_per_line": 0.2236503856041131}, {"file_path": "scripts/generate_issue_prompts.py", "total_complexity": 86, "total_lines": 533, "function_count": 6, "avg_function_complexity": 15.0, "functions": [{"name": "parse_args", "complexity": 1, "line": 25}, {"name": "load_pat_json", "complexity": 29, "line": 32}, {"name": "extract_issues", "complexity": 34, "line": 118}, {"name": "generate_issue_prompt", "complexity": 13, "line": 256}, {"name": "generate_issue_prompts", "complexity": 10, "line": 424}, {"name": "main", "complexity": 3, "line": 512}], "complexity_per_line": 0.16135084427767354}, {"file_path": "vibe_check/enterprise/collaboration.py", "total_complexity": 81, "total_lines": 876, "function_count": 39, "avg_function_complexity": 3.051282051282051, "functions": [{"name": "__post_init__", "complexity": 2, "line": 49}, {"name": "_get_default_permissions", "complexity": 4, "line": 54}, {"name": "has_permission", "complexity": 1, "line": 73}, {"name": "to_dict", "complexity": 1, "line": 77}, {"name": "add_member", "complexity": 3, "line": 103}, {"name": "remove_member", "complexity": 3, "line": 115}, {"name": "get_member", "complexity": 3, "line": 124}, {"name": "update_member_role", "complexity": 2, "line": 131}, {"name": "get_members_by_role", "complexity": 1, "line": 142}, {"name": "to_dict", "complexity": 1, "line": 146}, {"name": "__init__", "complexity": 2, "line": 164}, {"name": "initialize", "complexity": 2, "line": 178}, {"name": "_load_teams", "complexity": 5, "line": 190}, {"name": "_load_users", "complexity": 4, "line": 207}, {"name": "_save_teams", "complexity": 3, "line": 220}, {"name": "_save_users", "complexity": 3, "line": 233}, {"name": "_team_from_dict", "complexity": 2, "line": 245}, {"name": "create_team", "complexity": 5, "line": 272}, {"name": "get_team", "complexity": 1, "line": 333}, {"name": "get_team_by_name", "complexity": 3, "line": 337}, {"name": "list_teams", "complexity": 2, "line": 344}, {"name": "add_team_member", "complexity": 6, "line": 361}, {"name": "remove_team_member", "complexity": 6, "line": 410}, {"name": "update_member_role", "complexity": 6, "line": 445}, {"name": "delete_team", "complexity": 5, "line": 482}, {"name": "get_team_statistics", "complexity": 3, "line": 511}, {"name": "cleanup", "complexity": 1, "line": 539}, {"name": "__init__", "complexity": 2, "line": 549}, {"name": "initialize", "complexity": 2, "line": 563}, {"name": "_load_configurations", "complexity": 7, "line": 573}, {"name": "_save_configurations", "complexity": 4, "line": 602}, {"name": "save_configuration", "complexity": 2, "line": 628}, {"name": "get_configuration", "complexity": 4, "line": 674}, {"name": "update_configuration", "complexity": 3, "line": 702}, {"name": "delete_configuration", "complexity": 3, "line": 741}, {"name": "grant_access", "complexity": 4, "line": 775}, {"name": "list_configurations", "complexity": 6, "line": 816}, {"name": "get_configuration_statistics", "complexity": 1, "line": 860}, {"name": "cleanup", "complexity": 1, "line": 872}], "complexity_per_line": 0.09246575342465753}, {"file_path": "vibe_check/core/analysis/dependency_analyzer.py", "total_complexity": 81, "total_lines": 697, "function_count": 31, "avg_function_complexity": 3.7419354838709675, "functions": [{"name": "__init__", "complexity": 1, "line": 66}, {"name": "visit_Import", "complexity": 3, "line": 70}, {"name": "visit_ImportFrom", "complexity": 3, "line": 82}, {"name": "__init__", "complexity": 1, "line": 102}, {"name": "analyze_dependencies", "complexity": 1, "line": 107}, {"name": "_extract_imports", "complexity": 6, "line": 144}, {"name": "_file_path_to_module", "complexity": 2, "line": 170}, {"name": "_build_dependency_graph", "complexity": 5, "line": 181}, {"name": "_resolve_import", "complexity": 4, "line": 203}, {"name": "_is_external_module", "complexity": 3, "line": 219}, {"name": "_detect_circular_dependencies", "complexity": 4, "line": 242}, {"name": "_determine_cycle_severity", "complexity": 3, "line": 268}, {"name": "_generate_cycle_suggestions", "complexity": 2, "line": 277}, {"name": "_calculate_dependency_metrics", "complexity": 2, "line": 291}, {"name": "_detect_architectural_violations", "complexity": 4, "line": 324}, {"name": "_classify_dependencies", "complexity": 3, "line": 340}, {"name": "build_tree", "complexity": 6, "line": 358}, {"name": "get_dependency_tree", "complexity": 7, "line": 353}, {"name": "get_most_connected_modules", "complexity": 3, "line": 381}, {"name": "__init__", "complexity": 1, "line": 399}, {"name": "analyze_architecture", "complexity": 1, "line": 404}, {"name": "_detect_layers", "complexity": 8, "line": 423}, {"name": "_detect_architectural_patterns", "complexity": 5, "line": 457}, {"name": "_detect_mvc_pattern", "complexity": 5, "line": 499}, {"name": "_detect_repository_pattern", "complexity": 4, "line": 529}, {"name": "_detect_service_layer_pattern", "complexity": 4, "line": 551}, {"name": "_detect_layered_architecture", "complexity": 3, "line": 572}, {"name": "_detect_layer_violations", "complexity": 6, "line": 592}, {"name": "_get_module_layer", "complexity": 3, "line": 623}, {"name": "_calculate_architectural_metrics", "complexity": 6, "line": 630}, {"name": "_generate_architectural_recommendations", "complexity": 7, "line": 666}], "complexity_per_line": 0.11621233859397417}, {"file_path": "scripts/generate_similarity_report.py", "total_complexity": 78, "total_lines": 504, "function_count": 12, "avg_function_complexity": 7.25, "functions": [{"name": "__post_init__", "complexity": 2, "line": 49}, {"name": "parse_args", "complexity": 1, "line": 54}, {"name": "load_pat_json", "complexity": 3, "line": 63}, {"name": "extract_code_entities", "complexity": 19, "line": 78}, {"name": "get_entity_complexity", "complexity": 3, "line": 164}, {"name": "extract_function_content", "complexity": 10, "line": 171}, {"name": "extract_class_content", "complexity": 10, "line": 217}, {"name": "extract_methods_from_class", "complexity": 4, "line": 263}, {"name": "calculate_similarity_matrix", "complexity": 11, "line": 284}, {"name": "group_similar_entities", "complexity": 8, "line": 336}, {"name": "generate_similarity_report", "complexity": 11, "line": 377}, {"name": "main", "complexity": 5, "line": 458}], "complexity_per_line": 0.15476190476190477}, {"file_path": "vibe_check/core/progress.py", "total_complexity": 75, "total_lines": 996, "function_count": 74, "avg_function_complexity": 2.0, "functions": [{"name": "__init__", "complexity": 1, "line": 28}, {"name": "start", "complexity": 1, "line": 34}, {"name": "update", "complexity": 2, "line": 46}, {"name": "increment", "complexity": 2, "line": 58}, {"name": "complete", "complexity": 1, "line": 69}, {"name": "start_global", "complexity": 1, "line": 75}, {"name": "increment_global", "complexity": 1, "line": 86}, {"name": "start_phase", "complexity": 1, "line": 96}, {"name": "update_phase", "complexity": 1, "line": 107}, {"name": "increment_phase", "complexity": 1, "line": 118}, {"name": "complete_phase", "complexity": 1, "line": 129}, {"name": "fail_phase", "complexity": 1, "line": 139}, {"name": "complete_global", "complexity": 1, "line": 149}, {"name": "fail_global", "complexity": 1, "line": 159}, {"name": "__init__", "complexity": 1, "line": 172}, {"name": "start_global", "complexity": 1, "line": 191}, {"name": "increment_global", "complexity": 5, "line": 207}, {"name": "start_phase", "complexity": 3, "line": 230}, {"name": "update_phase", "complexity": 6, "line": 249}, {"name": "increment_phase", "complexity": 6, "line": 275}, {"name": "complete_phase", "complexity": 3, "line": 301}, {"name": "fail_phase", "complexity": 2, "line": 319}, {"name": "complete_global", "complexity": 3, "line": 337}, {"name": "fail_global", "complexity": 2, "line": 355}, {"name": "__init__", "complexity": 1, "line": 377}, {"name": "_send_update", "complexity": 1, "line": 397}, {"name": "start_global", "complexity": 1, "line": 417}, {"name": "increment_global", "complexity": 2, "line": 434}, {"name": "start_phase", "complexity": 4, "line": 448}, {"name": "update_phase", "complexity": 5, "line": 477}, {"name": "increment_phase", "complexity": 5, "line": 502}, {"name": "complete_phase", "complexity": 5, "line": 527}, {"name": "fail_phase", "complexity": 3, "line": 550}, {"name": "complete_global", "complexity": 4, "line": 570}, {"name": "fail_global", "complexity": 2, "line": 590}, {"name": "start_global", "complexity": 1, "line": 608}, {"name": "increment_global", "complexity": 1, "line": 611}, {"name": "start_phase", "complexity": 1, "line": 614}, {"name": "update_phase", "complexity": 1, "line": 617}, {"name": "increment_phase", "complexity": 1, "line": 620}, {"name": "complete_phase", "complexity": 1, "line": 623}, {"name": "fail_phase", "complexity": 1, "line": 626}, {"name": "complete_global", "complexity": 1, "line": 629}, {"name": "fail_global", "complexity": 1, "line": 632}, {"name": "__init__", "complexity": 1, "line": 639}, {"name": "start", "complexity": 1, "line": 644}, {"name": "update", "complexity": 1, "line": 655}, {"name": "increment", "complexity": 1, "line": 666}, {"name": "complete", "complexity": 1, "line": 676}, {"name": "start_global", "complexity": 1, "line": 681}, {"name": "increment_global", "complexity": 1, "line": 691}, {"name": "start_phase", "complexity": 2, "line": 700}, {"name": "update_phase", "complexity": 2, "line": 713}, {"name": "increment_phase", "complexity": 2, "line": 726}, {"name": "complete_phase", "complexity": 2, "line": 739}, {"name": "fail_phase", "complexity": 1, "line": 751}, {"name": "complete_global", "complexity": 1, "line": 760}, {"name": "fail_global", "complexity": 1, "line": 769}, {"name": "__init__", "complexity": 2, "line": 782}, {"name": "start", "complexity": 1, "line": 794}, {"name": "update", "complexity": 3, "line": 806}, {"name": "increment", "complexity": 3, "line": 821}, {"name": "complete", "complexity": 2, "line": 835}, {"name": "start_global", "complexity": 1, "line": 842}, {"name": "increment_global", "complexity": 1, "line": 852}, {"name": "start_phase", "complexity": 2, "line": 861}, {"name": "update_phase", "complexity": 3, "line": 873}, {"name": "increment_phase", "complexity": 3, "line": 886}, {"name": "complete_phase", "complexity": 3, "line": 901}, {"name": "fail_phase", "complexity": 2, "line": 912}, {"name": "complete_global", "complexity": 1, "line": 923}, {"name": "fail_global", "complexity": 2, "line": 932}, {"name": "get_progress_tracker", "complexity": 4, "line": 944}, {"name": "create_progress_tracker", "complexity": 7, "line": 968}], "complexity_per_line": 0.07530120481927711}, {"file_path": "vibe_check/core/vcs/rules/performance_rules.py", "total_complexity": 70, "total_lines": 400, "function_count": 22, "avg_function_complexity": 4.136363636363637, "functions": [{"name": "__init__", "complexity": 1, "line": 23}, {"name": "analyze", "complexity": 5, "line": 32}, {"name": "_check_for_loop", "complexity": 10, "line": 47}, {"name": "_check_while_loop", "complexity": 4, "line": 81}, {"name": "_check_list_comprehension", "complexity": 3, "line": 103}, {"name": "__init__", "complexity": 1, "line": 134}, {"name": "analyze", "complexity": 4, "line": 143}, {"name": "_check_function_calls", "complexity": 12, "line": 156}, {"name": "_check_membership_tests", "complexity": 8, "line": 195}, {"name": "__init__", "complexity": 1, "line": 220}, {"name": "analyze", "complexity": 4, "line": 229}, {"name": "_check_string_concatenation", "complexity": 7, "line": 242}, {"name": "_check_string_methods", "complexity": 4, "line": 268}, {"name": "_is_string_operation", "complexity": 7, "line": 285}, {"name": "__init__", "complexity": 1, "line": 300}, {"name": "analyze", "complexity": 4, "line": 309}, {"name": "_check_import_statement", "complexity": 2, "line": 322}, {"name": "_check_from_import", "complexity": 3, "line": 337}, {"name": "_is_inside_function", "complexity": 1, "line": 353}, {"name": "__init__", "complexity": 1, "line": 363}, {"name": "analyze", "complexity": 3, "line": 372}, {"name": "_check_memory_usage", "complexity": 5, "line": 383}], "complexity_per_line": 0.175}, {"file_path": "vibe_check/core/vcs/rules/framework_rules/flask_rules.py", "total_complexity": 70, "total_lines": 351, "function_count": 18, "avg_function_complexity": 4.833333333333333, "functions": [{"name": "__init__", "complexity": 1, "line": 23}, {"name": "analyze", "complexity": 4, "line": 32}, {"name": "_is_flask_app_file", "complexity": 2, "line": 61}, {"name": "__init__", "complexity": 1, "line": 69}, {"name": "analyze", "complexity": 16, "line": 78}, {"name": "__init__", "complexity": 1, "line": 123}, {"name": "analyze", "complexity": 14, "line": 132}, {"name": "_has_string_formatting", "complexity": 7, "line": 168}, {"name": "__init__", "complexity": 1, "line": 183}, {"name": "analyze", "complexity": 8, "line": 192}, {"name": "_is_post_route", "complexity": 8, "line": 218}, {"name": "__init__", "complexity": 1, "line": 235}, {"name": "analyze", "complexity": 11, "line": 244}, {"name": "__init__", "complexity": 1, "line": 286}, {"name": "analyze", "complexity": 8, "line": 295}, {"name": "get_rules", "complexity": 1, "line": 331}, {"name": "get_rule_categories", "complexity": 1, "line": 343}, {"name": "get_framework_name", "complexity": 1, "line": 348}], "complexity_per_line": 0.19943019943019943}, {"file_path": "vibe_check/cli/commands.py", "total_complexity": 69, "total_lines": 691, "function_count": 7, "avg_function_complexity": 12.714285714285714, "functions": [{"name": "should_ignore", "complexity": 4, "line": 208}, {"name": "run_vcs_analysis", "complexity": 12, "line": 186}, {"name": "analyze_command", "complexity": 50, "line": 18}, {"name": "tui_command", "complexity": 4, "line": 444}, {"name": "web_command", "complexity": 4, "line": 488}, {"name": "debug_command", "complexity": 5, "line": 536}, {"name": "plugin_command", "complexity": 10, "line": 634}], "complexity_per_line": 0.09985528219971057}, {"file_path": "vibe_check/ai/visualization/data_aggregator.py", "total_complexity": 65, "total_lines": 551, "function_count": 22, "avg_function_complexity": 3.909090909090909, "functions": [{"name": "to_dict", "complexity": 1, "line": 44}, {"name": "to_dict", "complexity": 1, "line": 65}, {"name": "to_dict", "complexity": 1, "line": 88}, {"name": "__init__", "complexity": 1, "line": 103}, {"name": "apply_filters", "complexity": 2, "line": 117}, {"name": "_apply_single_filter", "complexity": 9, "line": 126}, {"name": "_regex_match", "complexity": 2, "line": 157}, {"name": "__init__", "complexity": 1, "line": 169}, {"name": "initialize", "complexity": 2, "line": 176}, {"name": "aggregate_data", "complexity": 6, "line": 192}, {"name": "_aggregate_simple", "complexity": 20, "line": 271}, {"name": "_aggregate_with_grouping", "complexity": 6, "line": 325}, {"name": "_calculate_percentile", "complexity": 3, "line": 362}, {"name": "_weighted_average", "complexity": 2, "line": 377}, {"name": "_moving_average", "complexity": 3, "line": 390}, {"name": "_growth_rate", "complexity": 3, "line": 404}, {"name": "_trend_direction", "complexity": 5, "line": 417}, {"name": "create_time_series_aggregation", "complexity": 13, "line": 446}, {"name": "register_custom_aggregator", "complexity": 1, "line": 513}, {"name": "clear_cache", "complexity": 1, "line": 518}, {"name": "get_aggregator_statistics", "complexity": 2, "line": 523}, {"name": "cleanup", "complexity": 1, "line": 546}], "complexity_per_line": 0.11796733212341198}, {"file_path": "vibe_check/ai/explanation/comment_analyzer.py", "total_complexity": 64, "total_lines": 526, "function_count": 18, "avg_function_complexity": 4.5, "functions": [{"name": "to_dict", "complexity": 1, "line": 50}, {"name": "to_dict", "complexity": 1, "line": 78}, {"name": "__init__", "complexity": 1, "line": 97}, {"name": "extract_comments", "complexity": 11, "line": 106}, {"name": "_is_docstring_context", "complexity": 8, "line": 178}, {"name": "__init__", "complexity": 1, "line": 197}, {"name": "analyze_comment_quality", "complexity": 1, "line": 221}, {"name": "_calculate_quality_score", "complexity": 16, "line": 252}, {"name": "_determine_quality_level", "complexity": 4, "line": 294}, {"name": "_identify_issues", "complexity": 11, "line": 305}, {"name": "_generate_suggestions", "complexity": 8, "line": 334}, {"name": "__init__", "complexity": 1, "line": 364}, {"name": "initialize", "complexity": 2, "line": 371}, {"name": "analyze_comments", "complexity": 5, "line": 379}, {"name": "_calculate_comment_coverage", "complexity": 2, "line": 461}, {"name": "_generate_recommendations", "complexity": 5, "line": 474}, {"name": "get_comment_statistics", "complexity": 2, "line": 498}, {"name": "cleanup", "complexity": 1, "line": 522}], "complexity_per_line": 0.12167300380228137}, {"file_path": "vibe_check/ui/web/components.py", "total_complexity": 64, "total_lines": 627, "function_count": 12, "avg_function_complexity": 6.25, "functions": [{"name": "render_header", "complexity": 1, "line": 17}, {"name": "render_project_selector", "complexity": 9, "line": 31}, {"name": "render_progress", "complexity": 3, "line": 109}, {"name": "render_results", "complexity": 5, "line": 133}, {"name": "render_summary", "complexity": 6, "line": 160}, {"name": "render_issues", "complexity": 16, "line": 199}, {"name": "render_metrics", "complexity": 4, "line": 290}, {"name": "render_recommendations", "complexity": 9, "line": 363}, {"name": "render_visualization", "complexity": 4, "line": 450}, {"name": "render_dependency_graph", "complexity": 12, "line": 483}, {"name": "render_complexity_heatmap", "complexity": 3, "line": 545}, {"name": "render_coverage_chart", "complexity": 3, "line": 579}], "complexity_per_line": 0.10207336523125997}, {"file_path": "vibe_check/core/analysis/framework_rules.py", "total_complexity": 63, "total_lines": 476, "function_count": 18, "avg_function_complexity": 4.444444444444445, "functions": [{"name": "__init__", "complexity": 1, "line": 21}, {"name": "check", "complexity": 12, "line": 28}, {"name": "__init__", "complexity": 1, "line": 88}, {"name": "check", "complexity": 10, "line": 95}, {"name": "_is_route_decorator", "complexity": 4, "line": 151}, {"name": "__init__", "complexity": 1, "line": 164}, {"name": "check", "complexity": 7, "line": 171}, {"name": "_is_fastapi_decorator", "complexity": 4, "line": 225}, {"name": "__init__", "complexity": 1, "line": 238}, {"name": "check", "complexity": 7, "line": 245}, {"name": "__init__", "complexity": 1, "line": 299}, {"name": "check", "complexity": 8, "line": 306}, {"name": "_get_call_chain", "complexity": 3, "line": 341}, {"name": "create_framework_aware_registry", "complexity": 6, "line": 356}, {"name": "__init__", "complexity": 1, "line": 403}, {"name": "analyze_project_with_frameworks", "complexity": 3, "line": 406}, {"name": "_extract_framework_issues", "complexity": 3, "line": 440}, {"name": "_generate_framework_recommendations", "complexity": 7, "line": 456}], "complexity_per_line": 0.1323529411764706}, {"file_path": "vibe_check/plugins/manager.py", "total_complexity": 62, "total_lines": 535, "function_count": 25, "avg_function_complexity": 3.44, "functions": [{"name": "wrapper", "complexity": 1, "line": 38}, {"name": "decorator", "complexity": 1, "line": 36}, {"name": "plugin_hook", "complexity": 1, "line": 26}, {"name": "__init__", "complexity": 3, "line": 51}, {"name": "discover_plugins", "complexity": 2, "line": 76}, {"name": "_discover_from_entry_points", "complexity": 5, "line": 89}, {"name": "_discover_from_directory", "complexity": 8, "line": 108}, {"name": "_scan_module_for_plugins", "complexity": 13, "line": 141}, {"name": "_register_plugin", "complexity": 3, "line": 182}, {"name": "_get_plugin_type", "complexity": 4, "line": 204}, {"name": "initialize_plugins", "complexity": 8, "line": 223}, {"name": "get_plugin", "complexity": 1, "line": 264}, {"name": "get_plugins_by_type", "complexity": 2, "line": 276}, {"name": "get_typed_plugins", "complexity": 1, "line": 291}, {"name": "list_plugins", "complexity": 1, "line": 305}, {"name": "load_plugin", "complexity": 7, "line": 314}, {"name": "install_plugin", "complexity": 3, "line": 356}, {"name": "uninstall_plugin", "complexity": 6, "line": 385}, {"name": "register_tools", "complexity": 3, "line": 421}, {"name": "call_hooks", "complexity": 4, "line": 439}, {"name": "shutdown", "complexity": 3, "line": 463}, {"name": "get_plugin_manager", "complexity": 2, "line": 477}, {"name": "list_plugins", "complexity": 2, "line": 492}, {"name": "load_plugin", "complexity": 1, "line": 514}, {"name": "register_plugin", "complexity": 1, "line": 527}], "complexity_per_line": 0.11588785046728972}, {"file_path": "vibe_check/ai/temporal/temporal_engine.py", "total_complexity": 62, "total_lines": 579, "function_count": 20, "avg_function_complexity": 4.05, "functions": [{"name": "to_dict", "complexity": 1, "line": 57}, {"name": "to_dict", "complexity": 1, "line": 85}, {"name": "to_dict", "complexity": 1, "line": 111}, {"name": "__init__", "complexity": 1, "line": 129}, {"name": "generate_snapshot", "complexity": 1, "line": 132}, {"name": "get_snapshot", "complexity": 1, "line": 179}, {"name": "__init__", "complexity": 1, "line": 187}, {"name": "analyze_trend", "complexity": 7, "line": 194}, {"name": "_linear_regression_trend", "complexity": 7, "line": 228}, {"name": "_moving_average_trend", "complexity": 3, "line": 296}, {"name": "_exponential_smoothing_trend", "complexity": 3, "line": 317}, {"name": "_get_time_delta", "complexity": 6, "line": 340}, {"name": "__init__", "complexity": 1, "line": 359}, {"name": "initialize", "complexity": 2, "line": 366}, {"name": "analyze_evolution", "complexity": 7, "line": 374}, {"name": "_generate_insights", "complexity": 12, "line": 455}, {"name": "_generate_recommendations", "complexity": 12, "line": 492}, {"name": "_identify_risk_factors", "complexity": 9, "line": 519}, {"name": "get_analysis_statistics", "complexity": 4, "line": 537}, {"name": "cleanup", "complexity": 1, "line": 574}], "complexity_per_line": 0.1070811744386874}, {"file_path": "vibe_check/ai/explanation/documentation_generator.py", "total_complexity": 62, "total_lines": 635, "function_count": 23, "avg_function_complexity": 3.652173913043478, "functions": [{"name": "to_dict", "complexity": 1, "line": 44}, {"name": "to_dict", "complexity": 1, "line": 66}, {"name": "get_full_documentation", "complexity": 4, "line": 79}, {"name": "_format_as_markdown", "complexity": 2, "line": 90}, {"name": "_format_as_rst", "complexity": 2, "line": 100}, {"name": "_format_as_html", "complexity": 2, "line": 111}, {"name": "_format_as_plain", "complexity": 2, "line": 120}, {"name": "__init__", "complexity": 1, "line": 134}, {"name": "analyze_code_elements", "complexity": 12, "line": 150}, {"name": "_has_return_statement", "complexity": 4, "line": 218}, {"name": "_get_decorator_name", "complexity": 3, "line": 225}, {"name": "_get_base_name", "complexity": 3, "line": 234}, {"name": "calculate_documentation_completeness", "complexity": 8, "line": 243}, {"name": "__init__", "complexity": 1, "line": 277}, {"name": "initialize", "complexity": 3, "line": 289}, {"name": "generate_documentation", "complexity": 7, "line": 301}, {"name": "_generate_overview_section", "complexity": 7, "line": 401}, {"name": "_generate_functions_section", "complexity": 6, "line": 465}, {"name": "_generate_classes_section", "complexity": 6, "line": 503}, {"name": "_generate_examples_section", "complexity": 3, "line": 540}, {"name": "_calculate_documentation_quality", "complexity": 2, "line": 573}, {"name": "get_documentation_statistics", "complexity": 3, "line": 594}, {"name": "cleanup", "complexity": 1, "line": 631}], "complexity_per_line": 0.09763779527559055}, {"file_path": "vibe_check/core/knowledge/rule_engine.py", "total_complexity": 62, "total_lines": 396, "function_count": 20, "avg_function_complexity": 4.05, "functions": [{"name": "__init__", "complexity": 2, "line": 36}, {"name": "execute_rules", "complexity": 3, "line": 45}, {"name": "_get_applicable_rules", "complexity": 2, "line": 69}, {"name": "_execute_rule", "complexity": 2, "line": 79}, {"name": "_execute_ast_rule", "complexity": 1, "line": 88}, {"name": "_execute_regex_rule", "complexity": 6, "line": 99}, {"name": "_execute_import_rule", "complexity": 5, "line": 132}, {"name": "_execute_file_structure_rule", "complexity": 3, "line": 168}, {"name": "_extract_imports", "complexity": 7, "line": 191}, {"name": "__init__", "complexity": 1, "line": 211}, {"name": "visit_ClassDef", "complexity": 2, "line": 217}, {"name": "visit_FunctionDef", "complexity": 2, "line": 223}, {"name": "visit_Call", "complexity": 2, "line": 229}, {"name": "visit_Assign", "complexity": 2, "line": 235}, {"name": "_check_class_rule", "complexity": 10, "line": 241}, {"name": "_check_function_rule", "complexity": 11, "line": 284}, {"name": "_check_call_rule", "complexity": 4, "line": 327}, {"name": "_check_assign_rule", "complexity": 8, "line": 347}, {"name": "_get_name", "complexity": 4, "line": 375}, {"name": "_get_decorator_name", "complexity": 4, "line": 386}], "complexity_per_line": 0.15656565656565657}], "reductions_applied": ["Added 2 helper functions to scripts/extract_high_priority.py", "Added 1 helper functions to vibe_check/cli/formatters.py", "Added 1 helper functions to examples/pat_caw_actors_prototype.py", "Added 2 helper functions to scripts/generate_issue_prompts.py", "Added 1 helper functions to scripts/generate_similarity_report.py"], "improvement_details": {"old_average_complexity": 24.75874125874126, "new_average_complexity": 24.75874125874126, "improvement": 0.0, "files_processed": 5, "reductions_applied": ["Added 2 helper functions to scripts/extract_high_priority.py", "Added 1 helper functions to vibe_check/cli/formatters.py", "Added 1 helper functions to examples/pat_caw_actors_prototype.py", "Added 2 helper functions to scripts/generate_issue_prompts.py", "Added 1 helper functions to scripts/generate_similarity_report.py"]}}