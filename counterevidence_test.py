#!/usr/bin/env python3
"""
Counterevidence Test
===================

Actively search for evidence that contradicts our success claims:
- Test edge cases that might reveal performance issues
- Check for broken imports or missing dependencies
- Verify that generated HTML dashboards actually work
"""

import sys
import time
import subprocess
from pathlib import Path


def test_edge_case_performance():
    """Test edge cases that might reveal performance issues"""
    print("🧪 Testing Edge Case Performance")
    print("=" * 32)
    
    issues_found = []
    
    try:
        from test_unified_analyzer_simple import SimpleUnifiedAnalyzer
        
        # Test 1: Empty directory
        empty_dir = Path("empty_test_dir")
        empty_dir.mkdir(exist_ok=True)
        
        analyzer = SimpleUnifiedAnalyzer()
        
        import asyncio
        start_time = time.time()
        result = asyncio.run(analyzer.analyze_project(empty_dir))
        empty_time = time.time() - start_time
        
        empty_dir.rmdir()
        
        if empty_time > 1.0:  # Should be very fast for empty directory
            issues_found.append(f"Empty directory analysis too slow: {empty_time:.3f}s")
        else:
            print(f"  ✅ Empty directory handled efficiently: {empty_time:.3f}s")
        
        # Test 2: Large file with high complexity
        large_file_dir = Path("large_file_test")
        large_file_dir.mkdir(exist_ok=True)
        
        # Create a file with very high complexity
        complex_content = '''
def complex_function():
    """Very complex function"""
    result = []
    for i in range(100):
        if i % 2 == 0:
            for j in range(10):
                if j % 3 == 0:
                    while j < 5:
                        try:
                            if i > 50:
                                result.append(i * j)
                            elif i < 25:
                                result.append(i + j)
                            else:
                                result.append(i - j)
                        except Exception as e:
                            if str(e):
                                continue
                            else:
                                break
                        finally:
                            j += 1
                else:
                    with open("test.txt", "w") as f:
                        f.write(str(j))
        else:
            continue
    return result
''' * 10  # Repeat to make it larger
        
        (large_file_dir / "complex.py").write_text(complex_content)
        
        start_time = time.time()
        result = asyncio.run(analyzer.analyze_project(large_file_dir))
        complex_time = time.time() - start_time
        
        import shutil
        shutil.rmtree(large_file_dir, ignore_errors=True)
        
        if complex_time > 5.0:  # Should handle even complex files reasonably fast
            issues_found.append(f"Complex file analysis too slow: {complex_time:.3f}s")
        else:
            print(f"  ✅ Complex file handled efficiently: {complex_time:.3f}s")
        
        # Test 3: Many small files
        many_files_dir = Path("many_files_test")
        many_files_dir.mkdir(exist_ok=True)
        
        for i in range(200):
            (many_files_dir / f"file_{i}.py").write_text(f"def func_{i}(): pass")
        
        start_time = time.time()
        result = asyncio.run(analyzer.analyze_project(many_files_dir))
        many_files_time = time.time() - start_time
        
        shutil.rmtree(many_files_dir, ignore_errors=True)
        
        if many_files_time > 10.0:  # Should handle 200 small files quickly
            issues_found.append(f"Many files analysis too slow: {many_files_time:.3f}s for 200 files")
        else:
            print(f"  ✅ Many files handled efficiently: {many_files_time:.3f}s for 200 files")
        
    except Exception as e:
        issues_found.append(f"Edge case performance test failed: {e}")
    
    return issues_found


def test_broken_imports():
    """Check for broken imports or missing dependencies"""
    print("\n🧪 Testing for Broken Imports")
    print("=" * 30)
    
    issues_found = []
    
    # Test imports that should work
    import_tests = [
        ("vibe_check.core.unified_analyzer", "UnifiedAnalysisEngine"),
        ("vibe_check.core.visualization.unified_charts", "UnifiedChartEngine"),
        ("vibe_check.core.visualization.dashboard_engine", "UnifiedDashboardEngine"),
        ("vibe_check.cli.unified_formatters", "get_formatter"),
    ]
    
    for module_name, class_name in import_tests:
        try:
            module = __import__(module_name, fromlist=[class_name])
            if hasattr(module, class_name):
                print(f"  ✅ {module_name}.{class_name} imports correctly")
            else:
                issues_found.append(f"{module_name} missing {class_name}")
        except ImportError as e:
            issues_found.append(f"Import failed: {module_name} - {e}")
        except Exception as e:
            issues_found.append(f"Import error: {module_name} - {e}")
    
    # Test that old import paths are broken (as expected after consolidation)
    old_import_tests = [
        "vibe_check.ai.visualization.dashboard_engine",
        "vibe_check.ui.visualization.charts",
    ]
    
    for module_name in old_import_tests:
        try:
            __import__(module_name)
            # If this succeeds, consolidation might not be complete
            issues_found.append(f"Old import path still works: {module_name} (consolidation incomplete?)")
        except ImportError:
            print(f"  ✅ Old import path properly removed: {module_name}")
        except Exception as e:
            # Other errors are also fine - the point is the old path shouldn't work
            print(f"  ✅ Old import path inaccessible: {module_name}")
    
    return issues_found


def test_html_dashboard_validity():
    """Verify that generated HTML dashboards actually work"""
    print("\n🧪 Testing HTML Dashboard Validity")
    print("=" * 35)
    
    issues_found = []
    
    try:
        from test_visualization_simple import SimpleDashboardEngine, ChartType
        
        engine = SimpleDashboardEngine()
        dashboard = engine.create_dashboard("validity_test", "Validity Test Dashboard")
        
        # Add various panel types
        engine.add_metric_panel("validity_test", "metric1", "Test Metric", {'width': 200, 'height': 150}, "test_metric")
        engine.add_chart_panel("validity_test", "chart1", "Test Chart", ChartType.LINE, {'width': 400, 'height': 300})
        engine.add_chart_panel("validity_test", "chart2", "Test Bar Chart", ChartType.BAR, {'width': 400, 'height': 300})
        engine.add_chart_panel("validity_test", "chart3", "Test Pie Chart", ChartType.PIE, {'width': 400, 'height': 300})
        
        # Generate HTML
        html = engine.render_dashboard_html("validity_test", {
            'metric1': {'value': 42},
            'chart1': {'x': [1, 2, 3, 4], 'y': [10, 20, 15, 25]},
            'chart2': {'x': ['A', 'B', 'C'], 'y': [30, 40, 35]},
            'chart3': {'x': ['X', 'Y', 'Z'], 'y': [25, 35, 40]}
        })
        
        # Basic HTML validation
        html_checks = [
            ('<!DOCTYPE html>', 'Missing DOCTYPE'),
            ('<html>', 'Missing HTML tag'),
            ('<head>', 'Missing HEAD tag'),
            ('<body>', 'Missing BODY tag'),
            ('chart.js', 'Missing Chart.js library'),
            ('Validity Test Dashboard', 'Missing dashboard title'),
            ('Test Metric', 'Missing metric panel'),
            ('Test Chart', 'Missing chart panel'),
        ]
        
        for check, error_msg in html_checks:
            if check not in html:
                issues_found.append(f"HTML validation failed: {error_msg}")
        
        if not issues_found:
            print("  ✅ HTML structure validation passed")
        
        # Save for manual inspection
        html_file = Path("validity_test_dashboard.html")
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html)
        
        print(f"  ✅ HTML dashboard saved for inspection: {html_file}")
        
        # Check file size (should be substantial)
        if len(html) < 1000:
            issues_found.append(f"Generated HTML too small: {len(html)} characters")
        else:
            print(f"  ✅ HTML size reasonable: {len(html)} characters")
        
        # Check for JavaScript errors (basic)
        js_checks = [
            'new Chart(',
            'getContext(',
            'getElementById(',
        ]
        
        for js_check in js_checks:
            if js_check not in html:
                issues_found.append(f"Missing JavaScript component: {js_check}")
        
        if not any(js_check not in html for js_check in js_checks):
            print("  ✅ JavaScript components present")
        
    except Exception as e:
        issues_found.append(f"HTML dashboard test failed: {e}")
    
    return issues_found


def test_memory_leaks():
    """Test for potential memory leaks"""
    print("\n🧪 Testing for Memory Leaks")
    print("=" * 27)
    
    issues_found = []
    
    try:
        import psutil
        process = psutil.Process()
        
        # Initial memory
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        from test_unified_analyzer_simple import SimpleUnifiedAnalyzer
        from test_visualization_simple import SimpleDashboardEngine, ChartType
        
        # Create and destroy many objects
        for i in range(100):
            # Analysis objects
            analyzer = SimpleUnifiedAnalyzer()
            
            # Visualization objects
            engine = SimpleDashboardEngine()
            dashboard = engine.create_dashboard(f"leak_test_{i}", f"Test {i}")
            engine.add_metric_panel(f"leak_test_{i}", "metric", "Metric", {'width': 200, 'height': 150}, "metric")
            
            # Force some processing
            html = engine.render_dashboard_html(f"leak_test_{i}")
            
            # Delete references
            del analyzer
            del engine
            del dashboard
            del html
        
        # Final memory
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"  📊 Memory usage:")
        print(f"    • Initial: {initial_memory:.1f} MB")
        print(f"    • Final: {final_memory:.1f} MB")
        print(f"    • Increase: {memory_increase:.1f} MB")
        
        # Check for excessive memory growth
        if memory_increase > 50:  # More than 50MB increase is concerning
            issues_found.append(f"Potential memory leak: {memory_increase:.1f} MB increase")
        else:
            print(f"  ✅ Memory usage reasonable")
        
    except Exception as e:
        issues_found.append(f"Memory leak test failed: {e}")
    
    return issues_found


def test_concurrent_access():
    """Test concurrent access patterns"""
    print("\n🧪 Testing Concurrent Access")
    print("=" * 27)
    
    issues_found = []
    
    try:
        import threading
        import queue
        
        from test_unified_analyzer_simple import SimpleUnifiedAnalyzer
        
        # Test concurrent analysis
        results_queue = queue.Queue()
        errors_queue = queue.Queue()
        
        def worker():
            try:
                analyzer = SimpleUnifiedAnalyzer(max_workers=1, use_async=False)
                
                # Create small test
                test_dir = Path(f"concurrent_test_{threading.current_thread().ident}")
                test_dir.mkdir(exist_ok=True)
                (test_dir / "test.py").write_text("def test(): pass")
                
                import asyncio
                result = asyncio.run(analyzer.analyze_project(test_dir))
                
                results_queue.put(result.files_analyzed)
                
                # Cleanup
                import shutil
                shutil.rmtree(test_dir, ignore_errors=True)
                
            except Exception as e:
                errors_queue.put(str(e))
        
        # Start multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join(timeout=10)
        
        # Check results
        results = []
        while not results_queue.empty():
            results.append(results_queue.get())
        
        errors = []
        while not errors_queue.empty():
            errors.append(errors_queue.get())
        
        if errors:
            issues_found.extend([f"Concurrent access error: {error}" for error in errors])
        else:
            print(f"  ✅ Concurrent access handled: {len(results)} successful operations")
        
    except Exception as e:
        issues_found.append(f"Concurrent access test failed: {e}")
    
    return issues_found


def main():
    """Main counterevidence test function"""
    print("🔍 Counterevidence Search for Week 2 Claims")
    print("=" * 50)
    
    all_issues = []
    
    # Run all counterevidence tests
    all_issues.extend(test_edge_case_performance())
    all_issues.extend(test_broken_imports())
    all_issues.extend(test_html_dashboard_validity())
    all_issues.extend(test_memory_leaks())
    all_issues.extend(test_concurrent_access())
    
    print("\n" + "=" * 50)
    print("🔍 COUNTEREVIDENCE SUMMARY")
    print("=" * 50)
    
    if all_issues:
        print(f"❌ Found {len(all_issues)} potential issues:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        
        print(f"\n⚠️  Week 2 claims need verification")
        print(f"🔧 Issues should be addressed before proceeding")
        return 1
    else:
        print("✅ No significant issues found")
        print("🎉 Week 2 consolidation claims appear valid")
        print("🚀 Counterevidence search supports proceeding to Week 3")
        return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
