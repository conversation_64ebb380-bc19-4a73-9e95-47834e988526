#!/usr/bin/env python3
"""
Debug Moving Average Detection
==============================

Debug the moving average anomaly detection.
"""

import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def main():
    """Debug moving average detection"""
    from vibe_check.monitoring.anomaly import MovingAverageDetector
    
    # Generate test data exactly like in the test
    base_data = [50] * 30  # Stable baseline
    anomaly_data = base_data + [150] + [50] * 10  # Spike at position 30
    
    print(f"Data length: {len(anomaly_data)}")
    print(f"Data: {anomaly_data}")
    print(f"Anomaly at position 30: {anomaly_data[30]}")
    
    # Test with different window sizes
    for window in [10, 15, 20]:
        print(f"\n=== Testing with window size {window} ===")
        
        anomalies = MovingAverageDetector.simple_moving_average(
            anomaly_data, window=window, threshold=2.0
        )
        
        print(f"Anomalies found: {len(anomalies)}")
        for idx, deviation in anomalies:
            print(f"  Position {idx}: deviation {deviation:.2f}, value {anomaly_data[idx]}")
        
        # Manual calculation for position 30
        if len(anomaly_data) > 30 and 30 >= window:
            window_values = anomaly_data[30-window:30]
            current_value = anomaly_data[30]
            
            import statistics
            ma = statistics.mean(window_values)
            std = statistics.stdev(window_values) if len(window_values) > 1 else 0
            
            print(f"  Manual check for position 30:")
            print(f"    Window values: {window_values}")
            print(f"    Moving average: {ma:.2f}")
            print(f"    Standard deviation: {std:.2f}")
            print(f"    Current value: {current_value}")
            if std > 0:
                deviation = abs(current_value - ma) / std
                print(f"    Deviation: {deviation:.2f}")
                print(f"    Threshold: 2.0")
                print(f"    Anomaly: {'Yes' if deviation > 2.0 else 'No'}")

if __name__ == "__main__":
    main()
