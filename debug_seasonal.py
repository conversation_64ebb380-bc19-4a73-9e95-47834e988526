#!/usr/bin/env python3
"""
Debug Seasonal Detection
=========================

Debug the seasonal anomaly detection.
"""

import time
import math
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def main():
    """Debug seasonal detection"""
    from vibe_check.monitoring.anomaly import SeasonalDetector
    
    # Generate seasonal data (24-hour pattern) exactly like in the test
    timestamps = []
    values = []
    base_time = time.time()
    
    for i in range(48):  # 2 days of hourly data
        timestamp = base_time + i * 3600  # Hourly intervals
        hour = i % 24
        
        # Normal seasonal pattern (higher during day, lower at night)
        seasonal_value = 50 + 30 * math.sin(2 * math.pi * hour / 24)
        
        # Inject anomaly at hour 12 of second day
        if i == 36:  # Hour 12 of second day
            seasonal_value += 100  # Anomaly
        
        timestamps.append(timestamp)
        values.append(seasonal_value)
    
    print(f"Data points: {len(values)}")
    print(f"Anomaly injected at index 36: {values[36]:.1f}")
    
    # Show some sample values
    print("\nSample values:")
    for i in [0, 12, 24, 36, 48-1]:
        if i < len(values):
            hour = i % 24
            print(f"  Index {i} (hour {hour}): {values[i]:.1f}")
    
    # Test seasonal detection
    print("\n=== Testing Seasonal Detection ===")
    seasonal_anomalies = SeasonalDetector.seasonal_decomposition(
        values, timestamps, period=24, threshold=2.0
    )
    
    print(f"Anomalies found: {len(seasonal_anomalies)}")
    for idx, confidence in seasonal_anomalies:
        print(f"  Position {idx}: confidence {confidence:.2f}, value {values[idx]:.1f}")
    
    # Manual check - group by hour
    print("\n=== Manual Seasonal Grouping ===")
    seasonal_groups = {}
    for i, (value, timestamp) in enumerate(zip(values, timestamps)):
        seasonal_pos = int((timestamp % (24 * 3600)) // 3600)  # Hour of day
        index_based_hour = i % 24  # Alternative: index-based hour
        print(f"Index {i}: timestamp_hour={seasonal_pos}, index_hour={index_based_hour}, value={value:.1f}")
        if seasonal_pos not in seasonal_groups:
            seasonal_groups[seasonal_pos] = []
        seasonal_groups[seasonal_pos].append((i, value))
    
    # Check hour 12 specifically (where anomaly should be)
    hour_12_data = seasonal_groups.get(12, [])
    print(f"Hour 12 data points: {len(hour_12_data)}")
    for idx, value in hour_12_data:
        print(f"  Index {idx}: {value:.1f}")
    
    if len(hour_12_data) >= 2:
        values_only = [v for _, v in hour_12_data]
        import statistics
        mean_val = statistics.mean(values_only)
        std_val = statistics.stdev(values_only) if len(values_only) > 1 else 0
        print(f"  Mean: {mean_val:.1f}, Std: {std_val:.1f}")
        
        for idx, value in hour_12_data:
            if std_val > 0:
                z_score = abs((value - mean_val) / std_val)
                print(f"  Index {idx}: z_score = {z_score:.2f}")

if __name__ == "__main__":
    main()
