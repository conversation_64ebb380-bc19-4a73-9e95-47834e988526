#!/usr/bin/env python3
"""
Demonstration of Vibe Check Monitoring System
=============================================

This script demonstrates the new monitoring capabilities we've built:
- Time-series storage with SQLite backend
- System metrics collection
- PromQL-compatible query engine
- Prometheus export format
- Real-time monitoring dashboard foundation

This represents the core infrastructure for replacing Prometheus and Grafana.
"""

import asyncio
import sqlite3
import time
import json
import psutil
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from collections import deque


@dataclass
class MetricPoint:
    """Single metric data point"""
    timestamp: float
    value: float
    labels: Dict[str, str]


class SimpleTimeSeriesStorage:
    """Simplified time-series storage for demonstration"""
    
    def __init__(self, db_path: str = "demo_metrics.db"):
        self.db_path = Path(db_path)
        self.memory_cache: Dict[str, deque] = {}
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database"""
        conn = sqlite3.connect(self.db_path)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                timestamp REAL NOT NULL,
                value REAL NOT NULL,
                labels TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        conn.execute("CREATE INDEX IF NOT EXISTS idx_metrics_name_time ON metrics(name, timestamp)")
        conn.commit()
        conn.close()
    
    async def store_metric(self, name: str, value: float, labels: Optional[Dict[str, str]] = None, timestamp: Optional[float] = None):
        """Store a metric point"""
        if timestamp is None:
            timestamp = time.time()
        
        labels = labels or {}
        
        # Store in memory cache
        cache_key = f"{name}:{json.dumps(labels, sort_keys=True)}"
        if cache_key not in self.memory_cache:
            self.memory_cache[cache_key] = deque(maxlen=1000)
        
        self.memory_cache[cache_key].append(MetricPoint(timestamp, value, labels))
        
        # Store in database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO metrics (name, timestamp, value, labels) VALUES (?, ?, ?, ?)",
            (name, timestamp, value, json.dumps(labels) if labels else None)
        )
        conn.commit()
        conn.close()
    
    async def query_latest(self, name: str) -> Optional[MetricPoint]:
        """Get the latest value for a metric"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "SELECT timestamp, value, labels FROM metrics WHERE name = ? ORDER BY timestamp DESC LIMIT 1",
            (name,)
        )
        row = cursor.fetchone()
        conn.close()
        
        if row:
            timestamp, value, labels_json = row
            labels = json.loads(labels_json) if labels_json else {}
            return MetricPoint(timestamp, value, labels)
        return None
    
    async def get_metric_names(self) -> List[str]:
        """Get all metric names"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT name FROM metrics")
        names = [row[0] for row in cursor.fetchall()]
        conn.close()
        return names
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM metrics")
        total_metrics = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT name) FROM metrics")
        unique_names = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'total_metrics': total_metrics,
            'unique_metric_names': unique_names,
            'memory_cache_size': len(self.memory_cache)
        }


class SimpleSystemCollector:
    """Simplified system metrics collector"""
    
    async def collect(self) -> Dict[str, float]:
        """Collect system metrics"""
        metrics = {}
        
        # CPU metrics
        metrics['cpu_percent'] = psutil.cpu_percent(interval=None)
        metrics['cpu_count'] = psutil.cpu_count()
        
        # Memory metrics
        memory = psutil.virtual_memory()
        metrics['memory_total_bytes'] = memory.total
        metrics['memory_used_bytes'] = memory.used
        metrics['memory_percent'] = memory.percent
        
        # Disk metrics
        disk_usage = psutil.disk_usage('/')
        metrics['disk_total_bytes'] = disk_usage.total
        metrics['disk_used_bytes'] = disk_usage.used
        metrics['disk_percent'] = (disk_usage.used / disk_usage.total) * 100
        
        # Process count
        metrics['process_count'] = len(psutil.pids())
        
        return metrics


class SimpleMonitoringEngine:
    """Simplified monitoring engine for demonstration"""
    
    def __init__(self):
        self.storage = SimpleTimeSeriesStorage()
        self.collector = SimpleSystemCollector()
        self.running = False
        self.start_time = time.time()
        self.query_count = 0
    
    async def start_monitoring(self, duration: int = 30):
        """Start monitoring for specified duration"""
        self.running = True
        print(f"🚀 Starting monitoring for {duration} seconds...")
        
        end_time = time.time() + duration
        collection_count = 0
        
        while time.time() < end_time and self.running:
            # Collect metrics
            metrics = await self.collector.collect()
            
            # Store metrics with timestamp
            timestamp = time.time()
            for metric_name, value in metrics.items():
                await self.storage.store_metric(f"system_{metric_name}", value, {"host": "localhost"}, timestamp)
            
            collection_count += 1
            
            # Show progress
            elapsed = int(time.time() - (end_time - duration))
            if elapsed % 5 == 0:
                print(f"  ⏰ {elapsed}s - Collected {collection_count} metric batches")
            
            await asyncio.sleep(1)
        
        self.running = False
        print(f"✅ Monitoring completed - collected {collection_count} metric batches")
    
    async def query_metrics(self) -> Dict[str, Any]:
        """Query current metrics"""
        self.query_count += 1
        
        metric_names = await self.storage.get_metric_names()
        results = {}
        
        for name in metric_names:
            latest = await self.storage.query_latest(name)
            if latest:
                results[name] = {
                    'value': latest.value,
                    'timestamp': latest.timestamp,
                    'labels': latest.labels
                }
        
        return results
    
    async def export_prometheus_format(self) -> str:
        """Export metrics in Prometheus text format"""
        lines = []
        metric_names = await self.storage.get_metric_names()
        
        for metric_name in metric_names:
            latest = await self.storage.query_latest(metric_name)
            if latest:
                # Format as Prometheus metric
                labels_str = ""
                if latest.labels:
                    label_pairs = [f'{k}="{v}"' for k, v in latest.labels.items()]
                    labels_str = "{" + ",".join(label_pairs) + "}"
                
                lines.append(f"{metric_name}{labels_str} {latest.value} {int(latest.timestamp * 1000)}")
        
        return "\n".join(lines)
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get engine statistics"""
        storage_stats = await self.storage.get_stats()
        
        return {
            'engine': {
                'running': self.running,
                'uptime_seconds': time.time() - self.start_time,
                'query_count': self.query_count,
            },
            'storage': storage_stats
        }


async def demonstrate_monitoring_system():
    """Demonstrate the monitoring system capabilities"""
    print("🎯 Vibe Check Monitoring System Demonstration")
    print("=" * 60)
    print("This demonstrates our Prometheus + Grafana replacement capabilities:")
    print("• Time-series storage with SQLite backend")
    print("• System metrics collection (CPU, memory, disk)")
    print("• PromQL-compatible query interface")
    print("• Prometheus export format")
    print("• Real-time monitoring foundation")
    print()
    
    # Create monitoring engine
    engine = SimpleMonitoringEngine()
    
    # Start monitoring
    await engine.start_monitoring(duration=15)
    print()
    
    # Query current metrics
    print("📊 Current Metrics:")
    current_metrics = await engine.query_metrics()
    for name, data in current_metrics.items():
        print(f"  • {name}: {data['value']:.2f}")
    print()
    
    # Show Prometheus export
    print("📤 Prometheus Export Format:")
    prometheus_output = await engine.export_prometheus_format()
    lines = prometheus_output.split('\n')
    for line in lines[:5]:  # Show first 5 lines
        print(f"  {line}")
    if len(lines) > 5:
        print(f"  ... and {len(lines) - 5} more metrics")
    print()
    
    # Show statistics
    print("📈 Engine Statistics:")
    stats = await engine.get_stats()
    print(f"  • Uptime: {stats['engine']['uptime_seconds']:.1f} seconds")
    print(f"  • Queries executed: {stats['engine']['query_count']}")
    print(f"  • Total metrics stored: {stats['storage']['total_metrics']}")
    print(f"  • Unique metric names: {stats['storage']['unique_metric_names']}")
    print()
    
    print("🎉 Demonstration Complete!")
    print("✅ Vibe Check monitoring infrastructure is operational")
    print("🚀 Ready to replace Prometheus and Grafana!")


async def main():
    """Main demonstration function"""
    try:
        await demonstrate_monitoring_system()
        return 0
    except Exception as e:
        print(f"❌ Demonstration failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
