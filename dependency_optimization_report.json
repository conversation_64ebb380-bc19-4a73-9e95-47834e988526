{"optimizations_applied": ["Removed legacy directory: legacy", "Fixed syntax in: vibe_check/cli/__init__.py", "Fixed syntax in: vibe_check/core/monitoring/__init__.py", "Created consolidated utilities module", "Removed empty directory: vibe_check_output/state", "Removed empty directory: tests/unit/ui/reporting", "Removed empty directory: tests/unit/core/orchestration", "Removed empty directory: tests/tests/unit/core", "Removed empty directory: tests/tests/unit", "Removed empty directory: tests/vibe_check/core", "Removed empty directory: tests/vibe_check_output", "Removed empty directory: tests/core", "Removed empty directory: tests/tests", "Removed empty directory: tests/vibe_check", "Removed empty directory: tests/functional", "Removed empty directory: test_actor_system_output/state", "Removed empty directory: .mypy_cache/3.8/vibe_check/ui/visualization", "Removed empty directory: .mypy_cache/3.8/vibe_check/ui/tui", "Removed empty directory: .mypy_cache/3.8/vibe_check/ui/web", "Removed empty directory: .mypy_cache/3.8/vibe_check/ui/gui", "Removed empty directory: .mypy_cache/3.8/vibe_check/core/trend_analysis", "Removed empty directory: .mypy_cache/3.8/vibe_check/core/knowledge", "Removed empty directory: .mypy_cache/3.8/vibe_check/enterprise/security", "Removed empty directory: .mypy_cache/3.8/vibe_check/ai/visualization", "Removed empty directory: .mypy_cache/3.8/vibe_check/ai/temporal", "Removed empty directory: .mypy_cache/3.8/vibe_check/ai/explanation", "Removed empty directory: .mypy_cache/3.8/vibe_check/ai/refactoring", "Removed empty directory: .mypy_cache/3.8/vibe_check/ai/infrastructure", "Removed empty directory: .mypy_cache/3.8/vibe_check/ai", "Removed empty directory: vibe_check/enterprise/reporting/templates", "Removed empty directory: vibe_check/enterprise/dashboard/static/images", "Removed empty directory: vibe_check/enterprise/dashboard/templates", "Removed empty directory: test_actor_output/state", "Removed empty directory: test_projects/test_output/state", "Removed empty directory: test_projects/test_output2/state", "Removed empty directory: test_projects/test_output2", "Removed empty directory: debug_vcs_test", "Removed empty directory: vibe_check_output", "Removed empty directory: vcs_standalone_demo", "Removed empty directory: enterprise_analysis", "Removed empty directory: debug_vcs_test_fixed", "Removed empty directory: vcs_standalone_final", "Removed empty directory: vcs_enterprise_demo", "Removed empty directory: vcs_advanced_analysis", "Removed empty directory: .benchmarks", "Removed empty directory: test_actor_system_output", "Removed empty directory: output_dir", "Removed empty directory: large_project_analysis", "Removed empty directory: vcs_output", "Removed empty directory: test_actor_output"], "total_optimizations": 50, "timestamp": 1750875328.9528909}