{"stats": {"files_processed": 270, "imports_removed": 14, "files_modified": 9, "errors": ["Error analyzing vibe_check/enterprise/collaboration/__init__.py: unexpected indent (<unknown>, line 11)", "Error analyzing vibe_check/enterprise/reporting/__init__.py: unexpected indent (<unknown>, line 9)", "Error analyzing vibe_check/enterprise/cicd/gitlab_ci.py: unexpected indent (<unknown>, line 13)", "Error analyzing vibe_check/enterprise/cicd/__init__.py: unexpected indent (<unknown>, line 9)", "Error analyzing vibe_check/enterprise/cicd/github_actions.py: unexpected indent (<unknown>, line 13)", "Error analyzing vibe_check/enterprise/cicd/azure_devops.py: unexpected indent (<unknown>, line 13)", "Error analyzing vibe_check/enterprise/cicd/jenkins.py: unexpected indent (<unknown>, line 13)", "Error analyzing vibe_check/enterprise/dashboard/__init__.py: unexpected indent (<unknown>, line 9)", "Error analyzing vibe_check/enterprise/api/rest.py: unexpected indent (<unknown>, line 13)", "Error analyzing vibe_check/enterprise/api/__init__.py: unexpected indent (<unknown>, line 9)", "Error analyzing vibe_check/core/utils/__init__.py: unexpected indent (<unknown>, line 19)", "Error analyzing vibe_check/core/docs/__init__.py: unexpected indent (<unknown>, line 10)", "Error analyzing vibe_check/core/error_handling/__init__.py: unexpected indent (<unknown>, line 10)", "Error analyzing vibe_check/core/vcs/engine.py: unexpected indent (<unknown>, line 13)", "Error analyzing vibe_check/core/logging/__init__.py: unexpected indent (<unknown>, line 9)", "Error analyzing vibe_check/core/analysis/visualization/__init__.py: unexpected indent (<unknown>, line 8)", "Error analyzing vibe_check/ui/visualization/__init__.py: unexpected indent (<unknown>, line 8)", "Error analyzing vibe_check/ui/reporting/__init__.py: unexpected indent (<unknown>, line 8)", "Error analyzing vibe_check/ui/tui/components.py: expected an indented block after 'try' statement on line 10 (<unknown>, line 11)", "Error analyzing vibe_check/ui/web/run_web_ui.py: expected an indented block after 'try' statement on line 25 (<unknown>, line 26)", "Error analyzing dependencies in scripts/simple_meta_system_check.py: invalid syntax. Perhaps you forgot a comma? (<unknown>, line 500)", "Error analyzing dependencies in vibe_check/__init__.py: unexpected indent (<unknown>, line 23)", "Error analyzing dependencies in venv/lib/python3.13/site-packages/joblib/test/test_func_inspect_special_encoding.py: 'utf-8' codec can't decode byte 0xa4 in position 64: invalid start byte", "Error analyzing dependencies in vibe_check/core/__init__.py: unexpected indent (<unknown>, line 22)", "Error analyzing dependencies in vibe_check/cli/monitor.py: unexpected indent (<unknown>, line 15)", "Error analyzing dependencies in vibe_check/cli/knowledge_manager.py: unexpected indent (<unknown>, line 11)", "Error analyzing dependencies in vibe_check/enterprise/collaboration/__init__.py: unexpected indent (<unknown>, line 11)", "Error analyzing dependencies in vibe_check/enterprise/reporting/__init__.py: unexpected indent (<unknown>, line 9)", "Error analyzing dependencies in vibe_check/enterprise/cicd/gitlab_ci.py: unexpected indent (<unknown>, line 13)", "Error analyzing dependencies in vibe_check/enterprise/cicd/__init__.py: unexpected indent (<unknown>, line 9)", "Error analyzing dependencies in vibe_check/enterprise/cicd/github_actions.py: unexpected indent (<unknown>, line 13)", "Error analyzing dependencies in vibe_check/enterprise/cicd/azure_devops.py: unexpected indent (<unknown>, line 13)", "Error analyzing dependencies in vibe_check/enterprise/cicd/jenkins.py: unexpected indent (<unknown>, line 13)", "Error analyzing dependencies in vibe_check/enterprise/dashboard/__init__.py: unexpected indent (<unknown>, line 9)", "Error analyzing dependencies in vibe_check/enterprise/api/rest.py: unexpected indent (<unknown>, line 13)", "Error analyzing dependencies in vibe_check/enterprise/api/__init__.py: unexpected indent (<unknown>, line 9)", "Error analyzing dependencies in vibe_check/enterprise/monitoring/__init__.py: unexpected indent (<unknown>, line 9)", "Error analyzing dependencies in vibe_check/core/utils/__init__.py: unexpected indent (<unknown>, line 19)", "Error analyzing dependencies in vibe_check/core/docs/__init__.py: unexpected indent (<unknown>, line 10)", "Error analyzing dependencies in vibe_check/core/error_handling/__init__.py: unexpected indent (<unknown>, line 10)", "Error analyzing dependencies in vibe_check/core/vcs/engine.py: unexpected indent (<unknown>, line 13)", "Error analyzing dependencies in vibe_check/core/logging/__init__.py: unexpected indent (<unknown>, line 9)", "Error analyzing dependencies in vibe_check/core/analysis/visualization/__init__.py: unexpected indent (<unknown>, line 8)", "Error analyzing dependencies in vibe_check/ui/visualization/__init__.py: unexpected indent (<unknown>, line 8)", "Error analyzing dependencies in vibe_check/ui/reporting/__init__.py: unexpected indent (<unknown>, line 8)", "Error analyzing dependencies in vibe_check/ui/tui/components.py: expected an indented block after 'try' statement on line 10 (<unknown>, line 11)", "Error analyzing dependencies in vibe_check/ui/web/run_web_ui.py: expected an indented block after 'try' statement on line 25 (<unknown>, line 26)", "Error analyzing dependencies in .venv/lib/python3.13/site-packages/joblib/test/test_func_inspect_special_encoding.py: 'utf-8' codec can't decode byte 0xa4 in position 64: invalid start byte", "Error analyzing dependencies in legacy/scripts/simple_meta_system_check.py: invalid syntax. Perhaps you forgot a comma? (<unknown>, line 500)", "Error analyzing dependencies in legacy/meta_system_analysis/visualizer/visualizer.py: f-string: expecting a valid expression after '{' (<unknown>, line 362)"]}, "dependency_usage": {"asyncio": 230, "time": 642, "json": 614, "sqlite3": 23, "threading": 333, "psutil": 90, "aiofiles": 2, "weakref": 207, "typing": 7262, "dataclasses": 690, "datetime": 1210, "collections": 1193, "aiohttp": 3, "enum": 452, "pathlib": 1317, "vibe_check_monitoring_engine": 1, "os": 2544, "hashlib": 179, "pytest": 3274, "sys": 4683, "tempfile": 375, "shutil": 360, "unittest.mock": 122, "vibe_check.core.vcs.engine": 8, "vibe_check.core.vcs.models": 31, "vibe_check.core.vcs.config": 6, "vibe_check.core.vcs.rules.rule_loader": 2, "vibe_check.core.vcs.registry": 10, "vibe_check.core.models": 13, "vibe_check.core.simple_analyzer": 9, "vibe_check": 12, "vibe_check.core.analysis": 2, "vibe_check.tools.runners": 1, "vibe_check.tools.parsers": 1, "vibe_check.cli.main": 9, "subprocess": 582, "argparse": 261, "streamlit": 244, "pat_webui.app": 1, "traceback": 258, "PAT_tool": 3, "platform": 356, "main": 48, "PAT_tool.main": 2, "logging": 1156, "pat_plugins.analyzers": 1, "pat_plugins.core": 14, "pat_plugins.performance": 1, "pat_synergy": 1, "multiprocessing": 88, "pat_webui": 3, "matplotlib.pyplot": 203, "pat_core.report_generator": 1, "matplotlib.patches": 106, "pat_plugins.analyzers.codebase_graph_analyzer": 1, "pat_plugins.analyzers.effect_system_analyzer": 3, "math": 535, "re": 2071, "statistics": 7, "shlex": 57, "textual.app": 49, "textual.containers": 17, "textual.reactive": 41, "textual.screen": 14, "textual.scroll_view": 8, "textual.widgets": 22, "queue": 38, "difflib": 33, "sklearn.feature_extraction.text": 4, "sklearn.metrics.pairwise": 4, "importlib": 216, "ast": 276, "graphviz": 20, "webbrowser": 28, "meta_system_analysis": 2, "pyparsing": 24, "matplotlib": 615, "verify_phase_0_completion": 1, "PAT_tool.meta_system_analyzer": 4, "PAT_tool.meta_system_visualizer": 2, "PAT_tool.models": 78, "PAT_tool.utils": 68, "meta_system_analyzer": 4, "meta_system_visualizer": 4, "models": 155, "utils": 297, "pat_project_analysis": 2, "cli.main": 1, "random": 231, "vibe_check.core.actor_system.logging.enhanced_logger": 7, "vibe_check.core.actor_system.actor_system": 9, "unittest": 362, "src.utils": 1, "src.calculator": 1, "calculator": 1, "gc": 123, "uuid": 141, "warnings": 1371, "werkzeug.serving": 4, "flask": 58, "contextlib": 769, "markupsafe": 22, "werkzeug.exceptions": 13, "werkzeug.http": 6, "werkzeug.routing": 7, "flask.debughelpers": 1, "_pytest": 43, "flask.globals": 5, "io": 1005, "flask.logging": 1, "__future__": 4231, "flask.testing": 3, "flask.views": 4, "flask.sessions": 2, "site_package": 1, "config_module_app": 1, "config_package_app": 1, "namespace.package2": 1, "site_app": 1, "installed_package": 1, "flask.json.tag": 1, "greenlet": 3, "jinja2": 17, "blueprintapp": 2, "flask.helpers": 1, "decimal": 189, "codecs": 123, "flask.json.provider": 1, "importlib.metadata": 96, "ssl": 157, "types": 796, "click": 82, "dotenv": 4, "functools": 1431, "_pytest.monkeypatch": 9, "click.testing": 12, "flask.cli": 2, "cliapp.app": 1, "flask.json": 1, "packaging.version": 79, "pallets_sphinx_themes": 1, "docutils.nodes": 3, "docutils.parsers.rst.roles": 1, "werkzeug.local": 8, "globals": 24, "sansio.app": 6, "blinker": 9, "collections.abc": 1401, "typing_extensions": 788, "itsdangerous": 1, "werkzeug.datastructures": 3, "json.tag": 1, "app": 10, "wrappers": 11, "errno": 141, "werkzeug.utils": 10, "helpers": 14, "signals": 17, "sansio.scaffold": 4, "debughelpers": 3, "contextvars": 30, "ctx": 4, "sessions": 8, "blueprints": 3, "config": 36, "templating": 4, "cli": 21, "sansio.blueprints": 1, "inspect": 566, "importlib_metadata": 14, "cryptography": 59, "code": 9, "readline": 4, "operator": 540, "click.core": 8, "werkzeug": 1, "_typeshed.wsgi": 9, "rlcompleter": 1, "werkzeug.wrappers": 5, "itertools": 1051, "urllib.parse": 142, "werkzeug.wsgi": 1, "testing": 6, "asgiref.sync": 1, "jinja2.loaders": 1, "werkzeug.sansio.response": 3, "werkzeug.test": 2, "copy": 1409, "importlib.util": 90, "scaffold": 2, "json.provider": 1, "provider": 1, "base64": 135, "task_app": 1, "js_example": 2, "celery": 2, "celery.result": 1, "flaskr": 2, "flaskr.db": 4, "werkzeug.security": 1, "db": 2, "auth": 20, "http": 11, "flask.templating": 1, "flask.wrappers": 3, "blueprintapp.apps.admin": 1, "blueprintapp.apps.frontend": 1, "hello": 1, "matplotlib.pylab": 4, "grequests": 1, "optparse": 79, "tokenize": 84, "flake8.util": 1, "bisect": 49, "configparser": 89, "keyword": 38, "signal": 84, "fnmatch": 70, "struct": 265, "StringIO": 17, "stat": 103, "tarfile": 56, "glob": 114, "sysconfig": 127, "zipfile": 112, "urllib2": 9, "httplib": 4, "urllib.request": 88, "pipes": 1, "ConfigParser": 19, "_hypothesis_globals": 3, "_pytest.junitxml": 1, "hypothesis.reporting": 7, "hypothesis.configuration": 8, "hypothesis": 89, "hypothesis.errors": 46, "hypothesis.internal.escalation": 7, "hypothesis.internal.healthcheck": 4, "hypothesis.statistics": 2, "hypothesis.extra._patching": 2, "hypothesis.internal.observability": 5, "mypy_extensions": 34, "_pytest._py.error": 2, "_pytest._py.path": 2, "abc": 474, "builtins": 84, "asyncio.coroutines": 3, "_socket": 10, "astroid": 166, "astroid.typing": 35, "astroid.context": 30, "astroid.const": 21, "astroid.exceptions": 35, "astroid.nodes": 21, "astroid.bases": 8, "astroid.util": 11, "importlib.machinery": 52, "astroid.interpreter._import": 3, "astroid._backport_stdlib_names": 1, "astroid.__pkginfo__": 1, "astroid.brain.helpers": 37, "astroid.builder": 41, "astroid.inference_tip": 9, "astroid.objects": 4, "astroid.astroid_manager": 1, "astroid.manager": 61, "astroid.brain.brain_builtin_inference": 1, "textwrap": 538, "astroid._ast": 2, "pprint": 97, "astroid.nodes.node_classes": 15, "token": 41, "astroid.nodes.utils": 4, "astroid.interpreter": 5, "zipimport": 16, "astroid.modutils": 4, "astroid.transforms": 1, "astroid.interpreter._import.spec": 1, "astroid.interpreter.objectmodel": 2, "astroid.nodes._base_nodes": 4, "astroid.nodes.scoped_nodes": 11, "astroid.constraint": 2, "ipaddress": 46, "numbers": 172, "marshmallow": 13, "marshmallow.base": 2, "marshmallow.exceptions": 14, "marshmallow.utils": 4, "marshmallow.validate": 2, "marshmallow.warnings": 4, "marshmallow.schema": 4, "marshmallow.decorators": 4, "email.utils": 20, "marshmallow.fields": 5, "marshmallow.error_store": 2, "marshmallow.orderedset": 2, "mdurl._decode": 2, "mdurl._encode": 2, "mdurl._format": 2, "mdurl._parse": 2, "mdurl._url": 6, "string": 164, "_elffile": 16, "email.feedparser": 6, "email.header": 8, "email.message": 43, "email.parser": 31, "email.policy": 23, "licenses": 6, "_structures": 8, "_tokenizer": 24, "tags": 10, "version": 65, "_parser": 23, "markers": 14, "specifiers": 24, "ctypes": 148, "_manylinux": 8, "parser": 24, "tomllib": 38, "tomli": 30, "pipenv.project": 2, "yaml": 46, "packaging.requirements": 33, "packaging.specifiers": 33, "dependencies": 2, "errors": 201, "regex": 19, "poetry.packages.locker": 2, "certifi": 10, "trio": 10, "anyio": 8, "sniffio": 14, "_exceptions": 66, "_models": 61, "_sync.connection_pool": 2, "_api": 16, "_async": 2, "_backends.base": 26, "_backends.mock": 2, "_backends.sync": 8, "_ssl": 20, "_sync": 2, "_backends.anyio": 2, "_backends.trio": 2, "select": 16, "socket": 214, "networkx": 1095, "numpy": 3136, "scipy": 60, "pandas": 2881, "pygraphviz": 4, "pydot": 4, "sympy": 11, "networkx.exception": 52, "networkx.lazy_imports": 4, "networkx.utils": 364, "networkx.algorithms": 48, "networkx.classes": 16, "networkx.convert": 4, "networkx.convert_matrix": 4, "networkx.drawing": 2, "networkx.generators": 16, "networkx.linalg": 4, "networkx.readwrite": 2, "networkx.relabel": 4, "toml.tz": 2, "toml": 15, "toml.decoder": 2, "_speedups": 2, "_native": 2, "html": 31, "thread": 10, "dummy_thread": 7, "_thread": 14, "_dummy_thread": 7, "sorteddict": 1, "sortedlist": 3, "sortedset": 2, "vulture.core": 2, "vulture.version": 1, "pkgutil": 39, "vulture": 2, "vulture.config": 1, "vulture.reachability": 1, "vulture.utils": 1, "_abnf": 6, "_events": 10, "_receivebuffer": 4, "_state": 6, "_util": 46, "_headers": 6, "h11._connection": 2, "h11._events": 2, "h11._state": 2, "h11._util": 2, "h11._version": 2, "_readers": 2, "_writers": 2, "os.path": 286, "setuptools": 96, "distutils.core": 58, "mypy.build": 38, "mypy.errors": 84, "mypy.fscache": 26, "mypy.main": 12, "mypy.options": 126, "mypy.util": 86, "mypyc.codegen": 4, "mypyc.common": 64, "mypyc.errors": 18, "mypyc.ir.pprint": 24, "mypyc.namegen": 26, "mypyc.options": 26, "distutils": 110, "mypyc.ir.rtypes": 122, "mypyc.ir.func_ir": 68, "mypyc.subtype": 10, "platformdirs": 9, "pylint.__pkginfo__": 2, "pylint.typing": 32, "pylint.lint": 71, "pylint.lint.run": 4, "pylint.pyreverse.main": 2, "pylint.checkers.symilar": 1, "pylint.config.callback_actions": 6, "pylint.pyreverse.inspector": 3, "pylint.reporters.ureports.nodes": 14, "pylint.utils": 18, "pylint": 39, "streamlit.delta_generator_singletons": 23, "streamlit.elements.alert": 2, "streamlit.elements.arrow": 2, "streamlit.elements.balloons": 2, "streamlit.elements.bokeh_chart": 2, "streamlit.elements.code": 2, "streamlit.elements.deck_gl_json_chart": 3, "streamlit.elements.doc_string": 2, "streamlit.elements.empty": 2, "streamlit.elements.exception": 6, "streamlit.elements.form": 2, "streamlit.elements.graphviz_chart": 2, "streamlit.elements.heading": 4, "streamlit.elements.html": 2, "streamlit.elements.iframe": 2, "streamlit.elements.image": 2, "streamlit.elements.json": 2, "streamlit.elements.layouts": 2, "streamlit.elements.lib.form_utils": 52, "streamlit.elements.map": 2, "streamlit.elements.markdown": 2, "streamlit.elements.media": 2, "streamlit.elements.metric": 2, "streamlit.elements.plotly_chart": 2, "streamlit.elements.progress": 2, "streamlit.elements.pyplot": 2, "streamlit.elements.snow": 2, "streamlit.elements.text": 2, "streamlit.elements.toast": 2, "streamlit.elements.vega_charts": 2, "streamlit.elements.widgets.audio_input": 2, "streamlit.elements.widgets.button": 2, "streamlit.elements.widgets.button_group": 2, "streamlit.elements.widgets.camera_input": 2, "streamlit.elements.widgets.chat": 2, "streamlit.elements.widgets.checkbox": 2, "streamlit.elements.widgets.color_picker": 2, "streamlit.elements.widgets.data_editor": 2, "streamlit.elements.widgets.file_uploader": 6, "streamlit.elements.widgets.multiselect": 3, "streamlit.elements.widgets.number_input": 2, "streamlit.elements.widgets.radio": 2, "streamlit.elements.widgets.select_slider": 4, "streamlit.elements.widgets.selectbox": 2, "streamlit.elements.widgets.slider": 4, "streamlit.elements.widgets.text_widgets": 2, "streamlit.elements.widgets.time_widgets": 4, "streamlit.elements.write": 2, "streamlit.errors": 155, "streamlit.proto": 73, "streamlit.proto.RootContainer_pb2": 7, "streamlit.runtime": 19, "streamlit.runtime.scriptrunner": 46, "google.protobuf.message": 15, "streamlit.cursor": 8, "streamlit.elements.lib.built_in_chart_utils": 6, "secrets": 10, "rich": 42, "streamlit.config_option": 8, "streamlit.logger": 70, "authlib": 2, "streamlit.runtime.secrets": 18, "authlib.jose": 37, "streamlit.string_util": 46, "pyarrow": 334, "array": 96, "polars": 46, "xarray": 7, "streamlit.type_util": 42, "pandas.core.indexing": 16, "pandas.io.formats.style": 30, "pandas.api.types": 62, "streamlit.column_config": 2, "streamlit.components.v1": 4, "streamlit.deprecation_util": 20, "streamlit.version": 4, "streamlit.delta_generator": 113, "streamlit.elements.lib.mutable_status_container": 8, "streamlit.elements.lib.dialog": 8, "streamlit.elements.dialog_decorator": 2, "streamlit.runtime.caching": 19, "streamlit.runtime.connection_factory": 2, "streamlit.runtime.fragment": 12, "streamlit.runtime.metrics_util": 140, "streamlit.runtime.context": 2, "streamlit.runtime.state": 60, "streamlit.user_info": 2, "streamlit.commands.experimental_query_params": 2, "streamlit.commands.echo": 2, "streamlit.commands.logo": 2, "streamlit.commands.navigation": 4, "streamlit.navigation.page": 10, "streamlit.elements.spinner": 4, "streamlit.commands.page_config": 2, "streamlit.commands.execution_control": 2, "streamlit.emojis": 2, "streamlit.material_icon_names": 2, "streamlit.elements.lib.column_types": 4, "requests": 51, "streamlit.proto.ForwardMsg_pb2": 46, "streamlit.runtime.scriptrunner_utils.script_run_context": 72, "streamlit.util": 34, "altair": 33, "plotly.graph_objs": 82, "pydeck": 6, "packaging": 21, "streamlit.auth_util": 7, "streamlit.url_util": 8, "git": 18, "rich.console": 134, "streamlit.web.cli": 2, "annotated_types": 15, "attr.setters": 2, "attr.validators": 2, "attr": 21, "attr._next_gen": 2, "attr.exceptions": 2, "attr.converters": 2, "attr.filters": 2, "pygments.styles": 6, "pygments.util": 100, "pygments.token": 559, "locale": 83, "pygments.lexers": 31, "pygments": 23, "docutils": 12, "docutils.parsers.rst": 14, "docutils.statemachine": 6, "sphinx.util.nodes": 6, "pygments.lexers._mapping": 4, "pygments.formatters": 5, "pygments.filters": 6, "colorama.initialise": 4, "pygments.formatters.latex": 2, "pygments.formatters.terminal": 4, "pygments.formatters.terminal256": 2, "pygments.lexers.special": 2, "pygments.lexer": 450, "pygments.formatter": 28, "unicodedata": 63, "chardet": 6, "pygments.filter": 4, "pygments.regexopt": 2, "pygments.cmdline": 2, "ast_transforms": 2, "c_lexer": 2, "ply": 4, "plyparser": 2, "c_parser": 2, "c_ast": 2, "lextab": 2, "yacctab": 2, "_ast_gen": 2, "pycparser": 4, "ply.lex": 4, "rpds": 12, "_pytest._code": 28, "_pytest.assertion": 10, "_pytest.cacheprovider": 8, "_pytest.capture": 8, "_pytest.config": 75, "_pytest.config.argparsing": 50, "_pytest.debugging": 4, "_pytest.doctest": 2, "_pytest.fixtures": 38, "_pytest.freeze_support": 2, "_pytest.legacypath": 2, "_pytest.logging": 2, "_pytest.main": 30, "_pytest.mark": 8, "_pytest.nodes": 38, "_pytest.outcomes": 44, "_pytest.pytester": 4, "_pytest.python": 12, "_pytest.python_api": 6, "_pytest.recwarn": 2, "_pytest.reports": 26, "_pytest.runner": 14, "_pytest.stash": 24, "_pytest.terminal": 20, "_pytest.tmpdir": 6, "_pytest.warning_types": 28, "click.shell_completion": 19, "core": 98, "shellingham": 6, "_completion_classes": 2, "_completion_shared": 4, "params": 4, "click.parser": 5, "click.exceptions": 2, "click.termui": 2, "click.utils": 6, "click.formatting": 2, "click.types": 2, "gettext": 31, "_typing": 47, "completion": 4, "rich.align": 7, "rich.columns": 7, "rich.emoji": 2, "rich.highlighter": 13, "rich.markdown": 7, "rich.padding": 24, "rich.panel": 25, "rich.table": 32, "rich.text": 51, "rich.theme": 6, "typer": 49, "typer.core": 4, "rich.traceback": 12, "rich_utils": 2, "typer.main": 2, "_legacy": 1, "_password_hasher": 2, "_utils": 117, "low_level": 5, "_argon2_cffi_bindings": 1, "exceptions": 172, "profiles": 4, "timeit": 20, "util": 140, "actions": 4, "results": 7, "unicode": 6, "common": 7, "pdb": 10, "diagram": 2, "html.entities": 9, "future": 16, "idtracking": 2, "nodes": 24, "optimizer": 2, "visitor": 6, "environment": 32, "runtime": 20, "posixpath": 46, "bccache": 4, "loaders": 4, "async_utils": 5, "lexer": 8, "_string": 3, "constants": 21, "_identifier": 2, "compiler": 10, "defaults": 2, "ext": 6, "debug": 12, "filters": 2, "tests": 2, "marshal": 17, "pickle": 210, "sandbox": 2, "cryptography.__about__": 2, "cryptography.hazmat.bindings._rust": 64, "binascii": 61, "cryptography.exceptions": 39, "cryptography.hazmat.primitives": 62, "cryptography.hazmat.primitives.ciphers": 19, "cryptography.hazmat.primitives.hmac": 2, "nltk.corpus": 94, "xml.etree": 26, "nltk.tree": 72, "nltk": 92, "tkinter": 52, "tkinter.messagebox": 17, "nltk.draw.table": 4, "nltk.draw.util": 18, "urllib.error": 28, "nltk.corpus.reader.util": 72, "xml.etree.ElementTree": 25, "nltk.data": 66, "pydoc": 15, "nltk.collections": 2, "nltk.internals": 96, "joblib": 21, "tqdm": 8, "numpypy": 2, "nltk.collocations": 8, "nltk.decorators": 4, "nltk.featstruct": 6, "nltk.grammar": 36, "nltk.probability": 52, "nltk.text": 8, "nltk.util": 86, "nltk.jsontags": 6, "nltk.chunk": 8, "nltk.classify": 14, "nltk.inference": 4, "nltk.metrics": 24, "nltk.parse": 36, "nltk.tag": 46, "nltk.tokenize": 52, "nltk.translate": 28, "nltk.sem": 20, "nltk.stem": 4, "nltk.downloader": 2, "nltk.tree.prettyprinter": 6, "nltk.lm": 8, "nltk.lm.preprocessing": 6, "nltk.draw": 8, "nltk.tree.transforms": 6, "nltk.metrics.spearman": 4, "nltk.sem.logic": 40, "gzip": 72, "zlib": 54, "nltk.app.wordnet_app": 4, "nltk.classify.maxent": 10, "_bcrypt": 1, "referencing._core": 6, "attrs": 18, "referencing": 32, "referencing._attrs": 6, "referencing.typing": 8, "referencing.jsonschema": 31, "six": 33, "metaclass": 2, "contracts.utils": 2, "contracts": 16, "interface": 28, "enabling": 2, "useful_contracts": 1, "backported": 2, "contracts.interface": 7, "library": 4, "syntax": 39, "docstring_parsing": 2, "inspection": 1, "decorator": 1, "library.extensions": 1, "main_actual": 1, "ansi": 14, "ansitowin32": 12, "initialise": 4, "win32": 4, "winterm": 4, "msvcrt": 16, "atexit": 48, "imp": 13, "importlib._bootstrap": 2, "_imp": 11, "ctypes.util": 10, "cffi._shimmed_dist_utils": 8, "cffi.api": 2, "cffi": 13, "setuptools.command.build_ext": 4, "setuptools.command.build_py": 2, "distutils.ccompiler": 8, "distutils.command.build_ext": 12, "distutils.dir_util": 6, "distutils.errors": 90, "distutils.log": 6, "distutils.msvc9compiler": 2, "api": 41, "error": 49, "lock": 4, "cStringIO": 6, "_cffi_backend": 6, "verifier": 2, "recompiler": 2, "cffi_opcode": 2, "pycparser.lextab": 2, "pycparser.yacctab": 2, "commontypes": 2, "pattern": 5, "_meta": 55, "gitignore": 1, "pathspec": 4, "patterns.gitwildmatch": 2, "ruler": 12, "rules_block.state_block": 2, "markdown_it": 34, "markdown_it._compat": 4, "common.utils": 38, "rules_core": 2, "rules_core.state_core": 4, "rules_inline.state_inline": 2, "linkify_it": 2, "parser_block": 2, "parser_core": 2, "parser_inline": 2, "renderer": 2, "getopt": 7, "fontTools.misc.cliTools": 24, "fontTools.misc.loggingTools": 34, "fontTools.misc.macCreatorType": 4, "fontTools.misc.textTools": 166, "fontTools.misc.timeTools": 8, "fontTools.ttLib": 100, "fontTools.unicode": 4, "fontTools": 112, "fontTools.ttLib.sfnt": 8, "misc.timeTools": 2, "ttLib": 2, "ttLib.tables._c_m_a_p": 2, "ttLib.tables._g_l_y_f": 2, "ttLib.tables.O_S_2f_2": 2, "cffLib": 2, "varLib.builder": 2, "varLib": 2, "feaLib.builder": 2, "fontTools.colorLib.builder": 10, "otlLib.builder": 4, "ttLib.tables._f_v_a_r": 2, "unicodedata2": 4, "fontTools.misc.sstruct": 2, "EasyDialogs": 2, "runpy": 9, "intranges": 8, "package_data": 4, "uts46data": 4, "altair.vegalite.v5.schema._config": 4, "altair.vegalite.v5.theme": 2, "altair.utils.plugin_registry": 6, "altair.vegalite": 6, "IPython.core": 4, "narwhals.stable.v1.dependencies": 8, "IPython.core.getipython": 4, "altair.expr": 4, "altair.jupyter": 2, "altair.utils": 12, "altair.vegalite.v5.schema.core": 8, "altair._magics": 2, "altair.utils.deprecation": 8, "_ffi": 1, "ctypes.wintypes": 19, "_compat": 48, "decorators": 7, "formatting": 4, "termui": 4, "shell_completion": 4, "_textwrap": 2, "getpass": 21, "_termui_impl": 2, "colorama": 17, "_winconsole": 2, "termios": 8, "tty": 4, "tenacity": 32, "tornado": 100, "concurrent": 16, "after": 2, "before": 2, "before_sleep": 2, "nap": 2, "retry": 8, "stop": 2, "wait": 8, "tenacity.asyncio": 2, "tenacity.tornadoweb": 2, "tornado.concurrent": 50, "libcst._nodes.base": 38, "libcst.metadata.base_provider": 28, "libcst.metadata.wrapper": 12, "libcst._flatten_sentinel": 10, "libcst._maybe_sentinel": 20, "libcst._removal_sentinel": 16, "libcst._typed_visitor_base": 2, "libcst._nodes.expression": 20, "libcst._nodes.module": 16, "libcst._nodes.op": 18, "libcst._nodes.statement": 16, "libcst._nodes.whitespace": 28, "libcst._metadata_dependent": 14, "libcst._typed_visitor": 4, "libcst._visitors": 38, "libcst._batched_visitor": 8, "libcst._exceptions": 18, "libcst._parser.entrypoints": 42, "libcst._parser.types.config": 34, "libcst._version": 2, "libcst.helpers": 51, "libcst._types": 24, "libcst._add_slots": 32, "libcst._parser.parso.pgen2.generator": 8, "libcst._parser.parso.python.token": 22, "libcst._parser.types.token": 18, "libcst._tabs": 4, "libcst": 456, "libcst._parser.parso.utils": 30, "libcst.codemod": 104, "libcst.display": 4, "libcst.display.text": 4, "_version": 27, "violations": 3, "checker": 2, "snowballstemmer": 1, "pydocstyle": 1, "wordlists": 1, "pytz.tzinfo": 4, "pytz": 126, "sets": 3, "pytz.exceptions": 6, "doctest": 88, "pytz.lazy": 2, "pytz.tzfile": 3, "pkg_resources": 68, "UserDict": 4, "isort.settings": 6, "isort": 5, "isort.utils": 2, "isort.exceptions": 3, "settings": 21, "isort.literal": 1, "format": 3, "place": 1, "pylama.lint": 1, "isort.comments": 1, "sections": 1, "wrap_modes": 3, "_vendored": 1, "comments": 3, "deprecated.finders": 1, "isort.parse": 1, "logo": 1, "isort.main": 1, "isort.format": 1, "identify": 1, "_error": 6, "_soft": 4, "_unix": 4, "_windows": 10, "fcntl": 10, "heapq": 40, "_decorators": 11, "dummy_threading": 17, "_core": 15, "shellingham._core": 2, "regex._regex": 4, "regex._regex_core": 4, "copyreg": 18, "bandit.core": 114, "bandit.core.constants": 4, "bandit.core.issue": 4, "bandit.core.test_properties": 4, "bandit.cli": 2, "numpy._core": 112, "numpy._core._multiarray_tests": 28, "numpy._core.tests._natype": 6, "numpy.testing._private.utils": 26, "scipy_doctest.conftest": 2, "lib._utils_impl": 4, "numpy.linalg": 34, "numpy.fft": 6, "numpy.dtypes": 10, "numpy.random": 35, "numpy.polynomial": 8, "numpy.ma": 31, "numpy.ctypeslib": 4, "numpy.exceptions": 52, "numpy.testing": 356, "numpy.matlib": 4, "numpy.f2py": 8, "numpy.typing": 36, "numpy.rec": 2, "numpy.char": 2, "numpy.core": 4, "numpy.strings": 4, "numpy.distutils": 8, "_expired_attrs_2_0": 2, "_globals": 2, "numpy.__config__": 2, "lib": 13, "lib._arraypad_impl": 2, "lib._arraysetops_impl": 2, "lib._function_base_impl": 2, "lib._histograms_impl": 2, "lib._index_tricks_impl": 2, "lib._nanfunctions_impl": 2, "lib._npyio_impl": 2, "lib._polynomial_impl": 2, "lib._shape_base_impl": 2, "lib._stride_tricks_impl": 2, "lib._twodim_base_impl": 2, "lib._type_check_impl": 2, "lib._ufunclike_impl": 2, "matrixlib": 2, "_array_api_info": 2, "numpy._pytesttester": 24, "numpy._core._multiarray_umath": 44, "numpy._core._internal": 4, "numpy._core.multiarray": 37, "numpy.matrixlib.defmatrix": 4, "constant": 12, "encodings.aliases": 6, "charset_normalizer.cd": 2, "legacy": 2, "cd": 2, "md": 4, "_multibytecodec": 2, "_binary": 70, "_deprecate": 22, "mmap": 51, "colorsys": 20, "olefile": 4, "PyQt6": 8, "PySide6": 4, "PyQt6.QtCore": 2, "PyQt6.QtGui": 2, "PySide6.QtCore": 2, "PySide6.QtGui": 2, "calendar": 48, "PcxImagePlugin": 2, "PIL": 44, "JpegImagePlugin": 2, "IPython.display": 20, "fractions": 53, "TiffTags": 2, "defusedxml": 2, "IPython.lib.pretty": 2, "TiffImagePlugin": 2, "features": 4, "JpegPresets": 2, "MpoImagePlugin": 2, "_imaging": 2, "_imagingft": 2, "_internal_utils": 20, "compat": 96, "cookies": 20, "adapters": 4, "hooks": 15, "status_codes": 12, "structures": 22, "simplejson": 4, "http.cookies": 11, "encodings.idna": 4, "idna": 16, "urllib3.exceptions": 8, "urllib3.fields": 2, "urllib3.filepost": 2, "urllib3.util": 6, "urllib3": 5, "charset_normalizer": 8, "urllib3.contrib": 4, "__version__": 12, "winreg": 27, "netrc": 7, "OpenSSL": 4, "urllib3.poolmanager": 2, "urllib3.util.retry": 2, "urllib3.util.ssl_": 2, "urllib3.contrib.socks": 2, "safety.formatters.bare": 2, "safety.formatters.html": 2, "safety.formatters.json": 4, "safety.formatters.screen": 2, "safety.formatters.text": 2, "dparse": 10, "packaging.utils": 29, "ruamel.yaml": 8, "ruamel.yaml.error": 24, "safety.constants": 37, "safety.errors": 18, "safety.events.event_bus": 10, "safety.models": 32, "safety_schemas.models": 52, "safety.auth.models": 14, "safety.auth.utils": 16, "safety.cli_util": 20, "safety.output_utils": 16, "safety.events.utils": 20, "safety.meta": 18, "safety.util": 34, "safety.asyncio_patch": 2, "safety": 6, "safety.alerts": 2, "safety.auth": 2, "safety.auth.cli": 8, "safety.console": 28, "safety.decorators": 12, "safety.error_handlers": 14, "safety.firewall.command": 2, "safety.formatter": 12, "safety.init.command": 4, "safety.scan.command": 6, "safety.scan.constants": 18, "safety.scan.finder": 4, "safety.scan.main": 8, "safety.tool": 5, "safety_schemas.config.schemas.v3_0": 2, "cli_util": 10, "scan.main": 2, "safety.scan.models": 8, "filelock": 10, "pydantic.json": 2, "output_utils": 2, "dparse.parser": 4, "dparse.updater": 2, "asyncio.proactor_events": 2, "typer.rich_utils": 2, "rich.prompt": 17, "safety.auth.constants": 16, "safety.auth.cli_utils": 2, "safety.cli": 2, "safety_schemas.models.events.types": 28, "safety_schemas.models.events.constants": 2, "mando.core": 2, "argcomplete": 2, "mando.napoleon": 2, "mando.utils": 4, "funcsigs": 2, "rst2ansi": 2, "_core._eventloop": 16, "_core._exceptions": 14, "_core._subprocesses": 4, "_core._synchronization": 16, "_core._tasks": 16, "lowlevel": 12, "streams.buffered": 2, "_core._fileio": 4, "_core._resources": 2, "_core._signals": 2, "_core._sockets": 8, "_core._streams": 6, "_core._tempfile": 2, "_core._testing": 6, "_core._typedattr": 8, "concurrent.futures": 76, "abc._tasks": 4, "_interpqueues": 2, "_interpreters": 2, "exceptiongroup": 19, "pip._internal.utils.entrypoints": 8, "pip._internal.cli.main": 4, "tornado.escape": 53, "tornado.log": 63, "tornado.util": 52, "hmac": 21, "tornado.httputil": 24, "tornado.web": 55, "tornado.ioloop": 39, "mimetypes": 31, "tornado.httpserver": 19, "tornado.routing": 6, "tornado.locks": 20, "tornado.simple_httpclient": 12, "tornado.options": 12, "tornado.netutil": 34, "logging.handlers": 7, "curses": 6, "tornado.speedups": 2, "tornado.http1connection": 8, "tornado.tcpserver": 10, "http.client": 24, "tornado.platform.asyncio": 9, "tornado.gen": 14, "tornado.process": 6, "tornado.iostream": 28, "linecache": 17, "csv": 52, "tornado._locale_data": 2, "pycurl": 4, "tornado.httpclient": 23, "importlib.abc": 18, "tornado.autoreload": 2, "tornado.tcpclient": 6, "tornado.queues": 4, "dataclasses_json.stringcase": 1, "dataclasses_json.undefined": 3, "dataclasses_json.api": 1, "dataclasses_json.cfg": 2, "dataclasses_json": 6, "dataclasses_json.utils": 4, "typing_inspect": 3, "dataclasses_json.core": 2, "dataclasses_json.mm": 1, "marshmallow_enum": 1, "buf": 2, "mman": 2, "contourpy._contourpy": 28, "contourpy.array": 8, "contourpy.enum_util": 12, "contourpy.typecheck": 6, "contourpy": 14, "contourpy.types": 6, "contourpy._version": 2, "contourpy.chunk": 2, "contourpy.convert": 6, "contourpy.dechunk": 4, "_contourpy": 2, "brotli": 8, "brotlicffi": 8, "zstandard": 6, "http.cookiejar": 5, "_auth": 6, "_config": 34, "_urls": 16, "rich.markup": 4, "rich.progress": 10, "rich.syntax": 17, "httpcore": 4, "_client": 6, "_status_codes": 8, "_types": 32, "_urlparse": 2, "_content": 4, "_transports": 2, "_main": 2, "_multipart": 4, "h2": 2, "_decoders": 4, "_transports.base": 2, "_transports.default": 2, "_multiprocessing_helpers": 10, "externals.loky": 4, "_store_backends": 3, "func_inspect": 2, "logger": 14, "urllib": 22, "_cloudpickle_wrapper": 4, "compressor": 6, "hashing": 2, "memory": 2, "numpy_pickle": 4, "parallel": 8, "disk": 8, "numpy_pickle_utils": 4, "backports": 8, "dask": 9, "distributed": 8, "dask.distributed": 2, "dask.sizeof": 2, "dask.utils": 2, "distributed.utils": 2, "_multiprocessing": 10, "multiprocessing.context": 16, "bz2": 45, "_memmapping_reducer": 8, "numpy_pickle_compat": 2, "lz4": 6, "lzma": 19, "joblib.backports": 8, "lz4.frame": 4, "numpy.lib.stride_tricks": 10, "externals.loky.backend": 2, "externals.loky.backend.resource_tracker": 2, "numpy.lib.array_utils": 22, "_parallel_backends": 3, "externals": 2, "_dask": 4, "multiprocessing.pool": 6, "executor": 2, "externals.loky.process_executor": 4, "pool": 2, "externals.loky.reusable_executor": 2, "importlib.resources": 29, "pip._internal.models.direct_url": 14, "pip._internal.utils.egg_link": 7, "pip._vendor.packaging.version": 43, "pip._internal.operations.freeze": 3, "pip._internal.utils.urls": 24, "pipdeptree._warning": 5, "site": 28, "pipdeptree._models.package": 2, "pipdeptree._cli": 2, "pipdeptree._detect_env": 1, "pipdeptree._discovery": 1, "pipdeptree._models": 7, "pipdeptree._render": 1, "pipdeptree._validate": 1, "curio.meta": 2, "_impl": 4, "_make": 18, "converters": 2, "_cmp": 2, "_funcs": 6, "_next_gen": 2, "_version_info": 2, "annotationlib": 2, "jsonschema.exceptions": 26, "jsonschema.protocols": 6, "jsonschema": 50, "jsonschema.validators": 22, "reprlib": 14, "referencing.exceptions": 10, "jsonschema_specifications": 6, "jsonschema._format": 2, "jsonschema._types": 4, "jsonschema._utils": 4, "rfc3987": 2, "webcolors": 2, "jsonpointer": 2, "uri_template": 2, "isoduration": 2, "fqdn": 2, "rfc3986_validator": 2, "rfc3339_validator": 2, "pkgutil_resolve_name": 2, "jsonschema.cli": 4, "pyarrow.parquet": 38, "narwhals._expression_parsing": 50, "narwhals.dependencies": 47, "narwhals.exceptions": 46, "narwhals.expr": 28, "narwhals.series": 27, "narwhals.translate": 14, "narwhals.utils": 61, "narwhals._compliant": 68, "narwhals._translate": 16, "narwhals.dataframe": 24, "narwhals.dtypes": 80, "narwhals.schema": 20, "narwhals.typing": 115, "narwhals": 15, "ibis": 8, "narwhals._namespace": 8, "narwhals._polars.namespace": 9, "narwhals._pandas_like.dataframe": 26, "narwhals._pandas_like.series": 25, "narwhals._dask.namespace": 8, "narwhals._ibis.dataframe": 7, "narwhals._interchange.dataframe": 4, "narwhals._compliant.typing": 46, "narwhals.group_by": 2, "narwhals.functions": 6, "narwhals.series_cat": 2, "narwhals.series_dt": 2, "narwhals.series_list": 2, "narwhals.series_str": 2, "narwhals.series_struct": 2, "narwhals._arrow.typing": 19, "narwhals._arrow.utils": 31, "duckdb": 41, "pyspark.sql": 7, "narwhals._arrow.namespace": 10, "narwhals._duckdb.namespace": 6, "narwhals._pandas_like.namespace": 8, "narwhals._spark_like.dataframe": 15, "narwhals._spark_like.namespace": 6, "modin.pandas": 4, "cudf": 4, "dask.dataframe": 20, "sqlframe": 1, "sqlframe._version": 2, "narwhals.stable.v1": 23, "narwhals.stable.v1._namespace": 2, "narwhals._pandas_like.utils": 30, "narwhals._interchange.series": 4, "sqlframe.base.dataframe": 4, "narwhals._polars.utils": 11, "narwhals.expr_cat": 2, "narwhals.expr_dt": 2, "narwhals.expr_list": 2, "narwhals.expr_name": 2, "narwhals.expr_str": 2, "narwhals.expr_struct": 2, "generic": 2, "refinement": 1, "_dist_ver": 2, "setuptools_scm": 6, "std": 38, "keras": 2, "auto": 20, "tensorflow": 2, "notebook": 10, "gui": 4, "_monitor": 4, "pandas.core.frame": 46, "pandas.core.series": 38, "pandas.core.window.rolling": 12, "pandas.core.window": 8, "pandas.core.window.expanding": 4, "pandas.core.groupby.generic": 20, "pandas.core.groupby.groupby": 12, "pandas.core.groupby": 22, "pandas.core.common": 110, "_tqdm_pandas": 2, "tkinter.ttk": 3, "importlib_resources": 6, "dask.callbacks": 2, "ipywidgets": 12, "IPython.html.widgets": 4, "autonotebook": 2, "mypy.nodes": 236, "mypy.types": 194, "mypy.typevartuples": 8, "mypy.typeops": 64, "mypy": 94, "mypy.message_registry": 18, "mypy.patterns": 26, "mypy.reachability": 8, "mypy.sharedparse": 6, "mypy.traverser": 50, "mypy.visitor": 28, "mypy.semanal_main": 4, "mypy.checker": 22, "mypy.error_formatter": 6, "mypy.graph_utils": 6, "mypy.indirection": 4, "mypy.messages": 40, "mypy.partially_defined": 2, "mypy.semanal": 16, "mypy.semanal_pass1": 2, "mypy.report": 6, "mypy.config_parser": 12, "mypy.fixup": 4, "mypy.freetree": 2, "mypy.metastore": 2, "mypy.modulefinder": 48, "mypy.parse": 8, "mypy.plugin": 50, "mypy.plugins.default": 2, "mypy.renaming": 2, "mypy.stats": 2, "mypy.stubinfo": 6, "mypy.typestate": 24, "mypy.version": 24, "mypy.server.target": 4, "mypy.server.deps": 6, "mypy.refinfo": 2, "mypy.errorcodes": 38, "mypy.type_visitor": 10, "mypy.state": 38, "mypy.mixedtraverser": 4, "mypy.find_sources": 12, "mypy.moduleinspect": 8, "mypy.plugins.dataclasses": 2, "mypy.semanal_shared": 26, "mypy.stubdoc": 8, "mypy.stubgenc": 4, "mypy.stubutil": 6, "mypy.argmap": 16, "mypy.checkexpr": 6, "mypy.join": 14, "mypy.meet": 20, "mypy.server.update": 10, "mypy.types_utils": 16, "mypy.fastparse": 6, "mypy.exprtotype": 10, "mypy.erasetype": 22, "mypy.expandtype": 36, "mypy.maptype": 26, "mypy.typetraverser": 12, "_winapi": 6, "orjson": 2, "_curses": 2, "xml.sax.saxutils": 4, "mypy.subtypes": 42, "mypy.typevars": 30, "resource": 13, "mypy.typeanal": 12, "mypy.bogus_type": 2, "mypy.tvar_scope": 8, "mypy.dmypy.client": 4, "mypy.literals": 12, "mypy.plugins": 12, "mypy.semanal_classprop": 2, "mypy.semanal_infer": 2, "mypy.semanal_typeargs": 2, "mypy.server.aststrip": 4, "mypy.ipc": 8, "mypy.copytype": 2, "mypy.infer": 8, "mypy.checkmember": 10, "mypy.dmypy_util": 6, "mypy.fswatcher": 2, "mypy.inspections": 4, "mypy.suggestions": 2, "mypy.memprofile": 4, "mypy.operators": 10, "mypy.scope": 10, "mypy.lookup": 4, "mypy.constraints": 12, "mypy.solve": 6, "sqlite3.dbapi2": 2, "mypy.strconv": 4, "mypy.split_namespace": 2, "_typeshed": 44, "mypy.checkstrformat": 4, "mypy.semanal_enum": 8, "mypy.server.trigger": 14, "mypy.defaults": 10, "lxml": 12, "mypy.binder": 2, "mypy.checkpattern": 2, "mypy.mro": 4, "mypy.treetransform": 4, "mypy.constant_fold": 4, "mypy.semanal_namedtuple": 2, "mypy.semanal_newtype": 2, "mypy.semanal_typeddict": 2, "mypy.applytype": 4, "symtable": 3, "mypy.evalexpr": 2, "radon.raw": 10, "radon.visitors": 18, "radon.cli": 8, "radon": 2, "_cext": 2, "among": 29, "basestemmer": 29, "Stemmer": 1, "arabic_stemmer": 1, "armenian_stemmer": 1, "basque_stemmer": 1, "catalan_stemmer": 1, "danish_stemmer": 1, "dutch_stemmer": 1, "english_stemmer": 1, "finnish_stemmer": 1, "french_stemmer": 1, "german_stemmer": 1, "greek_stemmer": 1, "hindi_stemmer": 1, "hungarian_stemmer": 1, "indonesian_stemmer": 1, "irish_stemmer": 1, "italian_stemmer": 1, "lithuanian_stemmer": 1, "nepali_stemmer": 1, "norwegian_stemmer": 1, "porter_stemmer": 1, "portuguese_stemmer": 1, "romanian_stemmer": 1, "russian_stemmer": 1, "serbian_stemmer": 1, "spanish_stemmer": 1, "swedish_stemmer": 1, "tamil_stemmer": 1, "turkish_stemmer": 1, "yiddish_stemmer": 1, "_defaults": 4, "backend": 6, "graphs": 2, "jupyter_integration": 2, "parameters": 8, "quoting": 2, "sources": 4, "encoding": 12, "backend.execute": 2, "yaml._yaml": 4, "_pytest.hookspec": 3, "default_styles": 8, "theme": 12, "_loop": 42, "segment": 70, "style": 106, "console": 162, "rich._null_file": 4, "_log_render": 8, "highlighter": 36, "text": 112, "protocol": 12, "jupyter": 78, "measure": 68, "styled": 8, "_emoji_replace": 12, "_export_format": 4, "_fileno": 4, "align": 32, "color": 26, "control": 20, "emoji": 12, "markup": 8, "pager": 4, "pretty": 16, "region": 8, "scope": 8, "screen": 8, "terminal_theme": 16, "live": 20, "status": 4, "rule": 14, "rich.json": 2, "rich._win32_console": 6, "rich._windows_renderer": 2, "rich.cells": 12, "table": 40, "_palettes": 4, "color_triplet": 16, "repr": 17, "constrain": 12, "cells": 32, "containers": 10, "_emoji_codes": 8, "_ratio": 8, "panel": 30, "rich.tree": 4, "rich.styled": 2, "columns": 8, "palette": 8, "_extension": 4, "rich._inspect": 2, "rich.color": 12, "rich.style": 50, "rich.segment": 34, "rich.repr": 57, "_pick": 12, "IPython.core.formatters": 6, "rich.pretty": 10, "padding": 12, "markdown_it.token": 15, "_stack": 2, "__main__": 14, "_wrap": 4, "file_proxy": 4, "live_render": 4, "pygments.style": 98, "rich.containers": 2, "_timer": 4, "pty": 4, "progress_bar": 4, "spinner": 8, "_cell_widths": 4, "box": 4, "_spinners": 4, "rich.measure": 10, "pgen2": 2, "pgen2.grammar": 1, "blib2to3.pgen2.grammar": 5, "pgen2.token": 1, "dis": 12, "testslide.lib": 2, "testslide.mock_callable": 5, "testslide.mock_constructor": 3, "testslide": 5, "testslide.strict_mock": 4, "patch": 2, "testslide.matchers": 2, "typeguard": 3, "coverage": 55, "asyncio.log": 1, "testslide.patch_attribute": 1, "testslide.runner": 1, "testslide.dsl": 1, "runner": 1, "strict_mock": 1, "testslide.import_profiler": 1, "wcwidth": 3, "_hooks": 6, "_result": 8, "_warnings": 4, "_manager": 2, "_callers": 2, "nodejs_wheel": 1, "matplotlib.font_manager": 40, "matplotlib.transforms": 139, "matplotlib.path": 91, "matplotlib._path": 2, "path": 40, "matplotlib.collections": 56, "matplotlib.lines": 46, "matplotlib.artist": 44, "matplotlib.colors": 73, "matplotlib.scale": 10, "matplotlib.text": 38, "matplotlib.ticker": 44, "matplotlib.units": 16, "dateutil.tz": 70, "matplotlib.ft2font": 14, "matplotlib.mathtext": 4, "matplotlib.texmanager": 8, "IPython": 12, "matplotlib._enums": 4, "matplotlib._pylab_helpers": 28, "matplotlib.backend_managers": 2, "matplotlib.cbook": 32, "matplotlib.layout_engine": 6, "matplotlib.figure": 50, "backends.registry": 2, "matplotlib.dates": 27, "matplotlib.mlab": 6, "matplotlib.spines": 12, "gi.repository": 10, "_mathtext": 2, "matplotlib.backends": 22, "plistlib": 8, "matplotlib._fontconfig_pattern": 4, "matplotlib.rcsetup": 12, "_mathtext_data": 4, "matplotlib._cm": 2, "matplotlib._cm_bivar": 2, "matplotlib._cm_listed": 2, "matplotlib._cm_multivar": 2, "matplotlib.colorizer": 10, "matplotlib.container": 10, "matplotlib.offsetbox": 16, "matplotlib.axes": 56, "matplotlib._api": 6, "matplotlib.colorbar": 10, "matplotlib.image": 20, "matplotlib.legend": 8, "matplotlib.backend_bases": 78, "matplotlib.gridspec": 14, "style.core": 2, "matplotlib.cm": 8, "font_manager": 4, "ft2font": 4, "_enums": 10, "artist": 8, "cbook": 6, "transforms": 18, "matplotlib.patheffects": 12, "cycler": 16, "lines": 2, "patches": 6, "matplotlib._animation_data": 2, "backend_bases": 6, "bezier": 4, "matplotlib.bezier": 4, "textpath": 2, "matplotlib.projections": 10, "matplotlib._constrained_layout": 2, "matplotlib._tight_layout": 2, "colors": 4, "dateutil.parser": 18, "dateutil.relativedelta": 8, "dateutil.rrule": 4, "PIL.Image": 8, "matplotlib.axes._base": 8, "matplotlib.axis": 24, "matplotlib.contour": 8, "matplotlib.quiver": 4, "matplotlib.typing": 2, "matplotlib.widgets": 6, "ticker": 2, "IPython.core.pylabtools": 2, "matplotlib.hatch": 2, "kiwisolver": 2, "PIL.PngImagePlugin": 8, "matplotlib._image": 2, "matplotlib._layoutgrid": 2, "_color_data": 2, "tokens": 8, "events": 14, "dumper": 2, "loader": 2, "cyaml": 4, "emitter": 2, "representer": 4, "resolver": 8, "serializer": 4, "constructor": 4, "scanner": 4, "composer": 2, "reader": 2, "base": 154, "modulefinder": 2, "tomlkit": 6, "pycompat": 1, "configs": 3, "stdlib_list": 1, "render_context": 1, "pydeps.configs": 2, "arguments": 1, "dummymodule": 1, "pystdlib": 1, "pydeps": 1, "depgraph2dot": 1, "hypothesis.internal.conjecture": 8, "hypothesis.internal.conjecture.data": 24, "hypothesis.strategies": 23, "hypothesis.strategies._internal.utils": 19, "hypothesis._settings": 15, "hypothesis.internal.conjecture.choice": 11, "hypothesis.utils.conventions": 10, "watchdog.observers.api": 3, "sphinx.ext.autodoc": 3, "watchdog.events": 1, "watchdog.observers": 4, "hypothesis.internal.compat": 21, "hypothesis.internal.reflection": 27, "hypothesis.internal.validation": 17, "hypothesis.utils.dynamicvariables": 5, "hypothesis.vendor.pretty": 8, "hypothesis.control": 12, "hypothesis.core": 4, "hypothesis.entry_points": 1, "hypothesis.internal.detection": 1, "hypothesis.internal.entropy": 3, "hypothesis.version": 3, "hypothesis.database": 5, "hypothesis.internal.conjecture.engine": 10, "hypothesis.internal.conjecture.junkdrawer": 13, "hypothesis.internal.conjecture.providers": 5, "hypothesis.internal.conjecture.shrinker": 3, "hypothesis.internal.scrutineer": 2, "hypothesis.strategies._internal.misc": 5, "hypothesis.strategies._internal.strategies": 25, "hypothesis.stateful": 1, "hypothesis.strategies._internal.featureflags": 1, "black": 5, "multidict": 1, "middlewares": 1, "_black_version": 3, "black.concurrency": 2, "aiohttp.typedefs": 1, "aiohttp.web_middlewares": 1, "aiohttp.web_request": 1, "aiohttp.web_response": 1, "blackd": 1, "fields": 28, "util.connection": 10, "util.timeout": 18, "util.url": 20, "response": 35, "_base_connection": 20, "_collections": 25, "connectionpool": 22, "filepost": 8, "poolmanager": 8, "util.request": 10, "util.retry": 20, "contrib.emscripten": 2, "_request_methods": 4, "connection": 42, "util.proxy": 10, "util.response": 8, "util.ssl_": 8, "util.ssltransport": 2, "http2": 18, "util.util": 4, "util.wait": 2, "util.ssl_match_hostname": 8, "email.errors": 10, "_utilities": 2, "jsonschema_specifications._core": 2, "more_itertools": 31, "packaging.licenses": 2, "setuptools.wheel": 4, "unicode_utils": 6, "_path": 31, "_importlib": 12, "distutils._modified": 6, "_distutils._modified": 2, "_distutils_hack.override": 4, "distutils.util": 39, "setuptools.errors": 12, "jaraco.text": 17, "depends": 2, "discovery": 9, "dist": 22, "extension": 14, "_reqs": 8, "wheel": 8, "org.python.modules.posix.PosixModule": 1, "setuptools.sandbox": 4, "distutils.extension": 10, "setuptools._path": 6, "monkey": 4, "email": 30, "packaging.markers": 8, "_static": 2, "distutils.filelist": 10, "jaraco.functools": 10, "_itertools": 5, "distutils.cmd": 12, "distutils.command": 13, "distutils.dist": 20, "distutils.debug": 8, "distutils.fancy_getopt": 6, "_normalization": 4, "installer": 2, "command.bdist_wheel": 2, "packaging.tags": 4, "setuptools.archive_util": 9, "setuptools.command.egg_info": 12, "ntpath": 6, "_typeshed.importlib": 2, "consts": 2, "pydantic_core": 76, "_internal": 36, "annotated_handlers": 20, "pydantic": 42, "_internal._schema_generation_shared": 6, "json_schema": 22, "_migration": 40, "aliases": 14, "_internal._generate_schema": 4, "_internal._utils": 8, "pydantic_core._pydantic_core": 4, "_internal._repr": 2, "pydantic_core.core_schema": 8, "functional_serializers": 2, "functional_validators": 8, "networks": 4, "type_adapter": 6, "validate_call_decorator": 2, "deprecated.class_validators": 2, "deprecated.config": 2, "deprecated.tools": 2, "root_model": 6, "mypy.plugins.common": 14, "pydantic._internal": 2, "pydantic.version": 6, "pydantic.warnings": 4, "_internal._core_utils": 2, "_internal._dataclasses": 4, "_internal._validators": 2, "email_validator": 6, "pydantic.errors": 6, "pydantic.main": 2, "plugin._schema_validator": 12, "deprecated.parse": 2, "deprecated": 6, "deprecated.json": 2, "_parse": 2, "pathspec.patterns.gitwildmatch": 2, "black.handle_ipynb_magics": 2, "black.mode": 11, "black.output": 7, "black.report": 4, "black.nodes": 9, "blib2to3": 2, "blib2to3.pgen2": 10, "blib2to3.pgen2.parse": 1, "blib2to3.pgen2.tokenize": 3, "blib2to3.pytree": 13, "tokenize_rt": 1, "IPython.core.inputtransformer2": 1, "uvloop": 3, "black.cache": 3, "json.decoder": 2, "black.comments": 3, "black.const": 2, "black.files": 1, "black.linegen": 1, "black.lines": 3, "black.parsing": 2, "black.ranges": 1, "black.brackets": 2, "black.strings": 4, "blib2to3.pgen2.token": 2, "black.rusty": 1, "black.numerics": 1, "black.trans": 1, "black._width_table": 1, "_common": 24, "_psutil_windows": 4, "pwd": 15, "psutil._common": 14, "_pslinux": 2, "_pswindows": 2, "_pssunos": 2, "future.utils": 78, "lib2to3": 43, "lib2to3.main": 2, "libfuturize.fixes": 1, "lib2to3.fixer_util": 26, "lib2to3.pygram": 8, "lib2to3.pytree": 3, "coverage.exceptions": 43, "coverage.types": 56, "coverage.misc": 45, "coverage.plugin": 30, "coverage.report_core": 14, "coverage.results": 19, "coverage.tomlconfig": 2, "coverage.bytecode": 4, "coverage.debug": 16, "coverage.data": 15, "coverage.annotate": 2, "coverage.collector": 2, "coverage.config": 10, "coverage.context": 2, "coverage.core": 6, "coverage.disposition": 6, "coverage.files": 16, "coverage.html": 2, "coverage.inorout": 2, "coverage.jsonreport": 2, "coverage.lcovreport": 2, "coverage.multiproc": 2, "coverage.plugin_support": 4, "coverage.python": 8, "coverage.report": 2, "coverage.xmlreport": 2, "coverage.templite": 2, "coverage.version": 10, "coverage.plugins": 2, "coverage.control": 4, "coverage.execfile": 2, "ox_profile.core.launchers": 2, "eventlet.greenthread": 2, "gevent": 2, "__pypy__": 6, "coverage.pytracer": 2, "coverage.sysmon": 2, "coverage.tracer": 2, "coverage.numbits": 2, "coverage.sqlitedb": 2, "coverage.phystokens": 4, "xml.dom.minidom": 5, "pudb": 2, "coverage.parser": 2, "coverage.regions": 2, "_pypy_irc_topic": 2, "_structseq": 2, "multiprocessing.process": 5, "coverage.cmdline": 2, "coverage.sqldata": 3, "pbr": 34, "testrepository": 2, "pbr.hooks": 14, "pbr.version": 6, "sphinx.util": 4, "pbr.pbr_json": 2, "distutils.command.install_scripts": 6, "urlparse": 6, "setuptools.command": 9, "nose": 3, "reno": 2, "gitdb.exc": 24, "gitdb.fun": 10, "gitdb.util": 28, "gitdb_speedups._perf": 6, "gitdb.base": 18, "gitdb.const": 8, "gitdb.stream": 12, "gitdb.typ": 10, "gitdb.utils.encoding": 10, "sha": 2, "smmap": 4, "gitdb.db": 8, "_dill": 4, "dill": 37, "pickletools": 6, "__info__": 1, "session": 2, "source": 4, "shelve": 3, "dbm.ndbm": 2, "xdrlib": 1, "_pyio": 2, "dill._dill": 6, "diff": 1, "multiprocessing.reduction": 9, "_shims": 1, "detect": 2, "pointers": 1, "temp": 1, "libpasteurize.fixes": 1, "typeguard.importhook": 1, "_pydantic_core": 2, "core_schema": 2, "git.exc": 26, "git.types": 54, "git.util": 46, "git.cmd": 22, "git.compat": 28, "git.diff": 10, "git.repo.base": 12, "git.config": 18, "git.refs": 24, "git.objects.commit": 8, "git.objects.submodule.base": 4, "git.remote": 10, "exc": 2, "git.db": 8, "git.index": 8, "git.objects": 16, "git.repo": 35, "git.objects.blob": 2, "git.objects.util": 8, "git.objects.base": 6, "git.objects.tree": 4, "tomlkit.api": 4, "tomlkit.toml_document": 6, "tomlkit.container": 8, "tomlkit._compat": 8, "tomlkit._types": 4, "tomlkit._utils": 8, "tomlkit.exceptions": 10, "tomlkit.items": 6, "tomlkit.source": 2, "tomlkit.toml_char": 4, "tomlkit.parser": 2, "pandas._testing": 1634, "pandas.util._test_decorators": 222, "zoneinfo": 22, "pandas._config.config": 44, "pandas.core": 104, "pandas.core.dtypes.dtypes": 252, "pandas.core.indexes.api": 80, "pandas.util.version": 53, "IPython.core.interactiveshell": 2, "traitlets.config": 2, "pandas._libs": 326, "pandas._libs.tslibs": 228, "pandas.arrays": 47, "pandas.core.arrays.base": 24, "pandas.core.generic": 38, "pandas.core.indexes.base": 62, "pandas.core.internals": 28, "pandas.core.resample": 18, "pandas.io.formats.format": 32, "pandas.tseries.holiday": 16, "pandas.core.config_init": 2, "pandas.compat": 202, "pandas._config": 174, "pandas.core.api": 16, "pandas.core.computation.api": 2, "pandas.core.reshape.api": 2, "pandas.io.api": 2, "pandas.io.json._normalize": 6, "pandas.tseries": 42, "pandas.tseries.api": 2, "pandas.util._print_versions": 4, "pandas.util._tester": 2, "pandas._version_meson": 4, "pandas._version": 4, "tz.win": 2, "six.moves": 10, "dateutil": 23, "enabled": 4, "exception": 6, "docutils.parsers": 2, "stevedore": 30, "named": 6, "driver": 2, "hook": 2, "bindings": 4, "nbextension": 2, "_pytest.mark.structures": 10, "_pytest._io": 20, "_pytest.deprecated": 30, "twisted.trial.unittest": 2, "_pytest.compat": 22, "twisted.trial.itrial": 2, "zope.interface": 2, "bdb": 4, "_pytest._code.code": 20, "reports": 4, "py": 2, "_pytest._io.saferepr": 12, "_pytest._version": 4, "pluggy": 18, "_pytest._io.wcwidth": 2, "_pytest.assertion.util": 4, "_pytest.pathlib": 22, "_pytest.warnings": 2, "tracemalloc": 4, "_pytest.config.exceptions": 6, "argcomplete.completers": 2, "pexpect": 4, "iniconfig": 6, "_pytest.pytester_assertions": 2, "faulthandler": 7, "warning_types": 4, "_pytest.scope": 8, "_pytest.config.compat": 4, "importlib._bootstrap_external": 3, "pyarrow._orc": 2, "pyarrow.fs": 32, "pyarrow.lib": 38, "cython": 24, "fastparquet": 4, "pyarrow.gandiva": 4, "pyarrow.acero": 6, "pyarrow.dataset": 22, "pyarrow.orc": 2, "pyarrow.parquet.encryption": 8, "pyarrow.flight": 8, "pyarrow.substrait": 8, "pyarrow.cuda": 4, "pyarrow.tests.util": 20, "pyarrow._flight": 2, "pyarrow._substrait": 2, "pyarrow.ipc": 4, "pyarrow.types": 6, "_generated_version": 2, "setuptools_scm.git": 2, "pyarrow.util": 26, "pyarrow._dataset": 4, "pyarrow.compute": 65, "pyarrow._dataset_orc": 2, "pyarrow._dataset_parquet": 2, "pyarrow._dataset_parquet_encryption": 2, "pyarrow._cuda": 2, "pyarrow._feather": 2, "pyarrow.pandas_compat": 12, "concurrent.futures.thread": 2, "pandas.api.internals": 2, "fsspec": 10, "pyarrow._fs": 2, "pyarrow._azurefs": 2, "pyarrow._hdfs": 2, "pyarrow._gcsfs": 2, "pyarrow._s3fs": 2, "pyarrow._acero": 2, "pyarrow._csv": 2, "pyarrow._json": 2, "pyarrow._compute": 6, "pyarrow.vendored": 2, "platformdirs.windows": 4, "platformdirs.macos": 4, "platformdirs.unix": 4, "platformdirs.android": 4, "android": 6, "jnius": 6, "_pytest._pluggy": 1, "pytest_metadata.ci": 1, "pytest_metadata": 1, "pandas.testing": 34, "conftest": 2, "pyarrow.tests.strategies": 14, "hypothesis.extra.numpy": 6, "hypothesis.extra.pytz": 7, "tzdata": 2, "pyarrow.tests": 17, "pyarrow.interchange": 10, "pyarrow.vendored.version": 18, "pandas.tseries.offsets": 80, "numba.cuda.cudadrv.devicearray": 2, "pyarrow._pyarrow_cpp_tests": 2, "pyarrow.cffi": 2, "pyarrow.jvm": 2, "jpype.imports": 2, "java.lang": 2, "pyarrow.tests.test_io": 2, "sparse": 2, "scipy.sparse": 14, "pyarrow.csv": 6, "pyarrow.feather": 6, "pyarrow.json": 4, "test_fs": 2, "pyarrow.tests.parquet.encryption": 4, "test_cython": 2, "pandas_examples": 2, "test_extension_type": 2, "pyarrow.interchange.column": 6, "from_dataframe": 2, "pyarrow.interchange.buffer": 2, "pyarrow._parquet_encryption": 2, "pyarrow._parquet": 4, "pyarrow.interchange.from_dataframe": 4, "pandas.api.interchange": 2, "pyarrow.tests.parquet.common": 18, "pyarrow.tests.pandas_examples": 4, "findpaths": 2, "argparsing": 2, "_pytest.helpconfig": 2, "_pytest._argcomplete": 2, "expression": 2, "_code": 2, "fixtures": 15, "_pytest._code.source": 2, "_pytest._io.pprint": 2, "_pytest.assertion.rewrite": 2, "importlib.resources.abc": 2, "importlib.readers": 2, "importlib.resources.readers": 2, "pygments.lexers.python": 20, "pygments.lexers.diff": 4, "terminalwriter": 2, "grp": 8, "_code.source": 2, "function": 4, "image": 4, "pydeck.types": 4, "pydeck.types.base": 4, "traitlets": 7, "data_utils.binary_transfer": 2, "_frontend": 2, "debounce": 2, "frontend_semver": 4, "widget": 4, "bindings.view_state": 2, "type_checking": 5, "color_scales": 2, "viewport_helpers": 2, "io.html": 2, "base_map_provider": 2, "json_tools": 10, "layer": 4, "map_styles": 2, "view": 4, "view_state": 4, "deck": 2, "light_settings": 2, "pydeck.exceptions": 2, "data_utils": 2, "stevedore.example": 6, "stevedore.tests": 24, "stevedore.dispatch": 2, "stevedore.extension": 2, "stevedore.example2": 2, "testtools.matchers": 2, "ruamel.yaml.anchor": 14, "ruamel.yaml.compat": 30, "ruamel.yaml.docinfo": 6, "ruamel.yaml.tokens": 8, "ordereddict": 2, "ruamel.yaml.comments": 8, "ruamel.yaml.nodes": 12, "ruamel.yaml.scalarbool": 4, "ruamel.yaml.scalarfloat": 4, "ruamel.yaml.scalarint": 4, "ruamel.yaml.scalarstring": 6, "ruamel.yaml.tag": 12, "ruamel.yaml.timestamp": 4, "ruamel.yaml.util": 10, "ruamel.yaml.serializer": 4, "ruamel.yaml.events": 10, "configobj": 2, "ruamel.yaml.main": 2, "ruamel.yaml.emitter": 4, "ruamel.yaml.representer": 6, "ruamel.yaml.resolver": 8, "_ruamel_yaml": 4, "ruamel.yaml.constructor": 6, "ruamel.yaml.scanner": 4, "ruamel.yaml.composer": 2, "ruamel.yaml.parser": 2, "ruamel.yaml.reader": 2, "ruamel.yaml.dumper": 2, "ruamel.yaml.loader": 2, "dateutil.zoneinfo": 4, "isoparser": 2, "_factories": 2, "win": 2, "tz": 2, "pandas.util._exceptions": 167, "pandas.compat.compressors": 4, "pandas.compat._constants": 12, "pandas.compat.numpy": 98, "pandas.compat.pyarrow": 18, "pandas._typing": 360, "pandas._libs.arrays": 14, "pandas.core.arrays": 278, "pandas.util._decorators": 158, "pandas.compat._optional": 102, "pandas.core.dtypes.common": 363, "pandas.core.dtypes.missing": 154, "scipy.stats": 12, "pandas.core.dtypes.cast": 115, "pandas.core.array_algos.take": 6, "pandas.core.construction": 88, "pandas.core.dtypes.concat": 28, "pandas.core.dtypes.generic": 132, "pandas.core.indexers": 40, "pandas.core.reshape.tile": 8, "pandas.core.internals.construction": 8, "pandas.core.sorting": 32, "pandas.core.algorithms": 80, "pandas._libs.tslibs.dtypes": 62, "pandas.core.apply": 14, "pandas.core.base": 34, "pandas.core.groupby.grouper": 18, "pandas.core.groupby.ops": 14, "pandas.core.indexes.datetimes": 38, "pandas.core.indexes.period": 18, "pandas.core.indexes.timedeltas": 18, "pandas.errors": 330, "pandas.tseries.frequencies": 18, "pandas._libs.ops_dispatch": 2, "pandas.core.ops.common": 12, "pandas.core.dtypes.base": 35, "pandas.core.arrays.string_": 50, "pandas.core.computation": 24, "pandas.core.util": 2, "pandas.io.formats.printing": 52, "pandas.plotting._core": 6, "pandas.plotting": 18, "pandas._libs.lib": 36, "pandas.core.array_algos.replace": 4, "pandas.core.dtypes.astype": 24, "pandas.core.dtypes.inference": 29, "pandas.core.flags": 4, "pandas.core.methods.describe": 2, "pandas.core.missing": 10, "pandas.core.reshape.concat": 42, "pandas.core.shared_docs": 48, "pandas.util._validators": 64, "pandas.core.indexers.objects": 14, "pandas.core.computation.parsing": 6, "pandas.io.formats.excel": 12, "pandas.io": 10, "pandas.io.pickle": 4, "pandas.core.tools.datetimes": 18, "pandas.core.accessor": 18, "pandas.core.arrays.arrow": 8, "pandas.core.arrays.categorical": 22, "pandas.core.arrays.sparse": 32, "pandas.core.indexes.accessors": 6, "pandas.core.indexes.multi": 16, "pandas.core.methods": 4, "pandas.core.strings.accessor": 10, "pandas.io.formats.info": 4, "pandas._libs.internals": 18, "pandas.core.reshape.reshape": 4, "pandas._libs.hashtable": 10, "pandas._libs.missing": 32, "pandas.core.arrays.boolean": 16, "pandas.core.arrays.floating": 21, "pandas.core.arrays.integer": 29, "pandas.core.indexes.interval": 4, "pandas.core.tools.numeric": 8, "pandas.core.tools.timedeltas": 6, "pandas.core.arraylike": 16, "pandas.core.reshape.melt": 4, "pandas.io.common": 68, "pandas.io.formats": 21, "pandas.core.interchange.dataframe_protocol": 14, "pandas.core.interchange.dataframe": 2, "pandas.core.methods.to_dict": 2, "pandas.io.stata": 8, "pandas.io.feather_format": 8, "pandas.io.parquet": 6, "pandas.io.orc": 4, "pandas.io.formats.xml": 2, "pandas.core.arrays.masked": 14, "pandas.core.arrays.arrow.array": 14, "pandas.core.computation.eval": 8, "pandas.core.reshape.pivot": 6, "pandas.core.reshape.merge": 16, "pandas._libs.indexing": 2, "pandas.core._numba.executor": 2, "pandas.core._numba.extensions": 2, "pandas.core.util.hashing": 16, "pandas._libs.properties": 6, "pandas.io._util": 16, "pandas.core.arrays.arrow.extension_types": 14, "tables": 4, "pandas.compat.pickle_compat": 2, "pandas.core.computation.pytables": 2, "pandas.io.parsers": 28, "bs4": 2, "lxml.etree": 14, "lxml.html": 2, "pandas.io.clipboard": 4, "pandas.io.clipboards": 2, "pandas.io.excel": 16, "pandas.io.gbq": 2, "pandas.io.html": 4, "pandas.io.json": 6, "pandas.io.pytables": 18, "pandas.io.sas": 2, "pandas.io.spss": 2, "pandas.io.sql": 4, "pandas.io.xml": 10, "botocore.exceptions": 2, "sqlalchemy": 14, "sqlalchemy.sql.expression": 2, "sqlalchemy.schema": 4, "sqlalchemy.types": 2, "sqlalchemy.engine": 6, "pandas._libs.writers": 4, "pandas.core.indexes.range": 8, "google.auth.credentials": 2, "pandas._libs.tslibs.parsing": 14, "pandas._libs.tslibs.offsets": 56, "pandas._libs.algos": 10, "pandas._libs.tslibs.ccalendar": 12, "pandas._libs.tslibs.fields": 8, "pandas.core.arrays.datetimelike": 14, "dask.array": 4, "sklearn": 4, "hypothesis.extra.dateutil": 2, "pandas._testing.contexts": 4, "pandas._config.localization": 4, "pandas._testing._io": 2, "pandas._testing._warnings": 2, "pandas._testing.asserters": 2, "pandas._testing.compat": 2, "pandas.core.arrays._mixins": 16, "pandas._libs.testing": 2, "pandas._libs.sparse": 18, "pandas._libs.tslibs.np_datetime": 12, "pandas._libs.pandas_parser": 2, "pandas._libs.pandas_datetime": 2, "pandas._libs.interval": 10, "pandas.plotting._misc": 2, "matplotlib.table": 12, "pandas.api": 6, "pandas._config.display": 2, "pandas.core.dtypes.api": 6, "pandas.core.interchange.from_dataframe": 4, "pandas.io.json._json": 6, "pandas.plotting._matplotlib.style": 12, "pandas.plotting._matplotlib.tools": 12, "pandas.plotting._matplotlib.core": 6, "pandas.plotting._matplotlib.groupby": 6, "pandas.plotting._matplotlib.misc": 8, "pandas.plotting._matplotlib.boxplot": 2, "pandas.plotting._matplotlib.converter": 12, "pandas.plotting._matplotlib.hist": 4, "pandas.plotting._matplotlib": 6, "pandas.plotting._matplotlib.timeseries": 2, "pandas._libs.tslibs.conversion": 10, "pandas._libs.tslibs.nattype": 8, "pandas._libs.tslibs.period": 28, "pandas._libs.tslibs.timedeltas": 18, "pandas._libs.tslibs.timestamps": 4, "pandas._libs.tslibs.timezones": 18, "pandas._libs.tslibs.tzconversion": 4, "pandas._libs.tslibs.vectorized": 4, "pandas.core.internals.blocks": 28, "pandas.core.computation.check": 10, "pandas.core.reshape": 4, "pandas.core.reshape.util": 8, "pandas.tests.apply.common": 6, "pandas.core.groupby.base": 6, "pandas.tests.frame.common": 8, "pandas.tests.strings": 6, "pandas.core.tools.times": 6, "dateutil.tz.tz": 6, "pandas.core.tools": 4, "pandas.tests.indexes.datetimes.test_timezones": 6, "pandas.tests.extension": 22, "pandas.core.dtypes": 8, "pandas.core.arrays.numpy_": 8, "pandas.api.extensions": 18, "IPython.core.completer": 8, "pandas.util": 16, "fsspec.implementations.memory": 2, "fsspec.registry": 2, "boto3": 2, "py.path": 8, "pandas.tests.io.generate_legacy_storage_files": 2, "adbc_driver_manager": 2, "adbc_driver_postgresql": 2, "adbc_driver_sqlite": 2, "sqlalchemy.dialects.postgresql": 2, "sqlalchemy.sql": 2, "sqlalchemy.dialects.mysql": 2, "sqlalchemy.orm": 4, "botocore.response": 2, "pandas.tests.copy_view.util": 28, "pandas.core.interchange.utils": 10, "pandas.core.interchange.column": 4, "pandas._libs.join": 4, "pandas.tests.groupby": 16, "pandas.api.typing": 2, "pandas._libs.groupby": 6, "pandas.core.computation.engines": 6, "numexpr": 6, "pandas.core.computation.expr": 8, "pandas.core.computation.expressions": 4, "pandas.core.computation.ops": 10, "pandas.core.computation.scope": 8, "pandas.tests.plotting.common": 26, "pylab": 4, "pandas._libs.window.aggregations": 6, "pandas.api.indexers": 10, "numba": 36, "pandas.tests.extension.decimal": 4, "pandas.tests.extension.base": 4, "pandas.tests.arithmetic.common": 10, "pandas.core.ops.array_ops": 4, "pandas.tests.api.test_api": 2, "pandas._libs.tslibs.strptime": 4, "pandas._testing._hypothesis": 10, "pandas.tests.indexing.common": 8, "pandas.tests.indexing.test_floats": 2, "pandas.core.arrays.string_arrow": 16, "pandas.tests.base.common": 6, "pandas.core.indexes.frozen": 12, "pandas.core.indexes.datetimelike": 8, "pandas.core.arrays.datetimes": 16, "pandas.core.arrays.timedeltas": 14, "pandas._libs.index": 2, "pandas.core.arrays.arrow._arrow_utils": 7, "pandas.core.ops.mask_ops": 4, "pandas.tests.arrays.masked_shared": 6, "mpl_toolkits.axes_grid1": 10, "mpl_toolkits.axes_grid1.inset_locator": 4, "pandas.tests.extension.decimal.array": 8, "pandas.tests.tseries.offsets.common": 32, "pandas.io.formats.css": 4, "_csv": 2, "s3fs": 2, "xlrd": 8, "python_calamine": 4, "odf.namespaces": 4, "odf.table": 6, "pandas.io.excel._util": 12, "pandas.io.excel._openpyxl": 6, "openpyxl": 5, "pandas.io.excel._base": 20, "xlrd.biffh": 2, "pandas.io.parsers.c_parser_wrapper": 8, "pandas.io.parsers.base_parser": 10, "pandas._libs.parsers": 14, "pandas.io.parsers.readers": 10, "botocore": 2, "pandas.io.json._table_schema": 8, "pandas.tests.extension.date": 2, "pandas._libs.json": 6, "pandas.io.sas.sas7bdat": 4, "pandas._libs.byteswap": 4, "pandas.io.sas.sasreader": 8, "pandas.tests.io.pytables.common": 28, "pandas.io.formats.style_render": 8, "pandas.tests.extension.array_with_attr": 2, "pandas.tests.extension.array_with_attr.array": 2, "pandas.tests.extension.date.array": 2, "pandas.tests.extension.json.array": 4, "pandas.tests.extension.list.array": 4, "pandas.tests.extension.base.accumulate": 2, "pandas.tests.extension.base.casting": 2, "pandas.tests.extension.base.constructors": 2, "pandas.tests.extension.base.dim2": 2, "pandas.tests.extension.base.dtype": 2, "pandas.tests.extension.base.getitem": 2, "pandas.tests.extension.base.groupby": 2, "pandas.tests.extension.base.index": 2, "pandas.tests.extension.base.interface": 2, "pandas.tests.extension.base.io": 2, "pandas.tests.extension.base.methods": 2, "pandas.tests.extension.base.missing": 2, "pandas.tests.extension.base.ops": 2, "pandas.tests.extension.base.printing": 2, "pandas.tests.extension.base.reduce": 2, "pandas.tests.extension.base.reshaping": 2, "pandas.tests.extension.base.setitem": 2, "pandas.io.parsers.arrow_parser_wrapper": 2, "pandas.io.parsers.python_parser": 2, "pandas._libs.ops": 4, "pandas.io.formats.html": 2, "pandas.io.formats.string": 2, "pandas.io.formats.csvs": 2, "pandas.io.formats._color_data": 2, "pandas.io.formats.console": 2, "openpyxl.descriptors.serialisable": 2, "openpyxl.workbook": 2, "openpyxl.styles": 3, "openpyxl.cell.cell": 2, "pandas.io.excel._calamine": 2, "pandas.io.excel._odfreader": 2, "pandas.io.excel._pyxlsb": 2, "pandas.io.excel._xlrd": 2, "pandas.io.excel._odswriter": 2, "pandas.io.excel._xlsxwriter": 2, "pyxlsb": 2, "odf.opendocument": 4, "odf.text": 4, "odf.style": 2, "odf.config": 2, "odf.element": 2, "odf.office": 2, "xlsxwriter": 2, "pandas.io.sas.sas_constants": 2, "pandas._libs.sas": 2, "pandas.io.sas.sas_xport": 2, "AppKit": 42, "Foundation": 126, "qtpy": 2, "PyQt5": 4, "PyQt4": 2, "qtpy.QtWidgets": 2, "PyQt5.QtWidgets": 2, "PyQt4.QtGui": 2, "pandas.core.indexers.utils": 2, "pandas._libs.window.indexers": 2, "pandas._libs.reshape": 2, "pandas.core.reshape.encoding": 2, "pandas.core.strings.base": 4, "pandas._libs.hashing": 2, "pandas.core.nanops": 5, "pandas.core.interchange.buffer": 2, "pandas.core.arrays.sparse.array": 6, "pandas.core.util.numba_": 12, "pandas.core.groupby.categorical": 2, "pandas.core._numba": 4, "pandas.core.groupby.indexing": 2, "pandas.core._numba.kernels": 4, "pandas.core.internals.array_manager": 6, "pandas.core.internals.managers": 12, "pandas.core.internals.api": 2, "pandas.core.internals.base": 6, "pandas.core.internals.concat": 2, "pandas.core.array_algos.quantile": 10, "pandas.core.array_algos.putmask": 6, "pandas.core.array_algos.transforms": 4, "pandas.core.internals.ops": 2, "pandas.core.computation.common": 6, "pandas.core.computation.align": 2, "numba.core": 2, "numba.core.datamodel": 2, "numba.core.extending": 2, "numba.core.imputils": 2, "numba.typed": 2, "pandas.core.window.common": 4, "pandas.core.window.doc": 6, "pandas.core.window.numba_": 4, "pandas.core.window.online": 2, "pandas.core.window.ewm": 2, "pandas.core.arrays.numeric": 6, "pandas.core.strings.object_array": 6, "pandas.core.ops": 9, "pandas.core.array_algos": 10, "pandas.core.arrays._ranges": 4, "pandas.core.arrays.interval": 6, "pandas.core.arrays.period": 6, "pandas.core.arrays._arrow_string_mixins": 4, "pandas.core.arrays._utils": 4, "pandas.core.ops.invalid": 10, "pandas.core.ops.dispatch": 2, "pandas.core.ops.docstrings": 2, "pandas.core.roperator": 2, "pandas.core.indexes.extension": 12, "pandas.core.indexes.category": 2, "pandas.core.arrays.arrow.accessors": 2, "pandas.core.arrays.sparse.scipy_sparse": 2, "pandas.core.arrays.sparse.accessor": 2, "numba.extending": 2, "pandas.core._numba.kernels.shared": 6, "pandas.core._numba.kernels.mean_": 2, "pandas.core._numba.kernels.min_max_": 2, "pandas.core._numba.kernels.sum_": 4, "pandas.core._numba.kernels.var_": 2, "blob": 10, "fun": 6, "submodule.base": 6, "commit": 6, "tag": 6, "tree": 10, "gitdb": 12, "submodule": 2, "git.refs.reference": 6, "head": 4, "log": 3, "reference": 8, "remote": 4, "symbolic": 6, "git.refs.log": 2, "git.objects.fun": 2, "typ": 6, "git.refs.tag": 2, "gitdb.db.loose": 8, "git.refs.symbolic": 2, "root": 2, "lib2to3.pgen2": 10, "libfuturize.fixer_util": 29, "feature_base": 1, "multiprocess": 1, "test_mixins": 2, "cmath": 4, "test_dictviews": 1, "test_classdef": 1, "xml": 1, "dill.tests.__main__": 1, "dill.source": 2, "dill.detect": 1, "dill._objects": 1, "test_moduledict": 1, "dill.temp": 2, "test_source": 1, "pox": 1, "dill.logger": 1, "gitdb.pack": 4, "gitdb.test.lib": 10, "gitdb.db.base": 12, "gitdb.db.pack": 4, "gitdb.db.ref": 4, "gitdb.db.git": 4, "gitdb.db.mem": 2, "pbr.tests": 22, "testtools": 16, "mock": 14, "testscenarios": 4, "virtualenv": 2, "testresources": 2, "Queue": 8, "copy_reg": 1, "_markupbase": 1, "markupbase": 1, "__builtin__": 14, "future.builtins": 33, "commands": 3, "future.backports.misc": 4, "future.standard_library": 6, "socketserver": 2, "SocketServer": 1, "_winreg": 5, "UserList": 1, "UserString": 1, "cPickle": 5, "multiprocessing.queues": 3, "future.types.newbytes": 3, "future.types.newobject": 5, "future.types": 6, "future.types.newstr": 2, "future.utils.surrogateescape": 1, "newbytes": 1, "newint": 1, "newstr": 1, "newdict": 1, "newlist": 1, "newobject": 1, "newrange": 1, "unittest2": 4, "future.moves.subprocess": 1, "future.types.newdict": 1, "future.types.newint": 2, "future_builtins": 1, "future.builtins.new_min_max": 1, "future.builtins.newnext": 1, "future.builtins.newround": 1, "future.builtins.newsuper": 1, "future.builtins.iterators": 1, "future.builtins.misc": 1, "_weakref": 1, "misc": 1, "_strptime": 1, "_datetime": 1, "test": 3, "dbm": 3, "future.backports.urllib": 7, "future.moves.test": 1, "future.moves.dbm": 2, "future.backports.http.server": 3, "future.backports.test": 1, "future.backports.http.cookiejar": 1, "future.backports.email.utils": 3, "ftplib": 1, "future.backports": 8, "future.backports.http": 3, "parse": 4, "nturl2path": 1, "_scproxy": 1, "future.backports.html.entities": 1, "future.backports.email": 15, "future.backports.urllib.parse": 4, "future.backports.http.client": 1, "future.backports.xmlrpc.client": 1, "xml.parsers": 1, "future.backports.email.charset": 3, "future.backports.email.errors": 1, "future.backports.email.quoprimime": 1, "future.standard_library.email._policybase": 1, "future.standard_library.email.headerregistry": 1, "future.standard_library.email.utils": 1, "future.backports.email.parser": 1, "uu": 1, "future.backports.email._encoded_words": 1, "future.backports.email._policybase": 4, "future.backports.email.generator": 1, "future.backports.email.iterators": 1, "quopri": 2, "future.backports.email.feedparser": 1, "future.backports.email.message": 1, "future.backports.email.header": 1, "future.backports.email._parseaddr": 1, "future.backports.email.encoders": 3, "future.backports.email.mime.base": 2, "future.backports.email.mime.nonmultipart": 5, "sndhdr": 1, "imghdr": 1, "test.support": 11, "test.support.os_helper": 3, "test.support.warnings_helper": 3, "test.test_support": 1, "urllib.response": 1, "urllib.robotparser": 1, "robotparser": 1, "html.parser": 5, "HTMLParser": 3, "htmlentitydefs": 3, "tkinter.scrolledtext": 1, "ScrolledText": 1, "tkinter.colorchooser": 1, "tkColorChooser": 1, "tkinter.commondialog": 1, "tkCommonDialog": 1, "tkMessageBox": 1, "tkinter.dialog": 1, "Dialog": 1, "tkinter.constants": 1, "Tkconstants": 1, "tkinter.dnd": 1, "Tkdnd": 1, "Tkinter": 1, "ttk": 1, "tkinter.filedialog": 9, "FileDialog": 1, "tkFileDialog": 1, "tkinter.font": 19, "tkFont": 1, "tkinter.tix": 1, "Tix": 1, "tkinter.simpledialog": 3, "SimpleDialog": 1, "Cookie": 1, "http.server": 9, "BaseHTTPServer": 2, "CGIHTTPServer": 1, "SimpleHTTPServer": 1, "cookielib": 1, "dbm.gnu": 1, "gdbm": 1, "anydbm": 1, "whichdb": 1, "dbm.dumb": 1, "dumbdbm": 1, "xmlrpc.server": 1, "xmlrpclib": 4, "xmlrpc.client": 9, "libpasteurize.fixes.fix_division": 1, "lib2to3.fixes.fix_input": 1, "lib2to3.fixes.fix_xrange": 1, "lib2to3.fixes.fix_imports": 2, "lib2to3.fixes.fix_urllib": 1, "lib2to3.fixes.fix_import": 1, "libfuturize.fixes.fix_print": 1, "psutil._compat": 24, "psutil.tests": 38, "psutil._psutil_posix": 6, "psutil._psutil_linux": 2, "psutil._pslinux": 4, "pip": 14, "psutil._psposix": 2, "psutil.tests.test_process_all": 2, "win32api": 4, "win32con": 2, "win32process": 2, "wmi": 2, "psutil._pswindows": 2, "pydantic.color": 2, "pydantic.types": 6, "pydantic.v1.utils": 34, "pydantic.v1.errors": 16, "pydantic.v1.typing": 36, "pydantic.v1.version": 6, "pydantic.v1.fields": 26, "pydantic.v1.main": 26, "pydantic.v1": 14, "pydantic.v1.class_validators": 10, "pydantic.v1.error_wrappers": 8, "pydantic.v1.types": 24, "pydantic.v1.validators": 6, "pydantic.v1.config": 22, "pydantic.v1.schema": 4, "pydantic.v1.json": 6, "pydantic.v1.datetime_parse": 4, "pydantic.v1.annotated_types": 4, "pydantic.v1.dataclasses": 8, "pydantic.v1.parse": 6, "pydantic.v1.decorator": 2, "pydantic.v1.env_settings": 2, "pydantic.v1.networks": 6, "pydantic.v1.tools": 2, "pydantic.v1.color": 2, "_core_utils": 10, "_generate_schema": 8, "_typing_extra": 12, "_dataclasses": 6, "eval_type_backport": 2, "pydantic.fields": 2, "_fields": 8, "_import_utils": 12, "_validators": 4, "_generics": 6, "_mock_val_ser": 6, "_schema_generation_shared": 10, "_signature": 4, "_validate_call": 2, "_core_metadata": 4, "_docs_extraction": 4, "_forward_ref": 4, "_serializers": 2, "_std_types_schema": 2, "_internal_dataclass": 4, "pydantic._internal._serializers": 2, "_repr": 2, "pydantic._internal._internal_dataclass": 2, "_loader": 2, "alias_generators": 2, "_internal._import_utils": 2, "authlib.common.encoding": 65, "authlib.common.urls": 59, "rfc6749": 35, "rfc6750": 6, "authlib.common.security": 34, "rfc6749.parameters": 2, "rfc7009": 4, "rfc7636": 2, "client": 8, "authlib.common.errors": 14, "authlib.deprecate": 6, "rfc7517": 14, "authlib.jose.errors": 28, "rfc7515": 8, "rfc7516": 8, "rfc7518": 2, "rfc7519": 2, "rfc8037": 2, "authlib.consts": 21, "rfc5849": 4, "rfc5849.errors": 2, "signature": 8, "base_server": 4, "wrapper": 6, "cryptography.hazmat.backends": 14, "cryptography.hazmat.primitives.asymmetric": 30, "cryptography.hazmat.primitives.serialization": 10, "authorization_server": 13, "client_auth": 2, "resource_protector": 12, "rsa": 2, "flask.signals": 4, "authlib.oauth2": 10, "authlib.oauth2.rfc6749": 22, "authlib.oauth2.rfc6750": 8, "authlib.oidc.core": 6, "framework_integration": 4, "registry": 11, "sync_app": 4, "sync_openid": 2, "authlib.oauth2.auth": 4, "authlib.oauth2.client": 4, "requests.auth": 4, "base_client": 32, "authlib.oauth1": 18, "assertion_session": 2, "oauth1_session": 2, "oauth2_session": 4, "authlib.oauth2.rfc7521": 4, "authlib.oauth2.rfc7523": 4, "authlib.oauth1.client": 4, "django.dispatch": 4, "django.http": 12, "endpoints": 2, "django.conf": 8, "django.utils.module_loading": 2, "django.utils.functional": 2, "authlib.oauth2.rfc7009": 4, "client_mixin": 2, "functions": 2, "tokens_mixins": 2, "apps": 6, "integration": 6, "starlette.datastructures": 2, "starlette.responses": 2, "base_client.async_app": 2, "base_client.async_openid": 2, "httpx_client": 2, "requests_client": 4, "authlib.oauth1.errors": 4, "cache": 2, "httpx": 19, "assertion_client": 2, "oauth1_client": 2, "oauth2_client": 4, "nonce": 4, "django.core.cache": 4, "claims": 15, "jwt": 2, "_cryptography_key": 6, "key_set": 4, "base_key": 4, "asymmetric_key": 2, "jwk": 2, "cryptography.x509": 8, "authlib.jose.rfc7516.models": 2, "authlib.jose.util": 4, "jwe": 2, "cryptography.hazmat.primitives.ciphers.algorithms": 8, "cryptography.hazmat.primitives.ciphers.modes": 8, "cryptography.hazmat.primitives.padding": 2, "cryptography.hazmat.primitives.asymmetric.ec": 6, "ec_key": 6, "jwe_algs": 2, "jwe_encs": 2, "jwe_zips": 2, "jws_algs": 2, "oct_key": 6, "rsa_key": 6, "cryptography.hazmat.primitives.asymmetric.utils": 2, "authlib.jose.rfc7516": 8, "cryptography.hazmat.primitives.kdf.concatkdf": 4, "cryptography.hazmat.primitives.keywrap": 2, "cryptography.hazmat.primitives.asymmetric.rsa": 4, "_jwe_algorithms": 2, "_jwe_enc_cryptography": 2, "_jwe_enc_cryptodome": 2, "authlib.jose.rfc7518": 2, "authlib.jose.rfc8037": 2, "cryptography.hazmat.primitives.ciphers.aead": 2, "Cryptodome.Cipher": 2, "cryptography.hazmat.primitives.asymmetric.ed448": 2, "cryptography.hazmat.primitives.asymmetric.ed25519": 2, "cryptography.hazmat.primitives.asymmetric.x448": 2, "cryptography.hazmat.primitives.asymmetric.x25519": 2, "jws_eddsa": 2, "okp_key": 4, "jws": 2, "grants": 4, "authlib.oauth2.rfc8414": 2, "authlib.oauth2.rfc8414.models": 2, "well_known": 4, "authlib.oauth2.rfc6749.grants.authorization_code": 2, "implicit": 6, "hybrid": 2, "authlib.oauth2.base": 4, "revocation": 4, "device_code": 2, "endpoint": 6, "rfc6749.errors": 4, "authenticate_client": 4, "token_endpoint": 2, "rfc7591": 2, "rfc7591.claims": 2, "assertion": 6, "jwt_bearer": 2, "validator": 4, "introspection": 4, "token_validator": 4, "challenge": 2, "parameter": 2, "authlib.oauth2.rfc6750.token": 2, "authlib.jose.rfc7519": 2, "authlib.oauth2.rfc6750.errors": 6, "authlib.oauth2.rfc9068.token_validator": 4, "rfc7662": 2, "authlib.oauth2.rfc6750.validator": 2, "authorization_code": 2, "client_credentials": 2, "refresh_token": 2, "resource_owner_password_credentials": 2, "pkg1": 2, "pkg1.pkg2": 2, "nspkg": 2, "nspkg.subpkg": 2, "jaraco.path": 35, "test_resources": 2, "distutils.command.install_egg_info": 2, "mod": 2, "mod2": 2, "py39": 2, "setuptools.dist": 52, "email.headerregistry": 2, "setuptools._importlib": 6, "setuptools.config": 8, "_apply_pyprojecttoml": 2, "compat.py310": 5, "setuptools.discovery": 10, "jaraco": 13, "setuptools.warnings": 18, "_distutils_hack": 2, "setuptools.logging": 2, "email.generator": 14, "packaging.metadata": 4, "setuptools._core_metadata": 2, "config.downloads": 2, "setuptools.tests.textwrap": 2, "setuptools.command.build_clib": 2, "distutils.sysconfig": 12, "contexts": 4, "setuptools.command.build": 2, "setuptools.command.install_scripts": 2, "compat.py39": 6, "test_find_packages": 4, "setuptools.extension": 12, "setuptools.glob": 4, "jaraco.envs": 6, "setuptools.command.sdist": 6, "integration.helpers": 2, "setuptools.command.easy_install": 5, "setuptools.package_index": 2, "setuptools.depends": 4, "setuptools.tests": 5, "test_easy_install": 1, "wheel.macosx_libfile": 4, "setuptools.command.bdist_wheel": 6, "setuptools.command.develop": 1, "setuptools.command.editable_wheel": 2, "setuptools._normalization": 2, "setuptools.tests.server": 1, "distutils.command.build_py": 6, "distutils.command.build": 4, "setuptools.command.setopt": 6, "dl": 2, "Cython.Distutils.build_ext": 2, "distutils.command.build_scripts": 4, "_shutil": 4, "distutils.spawn": 7, "build": 4, "build_py": 2, "dist_info": 2, "egg_info": 6, "install": 2, "install_scripts": 2, "_vendor.wheel.wheelfile": 2, "wheel.wheelfile": 6, "distutils.command.install_lib": 4, "distutils.command.bdist": 4, "distutils.command.sdist": 4, "distutils.command.bdist_rpm": 4, "distutils.command.build_clib": 4, "modified": 2, "setuptools.unicode_utils": 2, "distutils.command.install": 8, "bdist_egg": 1, "easy_install": 2, "compilers.C": 10, "_log": 26, "compat.numpy": 2, "compilers.C.base": 4, "compilers.C.errors": 4, "dir_util": 8, "spawn": 6, "_modified": 10, "py_compile": 4, "cmd": 6, "compilers.C.cygwin": 2, "distutils.text_file": 6, "ccompiler": 10, "distutils.versionpredicate": 2, "fancy_getopt": 8, "distutils.tests": 50, "distutils.version": 2, "distutils.command.install_data": 2, "distutils.unixccompiler": 2, "distutils._log": 26, "xx": 2, "distutils.tests.support": 2, "distutils.command.install_headers": 2, "distutils.command.clean": 2, "distutils.command.check": 2, "distutils.command.bdist_dumb": 2, "distutils.file_util": 2, "distutils.tests.test_dist": 2, "distutils.command.config": 2, "distutils.archive_util": 4, "unix_compat": 4, "_msvccompiler": 2, "docutils.frontend": 2, "docutils.utils": 2, "filelist": 2, "text_file": 2, "archive_util": 2, "file_util": 8, "_macos_compat": 2, "distutils.compat": 2, "distutils.tests.compat.py39": 2, "distutils.cygwinccompiler": 2, "test.support.import_helper": 2, "jaraco.test.cpython": 2, "setuptools.config.pyprojecttoml": 4, "setuptools._static": 4, "setuptools.config.setupcfg": 2, "tomli_w": 2, "ini2toml.api": 2, "setuptools.config._apply_pyprojecttoml": 2, "downloads": 2, "setuptools.compat.py310": 2, "error_reporting": 4, "fastjsonschema_exceptions": 6, "extra_validations": 2, "fastjsonschema_validations": 2, "setuptools._vendor.packaging": 2, "trove_classifiers": 2, "compat.py38": 4, "_functools": 7, "zipp": 3, "_text": 157, "vendored.packaging.requirements": 2, "_bdist_wheel": 2, "metadata": 14, "vendored.packaging": 2, "wheelfile": 8, "macosx_libfile": 2, "wheel.cli": 6, "wheel.util": 2, "more": 2, "recipes": 4, "autocommand": 6, "automain": 4, "autoparse": 4, "autoasync": 4, "autocommand.errors": 2, "_re": 5, "_memo": 8, "_transformer": 4, "typeguard._config": 4, "typeguard._exceptions": 2, "typeguard._importhook": 2, "typeguard._utils": 2, "_checkers": 4, "_functions": 6, "_importhook": 2, "_suppression": 2, "typeshed.stdlib.types": 2, "_union_transformer": 2, "vendored.packaging.tags": 2, "unpack": 2, "pack": 2, "convert": 2, "inflect": 2, "jaraco.context": 2, "_operator": 2, "packaging.licenses._spdx": 4, "ssl_": 10, "url": 10, "request": 16, "timeout": 6, "ssltransport": 4, "socks": 4, "OpenSSL.SSL": 4, "OpenSSL.crypto": 6, "cryptography.x509.extensions": 8, "h2.config": 6, "h2.connection": 6, "h2.events": 6, "js": 4, "pyodide.ffi": 2, "urllib3.connection": 2, "fetch": 4, "hypothesis.strategies._internal": 3, "hypothesis.strategies._internal.collections": 5, "hypothesis.strategies._internal.core": 6, "hypothesis.strategies._internal.datetime": 1, "hypothesis.strategies._internal.ipaddress": 2, "hypothesis.strategies._internal.numbers": 5, "hypothesis.strategies._internal.types": 4, "hypothesis.extra.django._impl": 2, "hypothesis.internal.floats": 13, "sortedcontainers": 4, "hypothesis.internal.intervalsets": 5, "hypothesis.internal.coverage": 6, "dpcontracts": 1, "hypothesis.extra._array_helpers": 2, "libcst.metadata": 152, "lark": 1, "hypothesis.internal.conjecture.utils": 11, "hypothesis.strategies._internal.regex": 3, "lark.grammar": 1, "lark.lark": 1, "lark.lexer": 1, "libcst.matchers": 56, "hypothesis.strategies._internal.lazy": 7, "hypothesis.provisional": 3, "hypothesis.strategies._internal.deferred": 2, "hypothesis.strategies._internal.flatmapped": 2, "hypothesis.extra": 10, "hypothesis.utils.terminal": 1, "redis": 3, "_hypothesis_pytestplugin": 1, "django": 3, "django.contrib.staticfiles": 1, "django.core.exceptions": 1, "django.db": 2, "hypothesis.extra.django._fields": 2, "django.contrib.auth.forms": 1, "django.core.validators": 1, "hypothesis.extra.pandas.impl": 1, "hypothesis.internal.conjecture.pareto": 2, "hypothesis.internal.conjecture.floats": 3, "hypothesis.internal.conjecture.shrinking": 1, "hypothesis.internal.conjecture.shrinking.choicetree": 1, "hypothesis.internal.cache": 4, "hypothesis.internal.constants_ast": 1, "hypothesis.internal.conjecture.datatree": 1, "hypothesis.internal.conjecture.optimiser": 1, "hypothesis.internal": 3, "hypothesis.internal.conjecture.dfa": 1, "hypothesis.internal.conjecture.shrinking.common": 4, "hypothesis.internal.conjecture.shrinking.integer": 4, "hypothesis.internal.conjecture.shrinking.bytes": 1, "hypothesis.internal.conjecture.shrinking.collection": 3, "hypothesis.internal.conjecture.shrinking.floats": 1, "hypothesis.internal.conjecture.shrinking.ordering": 2, "hypothesis.internal.conjecture.shrinking.string": 1, "strategies": 1, "hypothesis.internal.cathetus": 1, "hypothesis.internal.charmap": 2, "hypothesis.strategies._internal.functions": 1, "hypothesis.strategies._internal.recursive": 1, "hypothesis.strategies._internal.shared": 1, "hypothesis.strategies._internal.strings": 2, "hypothesis.strategies._internal.random": 1, "hypothesis.strategies._internal.attrs": 1, "hypothesis.internal.filtering": 4, "_hypothesis_ftz_detector": 1, "re._constants": 1, "re._parser": 1, "sre_constants": 1, "sre_parse": 1, "matplotlib.tri._triangulation": 10, "matplotlib.tri": 10, "matplotlib.tri._trifinder": 4, "_triangulation": 2, "_tricontour": 2, "_trifinder": 2, "_triinterpolate": 2, "_tripcolor": 2, "_triplot": 2, "_trirefine": 2, "_tritools": 2, "matplotlib.tri._triinterpolate": 2, "matplotlib.tri._tritools": 2, "matplotlib.category": 6, "matplotlib.inset": 2, "matplotlib.markers": 9, "matplotlib.stackplot": 2, "matplotlib.streamplot": 2, "matplotlib.axes._secondary_axes": 2, "_axes": 2, "docutils.parsers.rst.directives.images": 4, "sphinx.errors": 6, "sphinx": 2, "gi": 10, "matplotlib.backend_tools": 8, "backend_cairo": 10, "backend_qt": 6, "qt_compat": 6, "matplotlib.backends.qt_editor.figureoptions": 2, "backend_gtk3": 4, "_backend_tk": 4, "backend_agg": 10, "cairo": 8, "backend_gtk4": 4, "backend_qtagg": 2, "cairocffi": 2, "matplotlib._afm": 4, "matplotlib.backends.backend_mixed": 8, "encodings": 4, "wx": 6, "backend_wx": 4, "matplotlib.backends.backend_pdf": 6, "matplotlib._tight_bbox": 2, "_backend_gtk": 4, "wx.lib.wxcairo": 2, "ipykernel.comm": 2, "backend_webagg_core": 4, "wx.svg": 2, "backend_qtcairo": 2, "_tkagg": 2, "shiboken6": 2, "sip": 2, "shiboken2": 2, "PySide2": 2, "_afm": 2, "tornado.websocket": 12, "tornado.template": 8, "matplotlib._mathtext_data": 2, "matplotlib.backends._backend_agg": 2, "matplotlib.testing.decorators": 122, "matplotlib.testing.compare": 8, "matplotlib.testing": 42, "matplotlib.testing._markers": 22, "matplotlib.backends.backend_tkagg": 4, "matplotlib.backends.backend_webagg_core": 2, "matplotlib.backends._backend_pdf_ps": 2, "matplotlib.backends.backend_agg": 9, "matplotlib.backends.backend_pgf": 2, "matplotlib.testing.exceptions": 4, "matplotlib.testing.conftest": 8, "matplotlib._type1font": 2, "matplotlib.animation": 2, "matplotlib.testing.jpl_units": 18, "matplotlib.backends.backend_qt5agg": 2, "matplotlib.backends.backend_qt5cairo": 2, "matplotlib.backends.backend_qt5": 4, "matplotlib.backends.backend_qt": 4, "matplotlib.textpath": 2, "PIL.TiffTags": 2, "matplotlib.sankey": 2, "matplotlib.dviread": 2, "matplotlib.testing.widgets": 4, "matplotlib.style.core": 2, "numpy.ma.testutils": 20, "matplotlib.backends.backend_template": 2, "matplotlib.backends.qt_compat": 6, "matplotlib.backends.qt_editor": 4, "matplotlib.backends.backend_qtagg": 2, "mpl_toolkits.mplot3d": 10, "mpl_toolkits.axisartist": 12, "matplotlib.projections.geo": 2, "matplotlib.projections.polar": 4, "matplotlib.legend_handler": 2, "sphinx_gallery": 2, "filecmp": 2, "xml.parsers.expat": 8, "deprecation": 4, "geo": 2, "polar": 2, "matplotlib.style": 2, "compare": 2, "Duration": 2, "Epoch": 2, "EpochConverter": 2, "StrConverter": 2, "UnitDbl": 2, "UnitDblConverter": 2, "UnitDblFormatter": 2, "blib2to3.pgen2.driver": 1, "dot_command": 2, "execute": 2, "mixins": 6, "piping": 2, "rendering": 2, "unflattening": 2, "upstream_version": 2, "viewing": 2, "engines": 2, "formats": 3, "formatters": 10, "renderers": 4, "radon.cli.colors": 8, "radon.cli.harvest": 8, "radon.cli.tools": 6, "radon.tests.test_cli_harvest": 4, "radon.complexity": 14, "radon.contrib.flake8": 2, "test_complexity_visitor": 2, "radon.metrics": 4, "xml.etree.cElementTree": 4, "nbformat": 4, "mando": 10, "mypy.dmypy_os": 2, "mypy.dmypy_server": 6, "mypy.test.data": 60, "mypy.test.helpers": 68, "mypy.test.typefixture": 10, "mypy.test.config": 56, "mypy.server.mergecheck": 2, "mypy.test.testfinegrained": 2, "mypy.api": 6, "mypy.server.astdiff": 4, "mypy.stubgen": 2, "mypy.test.visitors": 4, "mypy.stubtest": 4, "mypy.server.subexpr": 2, "mypy.test.update_data": 2, "mypy.plugins.functools": 2, "mypy.server.astmerge": 2, "mypy.server.objgraph": 2, "mypy.test.meta._pytest": 4, "slack": 2, "telegram": 2, "discord": 2, "utils_worker": 6, "requests.utils": 2, "slack_sdk": 2, "type_variable_operators": 1, "narwhals.stable": 2, "narwhals._polars.series": 8, "narwhals._polars.group_by": 2, "narwhals._duckdb.dataframe": 17, "narwhals._dask.dataframe": 16, "narwhals._arrow.dataframe": 25, "narwhals._polars.dataframe": 23, "narwhals._polars.expr": 11, "polars.dataframe.group_by": 2, "polars.lazyframe.group_by": 2, "narwhals._polars.typing": 2, "dask.dataframe.dask_expr": 14, "dask_expr": 6, "narwhals._dask.expr": 14, "narwhals._dask.utils": 6, "narwhals._dask.group_by": 2, "narwhals._compliant.group_by": 10, "dask_expr._groupby": 1, "narwhals._compliant.namespace": 12, "narwhals._dask.selectors": 2, "narwhals._compliant.expr": 13, "narwhals._dask.expr_dt": 2, "narwhals._dask.expr_str": 2, "narwhals._pandas_like.group_by": 6, "narwhals._arrow.series": 22, "narwhals._arrow.expr": 10, "narwhals._arrow.group_by": 2, "narwhals._arrow.series_cat": 2, "narwhals._arrow.series_dt": 2, "narwhals._arrow.series_list": 2, "narwhals._arrow.series_str": 2, "narwhals._arrow.series_struct": 2, "narwhals._compliant.series": 10, "narwhals._arrow.selectors": 2, "pyarrow._stubs_typing": 2, "narwhals._compliant.any_namespace": 14, "narwhals._compliant.dataframe": 6, "narwhals._compliant.selectors": 4, "narwhals._compliant.when_then": 4, "narwhals._duckdb.utils": 15, "narwhals._duckdb.expr": 18, "narwhals._duckdb.group_by": 2, "narwhals._duckdb.series": 2, "duckdb.typing": 7, "narwhals._duckdb.selectors": 2, "narwhals._duckdb.expr_dt": 2, "narwhals._duckdb.expr_list": 2, "narwhals._duckdb.expr_str": 2, "narwhals._duckdb.expr_struct": 2, "narwhals._duckdb.typing": 1, "ibis.selectors": 2, "narwhals._ibis.series": 2, "narwhals._pandas_like.expr": 12, "modin.pandas.utils": 2, "narwhals._pandas_like.series_cat": 2, "narwhals._pandas_like.series_dt": 2, "narwhals._pandas_like.series_list": 2, "narwhals._pandas_like.series_str": 2, "narwhals._pandas_like.series_struct": 2, "narwhals._pandas_like.selectors": 2, "narwhals._pandas_like.typing": 2, "narwhals._spark_like.expr": 18, "sqlframe.base.column": 19, "narwhals._spark_like.utils": 9, "narwhals._spark_like.group_by": 2, "sqlframe.base.window": 4, "sqlframe.base": 5, "sqlframe.base.functions": 1, "sqlframe.base.types": 2, "pyspark.pandas.spark.functions": 1, "sqlframe.base.session": 2, "narwhals._spark_like.selectors": 2, "narwhals._spark_like.expr_dt": 2, "narwhals._spark_like.expr_list": 2, "narwhals._spark_like.expr_str": 2, "narwhals._spark_like.expr_struct": 2, "narwhals._spark_like.typing": 1, "narwhals.stable.v1.dtypes": 2, "narwhals.selectors": 2, "narwhals.stable.v1._dtypes": 2, "pyperf": 22, "jsonschema.tests._suite": 6, "atheris": 2, "curio": 2, "pydantic.dataclasses": 30, "specification": 4, "config.schemas.v3_0": 4, "ecosystem": 2, "report.schemas.v3_0": 14, "telemetry": 6, "file": 6, "package": 8, "project": 4, "result": 4, "scan": 2, "vulnerability": 8, "dparse.dependencies": 6, "config_protocol": 2, "report_protocol": 2, "policy_file": 2, "events.payloads": 2, "context": 4, "onboarding": 2, "freeze": 1, "json_tree": 1, "mermaid": 1, "pipdeptree._models.dag": 1, "pipdeptree._freeze": 1, "dag": 1, "joblib.test.common": 22, "joblib.testing": 30, "joblib._utils": 2, "joblib.disk": 2, "joblib._memmapping_reducer": 6, "joblib.executor": 2, "joblib.parallel": 10, "joblib.pool": 2, "joblib.compressor": 4, "joblib.numpy_pickle_utils": 2, "joblib.test": 2, "joblib.memory": 8, "test_memory": 2, "posix": 2, "joblib._multiprocessing_helpers": 4, "joblib.externals.loky": 2, "_openmp_test_helper.parallel_sum": 2, "joblib._parallel_backends": 5, "joblib.externals.loky.process_executor": 2, "threadpoolctl": 3, "memory_profiler": 2, "joblib.func_inspect": 4, "joblib.test.test_func_inspect_special_encoding": 2, "joblib._store_backends": 4, "distributed.metrics": 2, "distributed.utils_test": 2, "joblib._dask": 2, "joblib.test.test_parallel": 2, "tmp_joblib_": 2, "joblib.hashing": 4, "joblib.logger": 2, "opcode": 2, "_collections_abc": 2, "cloudpickle": 2, "joblib.externals.cloudpickle": 4, "concurrent.futures._base": 4, "backend.context": 6, "process_executor": 4, "_base": 7, "backend.reduction": 4, "cloudpickle_wrapper": 2, "reusable_executor": 2, "viztracer": 4, "concurrent.futures.process": 4, "multiprocessing.connection": 6, "backend.queues": 2, "backend.utils": 2, "initializers": 2, "reduction": 7, "fork_exec": 3, "_posix_reduction": 2, "joblib.externals": 2, "process": 2, "queues": 2, "synchronize": 2, "multiprocessing.popen_spawn_win32": 2, "resource_tracker": 2, "multiprocessing.resource_tracker": 3, "popen_loky_win32": 2, "popen_loky_posix": 2, "asgi": 2, "default": 2, "wsgi": 2, "socksio": 6, "contourpy.util.mpl_util": 2, "contourpy.util.renderer": 4, "contourpy.util._build_config": 2, "bokeh.io": 2, "bokeh.io.export": 2, "bokeh.layouts": 2, "bokeh.models.annotations.labels": 2, "bokeh.palettes": 2, "bokeh.plotting": 2, "contourpy.util.bokeh_util": 2, "bokeh.core.enums": 2, "bokeh.models": 2, "selenium.webdriver.remote.webdriver": 2, "smmap.mman": 4, "smmap.util": 4, "smmap.buf": 2, "smmap.test.lib": 2, "tornado.test.util": 30, "tornado.testing": 51, "tornado.auth": 2, "tornado.platform.twisted": 3, "twisted.internet.defer": 4, "tornado.wsgi": 4, "wsgiref.validate": 2, "tornado.test": 4, "tornado.curl_httpclient": 2, "tornado.test.httpclient_test": 2, "pycares": 4, "twisted": 1, "twisted.names": 1, "tornado.platform.caresresolver": 2, "tornado.locale": 2, "tornado.test.runtests": 2, "twisted.internet.abstract": 1, "twisted.internet.asyncioreactor": 3, "twisted.names.cache": 1, "twisted.names.client": 1, "twisted.names.hosts": 1, "twisted.names.resolve": 1, "twisted.python": 2, "pip._internal.exceptions": 110, "pip._internal.utils": 12, "pip._internal.utils.compat": 38, "pip._internal.utils.logging": 39, "pip._internal.utils.misc": 108, "pip._vendor": 35, "pip._internal.utils.packaging": 18, "pip._vendor.packaging.requirements": 29, "pip._internal.models.link": 34, "pip._internal.models.wheel": 17, "pip._internal.utils.temp_dir": 33, "pip._vendor.packaging.tags": 12, "pip._vendor.packaging.utils": 62, "pip._vendor.rich.console": 52, "pip._vendor.rich.markup": 6, "pip._vendor.rich.text": 14, "pip._internal.metadata": 53, "pip._internal.req.req_install": 44, "pip._vendor.requests.models": 14, "pip._internal.utils.hashes": 18, "pip._internal.utils._log": 8, "pip._internal.cache": 15, "pip._internal.operations.build.wheel": 2, "pip._internal.operations.build.wheel_editable": 2, "pip._internal.operations.build.wheel_legacy": 2, "pip._internal.utils.setuptools_build": 8, "pip._internal.utils.subprocess": 32, "pip._internal.vcs": 18, "pip._internal.index.collector": 12, "pip._internal.index.package_finder": 28, "pip._internal.models.selection_prefs": 10, "pip._internal.network.session": 22, "pip._internal.utils.filesystem": 10, "pip._internal.cli.spinners": 12, "pip._internal.locations": 18, "pip._vendor.urllib3.util.ssl_": 4, "_ssl_constants": 6, "_macos": 2, "_openssl": 2, "pip._vendor.typing_extensions": 25, "fallback": 2, "_cmsgpack": 2, "__pypy__.builders": 2, "pip._vendor.pygments.styles": 5, "pip._vendor.pygments.util": 29, "pip._vendor.pygments.token": 18, "pip._vendor.pygments.lexers": 7, "pip._vendor.pygments.lexers._mapping": 4, "pip._vendor.pygments.formatters": 3, "pip._vendor.pygments.filters": 5, "pip._vendor.pygments": 3, "pip._vendor.pygments.formatters.latex": 1, "pip._vendor.pygments.formatters.terminal": 1, "pip._vendor.pygments.formatters.terminal256": 1, "pip._vendor.pygments.lexers.special": 1, "pip._vendor.pygments.lexer": 7, "pip._vendor.pygments.formatter": 14, "pip._vendor.pygments.filter": 4, "pip._vendor.pygments.regexopt": 2, "pip._vendor.pygments.cmdline": 2, "database": 4, "cgi": 4, "_abcoll": 2, "logging.config": 4, "_osx_support": 2, "_aix_support": 2, "_frozen_importlib_external": 2, "_frozen_importlib": 2, "java": 2, "resources": 2, "distro": 4, "pip._vendor.requests.structures": 6, "pip._vendor.urllib3": 8, "pip._vendor.requests": 9, "pip._vendor.cachecontrol.adapter": 6, "pip._vendor.cachecontrol.cache": 14, "pip._vendor.cachecontrol.controller": 10, "pip._vendor.cachecontrol.heuristics": 4, "pip._vendor.cachecontrol.serialize": 6, "pip._vendor.cachecontrol.filewrapper": 2, "pip._vendor.requests.adapters": 4, "pip._vendor.cachecontrol.wrapper": 2, "pip._vendor.urllib3.exceptions": 11, "pip._vendor.urllib3.fields": 2, "pip._vendor.urllib3.filepost": 2, "pip._vendor.urllib3.util": 8, "pip._vendor.certifi": 6, "pip._vendor.urllib3.contrib": 4, "pip._vendor.urllib3.poolmanager": 4, "pip._vendor.urllib3.util.retry": 2, "pip._vendor.urllib3.contrib.socks": 2, "_in_process": 2, "pip._vendor.rich._null_file": 4, "pip._vendor.rich.markdown": 4, "pip._vendor.rich.panel": 12, "pip._vendor.rich.syntax": 6, "pip._vendor.rich.table": 13, "pip._vendor.rich.json": 2, "pip._vendor.rich._win32_console": 6, "pip._vendor.rich._windows_renderer": 2, "pip._vendor.rich.cells": 2, "pip._vendor.rich.columns": 4, "pip._vendor.rich.highlighter": 6, "pip._vendor.rich.tree": 2, "pip._vendor.rich.styled": 2, "pip._vendor.rich": 26, "pip._vendor.rich._inspect": 2, "pip._vendor.rich.color": 6, "pip._vendor.rich.style": 8, "pip._vendor.rich.segment": 8, "pip._vendor.rich.repr": 2, "pip._vendor.rich.pretty": 6, "pip._vendor.pygments.style": 4, "pip._vendor.rich.containers": 2, "pip._vendor.rich.padding": 2, "pip._vendor.rich.measure": 2, "pip._vendor.rich.traceback": 2, "packages": 32, "packages.six": 8, "packages.six.moves.urllib.parse": 6, "urllib3_secure_extra": 2, "packages.six.moves.http_client": 6, "packages.six.moves": 6, "util.queue": 2, "packages.backports.weakref_finalize": 2, "pip._internal.utils._jaraco_text": 2, "pip._vendor.packaging": 8, "pip._vendor.platformdirs": 4, "providers": 5, "structs": 8, "reporters": 4, "resolvers": 3, "pip._vendor.platformdirs.windows": 2, "pip._vendor.platformdirs.macos": 2, "pip._vendor.platformdirs.unix": 2, "pip._vendor.platformdirs.android": 2, "contrib": 2, "_securetransport.bindings": 2, "_securetransport.low_level": 2, "packages.backports.makefile": 4, "cryptography.hazmat.backends.openssl": 2, "google.appengine.api": 2, "ntlm": 2, "pip._vendor.cachecontrol.caches.file_cache": 2, "pip._vendor.cachecontrol.caches.redis_cache": 2, "pip._vendor.pygments.plugin": 8, "pip._vendor.pygments.modeline": 2, "pip._vendor.pygments.console": 3, "ctags": 3, "pip._vendor.pygments.formatters._mapping": 2, "pip._vendor.pygments.styles._mapping": 2, "pip._vendor.packaging.licenses._spdx": 2, "keyring": 2, "pip._internal.vcs.versioncontrol": 12, "pip._vendor.requests.auth": 2, "pip._vendor.requests.utils": 2, "pip._internal.network.utils": 10, "pip._internal.cli.progress_bars": 3, "pip._internal.models.index": 10, "pip._internal.network.cache": 4, "pip._internal.network.auth": 2, "pip._internal.utils.glibc": 2, "pip._vendor.cachecontrol": 2, "pip._vendor.urllib3.connectionpool": 2, "pip._vendor.cachecontrol.caches": 2, "pip._internal.utils.deprecation": 14, "pip._vendor.rich.logging": 2, "pip._internal.utils.retry": 4, "pip._internal.utils.virtualenv": 22, "pip._vendor.pyproject_hooks": 12, "pip._internal.utils.filetypes": 12, "pip._internal.models.format_control": 6, "pip._internal.utils.compatibility_tags": 8, "pip._vendor.packaging.markers": 8, "pip._internal.cli.parser": 6, "pip._internal.models.target_python": 8, "pip._internal.cli.base_command": 28, "pip._internal.cli.command_context": 4, "pip._internal.self_outdated_check": 4, "pip._internal.cli.status_codes": 39, "pip._internal.configuration": 6, "pip._internal.cli.main_parser": 4, "pip._internal.commands": 8, "pip._internal.cli": 27, "pip._internal.build_env": 14, "pip._vendor.rich.progress": 2, "pip._internal.cli.autocompletion": 2, "pip._internal.resolution.resolvelib.resolver": 2, "pip._internal.resolution.legacy.resolver": 2, "pip._internal.cli.index_command": 6, "pip._internal.operations.build.build_tracker": 11, "pip._internal.operations.prepare": 8, "pip._internal.req.constructors": 14, "pip._internal.req.req_file": 6, "pip._internal.resolution.base": 8, "pip._internal.distributions": 4, "pip._internal.metadata.base": 11, "pip._internal.utils.direct_url_helpers": 8, "pip._internal.distributions.installed": 2, "pip._internal.network.download": 2, "pip._internal.network.lazy_wheel": 2, "pip._internal.utils.unpacking": 8, "pip._internal.operations.build.metadata": 2, "pip._internal.operations.build.metadata_editable": 2, "pip._internal.operations.build.metadata_legacy": 2, "pip._internal.operations.install.editable_legacy": 2, "pip._internal.operations.install.wheel": 2, "pip._internal.pyproject": 2, "pip._internal.req.req_uninstall": 2, "pip._vendor.packaging.specifiers": 12, "req_file": 2, "req_install": 2, "req_set": 2, "pip._internal.models.search_scope": 6, "pip._internal.req.req_set": 6, "pip._internal.vcs.bazaar": 2, "pip._internal.vcs.git": 2, "pip._internal.vcs.mercurial": 2, "pip._internal.vcs.subversion": 2, "pip._internal.models.scheme": 8, "pip._vendor.requests.exceptions": 3, "pip._internal.models.candidate": 4, "pip._internal.req": 8, "pip._internal.operations.check": 4, "pip._internal.cli.req_command": 11, "pip._internal.commands.search": 2, "pip._internal.cli.cmdoptions": 6, "pip._internal.network.xmlrpc": 2, "pip._internal.models.installation_report": 2, "pip._internal.wheel_builder": 4, "pip._internal.utils.wheel": 6, "_json": 4, "pip._internal.distributions.base": 8, "pip._internal.distributions.sdist": 2, "pip._internal.distributions.wheel": 2, "_dists": 4, "_envs": 2, "pip._vendor.pkg_resources": 1, "pip._vendor.resolvelib.providers": 2, "candidates": 4, "factory": 10, "pip._vendor.resolvelib.resolvers": 4, "pip._vendor.resolvelib.reporters": 2, "pip._vendor.resolvelib": 4, "found_candidates": 2, "requirements": 7, "pip._internal.resolution.resolvelib.provider": 2, "pip._internal.resolution.resolvelib.reporter": 2, "pip._vendor.resolvelib.structs": 2, "compileall": 2, "pip._vendor.distlib.scripts": 2, "pip._vendor.distlib.util": 2, "_resources": 8, "_tasks": 16, "from_thread": 4, "_sockets": 4, "_subprocesses": 4, "_testing": 6, "_eventloop": 14, "_streams": 6, "trio.from_thread": 2, "trio.lowlevel": 4, "outcome": 2, "trio.socket": 2, "trio.to_thread": 2, "abc._eventloop": 4, "streams.memory": 6, "trio.testing": 2, "asyncio.base_events": 2, "anyio._core._asyncio_selector_thread": 2, "selectors": 5, "streams.stapled": 2, "streams.tls": 2, "_synchronization": 28, "mando.napoleon.docstring": 2, "mando.napoleon.iterators": 2, "mando.napoleon.pycompat": 2, "sphinx.ext.napoleon": 2, "rich.live": 3, "safety.events.utils.emission": 6, "safety.init.constants": 4, "safety.init.main": 6, "safety.init.models": 2, "safety.scan.init_scan": 3, "safety_schemas.models.events.payloads": 12, "tool.main": 4, "render": 2, "safety.init.render": 3, "safety.scan.render": 8, "safety.scan.util": 10, "auth.utils": 4, "tool": 2, "safety.auth.main": 4, "authlib.integrations.base_client": 6, "cli_utils": 2, "safety.auth.server": 2, "authlib.integrations.base_client.errors": 2, "authlib.integrations.requests_client": 4, "requests.adapters": 2, "safety.safety": 6, "safety.formatters.schemas.zero_five": 2, "obj": 2, "tools": 2, "vulnerabilities": 2, "safety.events.handlers": 6, "safety_schemas.models.events": 20, "safety.scan.decorators": 2, "safety.scan.finder.file_finder": 2, "safety.scan.finder.handlers": 4, "safety.scan.fun_mode.easter_eggs": 2, "safety.scan.validators": 4, "spdx_tools.spdx.model": 2, "spdx_tools.spdx.model.spdx_no_assertion": 2, "spdx_tools.spdx.writer.write_utils": 2, "spdx_tools.spdx": 2, "ecosystems.base": 2, "ecosystems.target": 2, "init.main": 2, "tool.interceptors": 2, "github": 2, "handlers": 15, "tool_inspector": 2, "definitions": 2, "safety.tool.pip": 2, "safety.tool.poetry": 2, "safety.tool.uv.main": 2, "safety.tool.utils": 3, "interceptors": 2, "safety.tool.constants": 9, "safety.tool.typosquatting": 2, "environment_diff": 6, "intents": 8, "scan.util": 2, "unix": 2, "windows": 2, "safety.tool.resolver": 4, "safety.tool.intents": 2, "safety.tool.pip.parser": 2, "pip.command": 2, "safety.tool.poetry.parser": 1, "creation": 4, "emission": 2, "safety_schemas.models.events.context": 2, "event_bus": 2, "types.base": 2, "conditions": 2, "data": 4, "safety.init.types": 2, "safety.events.utils.context": 2, "bus": 4, "safety.events.types": 6, "safety.firewall.events.utils": 2, "safety.scan.fun_mode.celebration_effects": 2, "file_finder": 2, "python.main": 2, "python.dependencies": 2, "safety_schemas.models.package": 2, "common.schemas": 2, "scans.schemas.base": 2, "common.const": 2, "common.exceptions": 2, "pydantic.validators": 2, "charset_normalizer.md": 2, "charset_normalizer.models": 2, "charset_normalizer.version": 2, "_convertions": 2, "numpy._core.strings": 4, "pickle5": 2, "_utils._inspect": 4, "py3k": 2, "numpy._core.defchararray": 2, "numpy.version": 6, "_linalg": 2, "numpy._globals": 6, "numpy._typing": 8, "numpy._utils": 34, "numpy.lib._twodim_base_impl": 6, "numpy.lib._function_base_impl": 12, "numpy.lib._index_tricks_impl": 8, "numpy._core.umath": 22, "extras": 2, "numpy._core.numerictypes": 24, "numpy._core.numeric": 50, "numpy._utils._inspect": 2, "numpy._core.fromnumeric": 12, "_multiarray_umath": 6, "multiarray": 14, "numpy._core.function_base": 8, "numpy._core.overrides": 30, "numpy.lib._stride_tricks_impl": 10, "overrides": 6, "arrayprint": 4, "numerictypes": 8, "einsumfunc": 2, "fromnumeric": 8, "function_base": 2, "getlimits": 2, "memmap": 2, "numeric": 12, "records": 2, "shape_base": 2, "_ufunc_config": 4, "_machar": 2, "umath": 8, "_ctypes": 2, "_asarray": 2, "_dtype": 2, "_string_helpers": 2, "_type_aliases": 2, "code_generators.genapi": 2, "code_generators.numpy_api": 2, "printoptions": 2, "_nbit_base": 6, "_array_like": 4, "_char_codes": 4, "_dtype_like": 2, "_nbit": 2, "_nested_sequence": 4, "_scalars": 2, "_shape": 24, "_ufunc": 2, "numpy.lib": 20, "PyInstaller.compat": 2, "PyInstaller.utils.hooks": 2, "numpy._core.records": 4, "numpy._typing._add_docstring": 2, "capi_maps": 4, "auxfuncs": 16, "crackfortran": 6, "fileinput": 3, "numpy.f2py._backends": 2, "cfuncs": 4, "numpy.distutils.system_info": 4, "_isocbind": 4, "numpy_distutils": 2, "numpy_distutils.command.build_flib": 2, "numpy.distutils.fcompiler": 2, "numpy_distutils.fcompiler": 2, "numpy.distutils.cpuinfo": 2, "numpy_distutils.command.cpuinfo": 2, "numpy_distutils.cpuinfo": 2, "numpy.f2py.f2py2e": 4, "_private": 2, "_private.utils": 2, "numpy.lib._type_check_impl": 4, "numpy.ma.mrecords": 8, "numpy.lib._iotools": 6, "_scimath_impl": 2, "_array_utils_impl": 2, "_ufunclike_impl": 2, "_user_array_impl": 2, "_arrayterator_impl": 2, "numpy.lib._utils_impl": 8, "_npyio_impl": 2, "numpy._core.shape_base": 4, "_stride_tricks_impl": 2, "_datasource": 2, "_iotools": 2, "numpy.matrixlib": 2, "numpy.lib._histograms_impl": 2, "_helper": 2, "_pocketfft": 2, "_generator": 4, "_mt19937": 4, "_pcg64": 4, "_philox": 4, "_sfc64": 4, "bit_generator": 4, "mtrand": 4, "defmatrix": 2, "_polybase": 14, "polynomial": 12, "chebyshev": 2, "hermite": 2, "hermite_e": 2, "laguerre": 2, "legendre": 2, "numpy.polynomial.chebyshev": 2, "numpy.polynomial.polynomial": 12, "numpy.polynomial.hermite_e": 2, "numpy.polynomial.polyutils": 4, "numpy.polynomial.laguerre": 2, "numpy.polynomial.legendre": 2, "numpy.polynomial.hermite": 2, "numpy.linalg.tests.test_linalg": 2, "numpy.ma.core": 12, "numpy.ma.extras": 4, "numpy.random._common": 2, "numpy.random.bit_generator": 2, "Cython.Compiler.Version": 6, "numpy.random._examples.numba": 2, "numpy.random._examples.cffi": 2, "numpy.lib._npyio_impl": 4, "numpy.lib._arraypad_impl": 2, "numpy.lib._datasource": 2, "numpy._core._rational_tests": 20, "numpy.lib.recfunctions": 6, "numpy.lib._nanfunctions_impl": 2, "numpy.linalg._umath_linalg": 4, "win32pdh": 2, "unittest.case": 2, "numpy.distutils.misc_util": 4, "_meson": 2, "_distutils": 2, "numpy.distutils.core": 2, "_backend": 4, "numpy.f2py.tests": 2, "numpy.f2py._src_pyf": 2, "numpy.f2py.crackfortran": 6, "numpy.f2py.auxfuncs": 2, "numpy._core._type_aliases": 2, "numpy.f2py.symbolic": 2, "numpy.f2py._backends._meson": 2, "numpy.typing.mypy_plugin": 2, "numpy.lib.user_array": 2, "limited_api1": 2, "limited_api2": 2, "limited_api_latest": 2, "numpy._core._machar": 2, "numpy._core.arrayprint": 4, "numpy._core.tests._locales": 6, "array_interface_testing": 2, "numpy._core._struct_ufunc_tests": 4, "numpy.lib._shape_base_impl": 2, "numpy.lib.tests.test_io": 2, "mem_policy": 2, "numpy._core._simd": 4, "checks": 4, "numpy._core._operand_flag_tests": 2, "numpy._core._umath_tests": 2, "numpy._core.getlimits": 2, "numpy.testing.overrides": 2, "_testbuffer": 2, "Cython": 2, "Cython.Build": 2, "numpy.lib.mixins": 2, "numpy.linalg.lapack_lite": 2, "numpy.linalg._linalg": 2, "bandit": 66, "bandit.core.utils": 2, "bandit.plugins": 2, "bandit.formatters": 4, "sarif_om": 2, "jschema_to_python.to_json": 2, "bandit.blacklists": 4, "pipreqs": 1, "pip_api": 1, "tomli._parser": 1, "parasite_axes": 4, "axes_divider": 6, "mpl_axes": 6, "axes_grid": 2, "axes3d": 2, "axis_artist": 8, "axisline_style": 4, "mpl_toolkits.axes_grid1.parasite_axes": 8, "axislines": 6, "floating_axes": 2, "grid_helper_curvelinear": 2, "mpl_toolkits.axisartist.grid_finder": 6, "grid_finder": 4, "mpl_toolkits.axes_grid1.axes_divider": 4, "mpl_toolkits.axisartist.axis_artist": 2, "mpl_toolkits.axisartist.angle_helper": 2, "mpl_toolkits.axisartist.axislines": 6, "mpl_toolkits.axisartist.grid_helper_curvelinear": 2, "mpl_toolkits.axisartist.floating_axes": 2, "mpl_toolkits.mplot3d.art3d": 2, "mpl_toolkits.mplot3d.axes3d": 2, "mpl_toolkits.axes_grid1.mpl_axes": 2, "mpl_toolkits.axes_grid1.anchored_artists": 2, "mpl_toolkits.axes_grid1.axes_rgb": 2, "libcst._nodes.internal": 20, "libcst._type_enforce": 4, "libcst._nodes.deep_equals": 8, "libcst.metadata.type_inference_provider": 8, "libcst.testing.utils": 228, "libcst.codemod.visitors": 32, "hypothesmith": 2, "libcst.codegen.gather": 6, "libcst.codegen.gen_visitor_functions": 4, "libcst.codegen.gen_matcher_classes": 4, "libcst.codegen.gen_type_mapping": 4, "libcst.codegen.transforms": 2, "libcst._parser.wrapped_tokenize": 4, "libcst._parser.parso.python.tokenize": 8, "libcst._parser.types.whitespace_state": 12, "libcst_native": 12, "libcst._parser.conversions.expression": 2, "libcst._parser.conversions.module": 2, "libcst._parser.conversions.params": 2, "libcst._parser.conversions.statement": 2, "libcst._parser.conversions.terminals": 2, "libcst._parser.production_decorator": 10, "libcst._parser.types.conversions": 6, "libcst._parser.types.production": 4, "libcst._parser.base_parser": 2, "libcst._parser.grammar": 6, "libcst._parser": 2, "libcst._parser.detect_config": 4, "libcst._parser.python_parser": 2, "libcst.native": 2, "libcst.display.graphviz": 2, "libcst.matchers._decorators": 4, "libcst.matchers._matcher_base": 6, "libcst.matchers._visitors": 2, "libcst.matchers._return_types": 2, "libcst._position": 6, "libcst.metadata.position_provider": 8, "libcst.metadata.accessor_provider": 2, "libcst.metadata.expression_context_provider": 4, "libcst.metadata.file_path_provider": 2, "libcst.metadata.full_repo_manager": 6, "libcst.metadata.name_provider": 4, "libcst.metadata.parent_node_provider": 2, "libcst.metadata.reentrant_codegen": 2, "libcst.metadata.scope_provider": 8, "libcst.metadata.span_provider": 4, "libcst.helpers.module": 6, "libcst.helpers._template": 2, "libcst.helpers.common": 8, "libcst.helpers.expression": 4, "libcst.helpers.node_fields": 2, "libcst.codemod._context": 32, "libcst.codemod._codemod": 12, "libcst.codemod._visitor": 24, "libcst.codemod.visitors._add_imports": 7, "libcst.codemod.visitors._remove_imports": 4, "libcst.codemod._dummy_pool": 2, "libcst.codemod._runner": 6, "libcst.codemod._cli": 2, "libcst.codemod._command": 2, "libcst.codemod._testing": 2, "libcst.codemod.visitors._gather_exports": 4, "libcst.codemod.visitors._gather_string_annotation_names": 4, "libcst.codemod.visitors._apply_type_annotations": 4, "libcst.codemod.visitors._gather_comments": 2, "libcst.codemod.visitors._gather_global_names": 4, "libcst.codemod.visitors._gather_imports": 7, "libcst.codemod.visitors._gather_unused_imports": 4, "libcst.codemod.visitors._imports": 8, "libcst.codemod.commands.rename": 4, "libcst.codemod.commands.unnecessary_format_string": 2, "libcst.codemod.commands.fix_pyre_directives": 2, "libcst.codemod.commands.remove_unused_imports": 2, "libcst.codemod.commands.convert_type_comments": 2, "libcst.codemod.commands.convert_format_to_fstring": 2, "libcst.codemod.commands.convert_union_to_or": 2, "libcst.codemod.commands.rename_typing_generic_aliases": 2, "libcst.codemod.commands.convert_percent_format_to_fstring": 2, "libcst.codemod.commands.ensure_import_present": 2, "libcst.codemod.commands.noop": 2, "libcst.codemod.commands.convert_namedtuple_to_dataclass": 2, "libcst.codemod.commands.fix_variadic_callable": 2, "libcst.codemod.commands.add_trailing_commas": 2, "libcst.codemod.commands.strip_strings_from_types": 2, "libcst.codemod.commands.remove_pyre_directive": 2, "libcst.codemod.commands.add_pyre_directive": 2, "libcst.helpers.paths": 4, "libcst.metadata.tests.test_type_inference_provider": 2, "libcst.tests.test_pyre_integration": 2, "libcst.tool": 2, "libcst._parser.types": 4, "libcst._parser.types.py_token": 2, "libcst._parser.custom_itertools": 6, "libcst._parser.types.partials": 8, "libcst._parser.whitespace_parser": 10, "libcst._parser.parso.python.py_token": 2, "libcst._parser.parso.pgen2.grammar_parser": 2, "libcst.codegen.generate": 2, "libcst._nodes.tests.base": 102, "tenacity.stop": 2, "tenacity.wait": 2, "altair.expr.core": 6, "altair.vegalite.v5.schema._typing": 6, "altair.utils._vegafusion_data": 10, "altair.vegalite.v5.data": 4, "mimebundle": 4, "vegafusion": 2, "vl_convert": 2, "altair.typing": 10, "altair.utils._importers": 10, "plugin_registry": 7, "schemapi": 4, "altair.utils.schemapi": 12, "narwhals.stable.v1.typing": 6, "altair.utils._dfi_types": 2, "altair.utils.data": 8, "altair.vegalite.v5.schema": 6, "altair.utils.core": 8, "altair.vegalite.data": 4, "vegafusion.runtime": 2, "_vegafusion_data": 2, "altair.vegalite.v5.display": 4, "_importers": 4, "altair.utils.display": 6, "v5": 2, "v5.api": 2, "v5.schema": 2, "altair.vegalite.v5.api": 6, "altair.vegalite.v5.schema.channels": 4, "anywidget": 5, "altair.utils.selection": 2, "jupyter_chart": 2, "altair.utils.compiler": 2, "altair.vegalite.v5": 2, "altair.vegalite.v5.compiler": 2, "altair.utils.mimebundle": 2, "altair.vegalite.display": 2, "schema": 4, "altair.utils._show": 2, "display": 2, "schema._typing": 2, "schema._config": 2, "schema.channels": 2, "schema.core": 2, "altair.utils.save": 2, "altair.utils.server": 2, "altair.utils._transformed_data": 2, "fontTools.cu2qu": 6, "qu2cu": 4, "fontTools.misc": 116, "fontTools.misc.bezierTools": 10, "fontTools.cu2qu.benchmark": 2, "fontTools.pens.qu2cuPen": 2, "fontTools.pens.ttGlyphPen": 4, "fontTools.misc.roundTools": 50, "fontTools.misc.vector": 14, "psOperators": 2, "fontTools.misc.fixedTools": 58, "fontTools.pens.basePen": 58, "roundTools": 2, "fontTools.encodings.StandardEncoding": 8, "textTools": 2, "fontTools.encodings.codecs": 2, "fontTools.config": 10, "fontTools.misc.xmlWriter": 2, "xattr": 2, "fontTools.pens.boundsPen": 8, "fontTools.ttLib.tables.DefaultTable": 12, "fontTools.misc.arrayTools": 18, "fontTools.misc.transform": 26, "cu2qu": 4, "fontTools.pens.pointPen": 22, "fontTools.pens.reverseContourPen": 8, "ufoLib2": 2, "defcon": 2, "ufo": 2, "fontTools.subset.util": 6, "fontTools.otlLib.maxContextCalc": 4, "fontTools.subset.cff": 4, "fontTools.subset.svg": 2, "fontTools.ttLib.tables": 50, "fontTools.ttLib.tables._n_a_m_e": 2, "fontTools.ttLib.tables.otBase": 18, "fontTools.varLib": 18, "fontTools.colorLib.unbuilder": 4, "fontTools.ttLib.tables.S_V_G_": 2, "fontTools.subset": 2, "fontTools.voltLib.ast": 2, "fontTools.voltLib.error": 6, "fontTools.voltLib.lexer": 2, "fontTools.feaLib": 2, "fontTools.voltLib": 2, "fontTools.voltLib.parser": 2, "fontTools.merge.unicode": 2, "fontTools.pens.recordingPen": 12, "fontTools.merge.base": 6, "fontTools.merge.util": 4, "fontTools.merge.tables": 2, "fontTools.merge.cmap": 4, "fontTools.merge.layout": 2, "fontTools.merge.options": 2, "fontTools.misc.psCharStrings": 12, "fontTools.merge": 2, "fontTools.misc.configTools": 4, "interpolatableHelpers": 8, "fontTools.designspaceLib": 24, "fontTools.varLib.models": 34, "fontTools.cffLib": 14, "fontTools.cffLib.specializer": 8, "fontTools.pens.t2CharStringPen": 4, "fontTools.cffLib.CFFToCFF2": 4, "fontTools.varLib.varStore": 14, "fontTools.designspaceLib.split": 2, "fontTools.ttLib.tables._f_v_a_r": 4, "fontTools.ttLib.tables._g_l_y_f": 8, "fontTools.ttLib.tables.ttProgram": 2, "fontTools.ttLib.tables.TupleVariation": 14, "fontTools.varLib.featureVars": 4, "fontTools.varLib.iup": 8, "fontTools.varLib.merger": 8, "fontTools.varLib.mvar": 6, "fontTools.varLib.stat": 2, "cff": 2, "fontTools.ttx": 4, "fontTools.pens.cairoPen": 2, "fontTools.ttLib.ttGlyphSet": 8, "fontTools.varLib.interpolatableHelpers": 2, "glyphsLib": 4, "fontTools.otlLib.builder": 10, "fontTools.designspaceLib.types": 4, "scipy.optimize": 2, "munkres": 2, "scipy.sparse.csgraph": 2, "fontTools.misc.intTools": 8, "fontTools.varLib.builder": 6, "fontTools.pens.momentsPen": 4, "fontTools.pens.statisticsPen": 4, "fontTools.pens.transformPen": 16, "interpolatableTestContourOrder": 2, "interpolatableTestStartingPoint": 2, "fontTools.ufoLib": 4, "interpolatablePlot": 2, "fontTools.pens.areaPen": 2, "fontTools.misc.dictTools": 2, "fontTools.ttLib.ttVisitor": 6, "fontTools.misc.treeTools": 4, "fontTools.otlLib.optimize.gpos": 6, "fontTools.ttLib.tables.otConverters": 4, "fontTools.ttLib.tables.otTraverse": 4, "fontTools.misc.iterTools": 4, "fontTools.feaLib.lookupDebugInfo": 6, "fontTools.otlLib": 4, "fontTools.ttLib.tables._c_m_a_p": 2, "fontTools.misc.testTools": 2, "fontTools.mtiLib": 2, "PyQt5.QtGui": 2, "fontTools.pens.filterPen": 12, "fontTools.qu2cu": 2, "reportlab.graphics.shapes": 2, "reportlab.lib": 2, "reportlab.graphics": 2, "fontTools.misc.symfont": 2, "freetype": 2, "freetype.ft_enums": 2, "freetype.ft_errors": 2, "freetype.ft_structs": 2, "freetype.ft_types": 2, "freetype.raw": 2, "Quartz.CoreGraphics": 2, "fontTools.feaLib.ast": 6, "fontTools.otlLib.error": 4, "fs.base": 6, "fs.osfs": 6, "fontTools.ufoLib.utils": 8, "fs": 4, "fs.copy": 2, "fs.errors": 4, "fs.subfs": 2, "fs.tempfs": 2, "fs.tools": 2, "fs.zipfs": 2, "fontTools.ufoLib.converters": 2, "fontTools.ufoLib.errors": 4, "fontTools.ufoLib.filenames": 4, "fontTools.ufoLib.validators": 4, "fontTools.ufoLib.glifLib": 2, "fs.path": 2, "fontTools.misc.plistlib": 2, "fontTools.misc.etree": 2, "fontTools.designspaceLib.statNames": 2, "fontTools.feaLib.error": 10, "fontTools.feaLib.parser": 2, "fontTools.feaLib.variableScalar": 4, "fontTools.feaLib.lexer": 2, "fontTools.misc.encodingTools": 8, "fontTools.feaLib.location": 4, "fontTools.feaLib.builder": 2, "fontTools.ttLib.woff2": 2, "zopfli.zlib": 2, "fontTools.misc.macRes": 4, "fontTools.ttLib.ttFont": 6, "fontTools.ttLib.tables.otTables": 10, "fontTools.varLib.multiVarStore": 4, "fontTools.ttLib.ttCollection": 4, "pathops": 2, "tables.DefaultTable": 2, "reorderGlyphs": 2, "fontTools.misc.visitor": 2, "CFFToCFF2": 2, "CFF2ToCFF": 2, "width": 2, "Res": 2, "fontTools.misc.psOperators": 2, "Carbon": 2, "geometry": 2, "table_builder": 4, "sbixStrike": 2, "otBase": 54, "lz4.block": 2, "otData": 2, "otConverters": 2, "fontTools.misc.lazyTools": 4, "otTables": 4, "fontTools.ttLib.standardGlyphOrder": 2, "BitmapGlyphMetrics": 6, "E_B_D_T_": 2, "T_S_I_V_": 10, "fontTools.misc.filenames": 2, "sbixGlyph": 2, "uharfbuzz": 2, "DefaultTable": 2, "fontTools.ttLib.tables.C_F_F_": 2, "fontTools.otlLib.optimize": 2, "fontTools.varLib.instancer": 6, "featureVars": 2, "fontTools.cffLib.CFF2ToCFF": 2, "fontTools.ttLib.removeOverlaps": 2, "shapes": 2, "arc": 2, "common.html_blocks": 2, "common.html_re": 4, "state_block": 24, "blockquote": 2, "fence": 2, "heading": 2, "hr": 2, "html_block": 2, "lheading": 2, "list": 2, "paragraph": 2, "markdown_it.main": 4, "state_core": 16, "block": 2, "inline": 2, "linkify": 4, "normalize": 2, "replacements": 2, "smartquotes": 2, "text_join": 2, "entities": 2, "mdurl": 2, "state_inline": 30, "autolink": 2, "backticks": 2, "balance_pairs": 2, "entity": 2, "escape": 2, "fragments_join": 2, "html_inline": 2, "link": 2, "newline": 2, "common.entities": 2, "parse_link_destination": 2, "parse_link_label": 2, "parse_link_title": 2, "markdown_it.rules_inline": 8, "gitwildmatch": 1, "contextlib2": 2, "numpy_specific": 1, "contracts.main": 3, "types_misc": 2, "compositions": 3, "contracts.library.extensions": 2, "arithmetic": 1, "attributes": 1, "comparison": 1, "dicts": 1, "dummy": 1, "files": 1, "lists": 1, "map": 1, "separate_context": 1, "seq": 1, "simple_values": 1, "strings": 1, "suggester": 3, "tuple": 1, "variables": 1, "extensions": 2, "isinstance_imp": 1, "miscellaneous_aliases": 1, "scoped_variables": 1, "contracts.library.types_misc": 3, "contracts.inspection": 2, "contracts.library.simple_values": 2, "pyparsing_utils": 2, "array_ops": 1, "contracts.syntax": 3, "past.builtins": 2, "test_registrar": 3, "contracts.test_registrar": 3, "contracts.library": 1, "nose.tools": 1, "contracts.library.array": 1, "referencing.retrieval": 2, "language_server": 23, "identifiers": 2, "typeshed": 1, "configuration": 17, "pyre_extensions": 16, "filesystem": 13, "find_directories": 3, "platform_aware": 1, "python_version": 1, "scheduler_policies": 1, "shared_memory": 1, "unwatched": 1, "initialization": 3, "persistent": 1, "pyre_language_server": 1, "pyre_extensions.type_variable_operators": 2, "daemon_querier": 2, "document_formatter": 1, "pyre_language_server_error": 1, "server_state": 3, "source_code_context": 1, "pyre_server_options": 1, "query_response": 1, "tabulate": 1, "language_server.protocol": 2, "libcst_vendored_visitors": 1, "initialize": 1, "_apply_type_annotations": 1, "_gather_global_names": 2, "commands.codemods": 1, "commands.configurationless": 1, "commands.consolidate_nested_configurations": 1, "commands.expand_target_coverage": 1, "commands.fix_configuration": 1, "commands.fixme": 1, "commands.fixme_all": 1, "commands.fixme_single": 1, "commands.global_strictness": 1, "commands.global_version_update": 1, "commands.pysa_version_update": 1, "commands.strict_default": 1, "commands.support_sqlalchemy": 1, "commands.targets_to_configuration": 1, "repository": 16, "command": 12, "consolidate_nested_configurations": 1, "fixme": 1, "commands.command": 2, "strict_default": 1, "client.find_directories": 1, "google.protobuf.internal": 221, "google.protobuf.pyext": 8, "google.protobuf": 224, "encodings.raw_unicode_escape": 2, "encodings.unicode_escape": 2, "google.protobuf.descriptor_pool": 3, "google.protobuf.duration_pb2": 2, "google.protobuf.any_pb2": 2, "google.protobuf.timestamp_pb2": 2, "google._upb": 2, "google.protobuf.descriptor": 4, "nltk.misc.babelfish": 2, "nltk.misc.chomsky": 2, "nltk.misc.minimalset": 2, "nltk.misc.wordfinder": 2, "nltk.tree.parented": 4, "nltk.tree.tree": 14, "nltk.draw.tree": 12, "nltk.tree.immutable": 8, "svgling": 2, "nltk.tree.parsing": 2, "nltk.tree.probabilistic": 2, "nltk.metrics.distance": 6, "nltk.metrics.agreement": 4, "nltk.metrics.aline": 2, "nltk.metrics.association": 2, "nltk.metrics.confusionmatrix": 2, "nltk.metrics.paice": 2, "nltk.metrics.scores": 2, "nltk.metrics.segmentation": 2, "scipy.stats.stats": 2, "twython": 6, "nltk.twitter.util": 4, "nltk.twitter.twitterclient": 2, "nltk.twitter.common": 6, "nltk.twitter": 4, "nltk.twitter.api": 2, "twython.exceptions": 2, "nltk.cluster.api": 2, "nltk.cluster.em": 2, "nltk.cluster.gaac": 2, "nltk.cluster.kmeans": 2, "nltk.cluster.util": 8, "nltk.cluster": 4, "nltk.chunk.regexp": 4, "nltk.app.chartparser_app": 2, "nltk.app.chunkparser_app": 2, "nltk.app.collocations_app": 2, "nltk.app.concordance_app": 2, "nltk.app.nemo_app": 2, "nltk.app.rdparser_app": 2, "nltk.app.srparser_app": 2, "nltk.app.wordfreq_app": 2, "nltk.parse.chart": 14, "nltk.corpus.reader.wordnet": 4, "nltk.tag.api": 20, "nltk.classify.util": 22, "nltk.classify.api": 12, "nltk.classify.decisiontree": 2, "nltk.classify.megam": 4, "nltk.classify.naivebayes": 6, "nltk.classify.positivenaivebayes": 2, "nltk.classify.rte_classify": 4, "nltk.classify.scikitlearn": 4, "nltk.classify.senna": 2, "nltk.classify.textcat": 2, "nltk.classify.weka": 2, "nltk.classify.tadm": 2, "nltk.tabdata": 4, "nltk.tag.sequential": 4, "sklearn.feature_extraction": 2, "sklearn.preprocessing": 2, "sklearn.linear_model": 2, "sklearn.naive_bayes": 2, "nltk.tag.mapping": 4, "nltk.tag.util": 6, "nltk.chunk.api": 6, "nltk.chunk.named_entity": 2, "nltk.chunk.util": 6, "nltk.parse.malt": 6, "nltk.corpus.reader": 30, "nltk.chat.util": 12, "nltk.chat.eliza": 2, "nltk.chat.iesha": 2, "nltk.chat.rude": 2, "nltk.chat.suntsu": 2, "nltk.chat.zen": 2, "norm": 2, "nltk.stem.api": 22, "nltk.stem.porter": 6, "nltk.translate.ibm_model": 24, "nltk.translate.api": 2, "nltk.translate.ibm1": 2, "nltk.translate.ibm2": 2, "nltk.translate.ibm3": 2, "nltk.translate.ibm4": 2, "nltk.translate.ibm5": 2, "nltk.translate.bleu_score": 4, "nltk.translate.ribes_score": 4, "nltk.translate.meteor_score": 4, "nltk.translate.metrics": 2, "nltk.translate.stack_decoder": 4, "nltk.translate.nist_score": 4, "nltk.translate.chrf_score": 2, "nltk.translate.gale_church": 2, "nltk.translate.gdfa": 4, "nltk.translate.gleu_score": 2, "nltk.translate.phrase_based": 2, "nltk.lm.api": 4, "nltk.lm.smoothing": 2, "nltk.lm.counter": 4, "nltk.lm.models": 2, "nltk.lm.vocabulary": 4, "nltk.lm.util": 2, "nltk.sem.skolemize": 4, "nltk.sem.drt": 8, "nltk.sem.boxer": 2, "nltk.sem.evaluate": 2, "nltk.sem.lfg": 2, "nltk.sem.relextract": 2, "nltk.sem.util": 2, "nltk.sem.glue": 4, "nltk.parse.featurechart": 8, "nltk.parse.dependencygraph": 14, "nltk.corpus.util": 8, "nltk.corpus.reader.api": 80, "nltk.parse.api": 20, "bllipparser": 2, "bllipparser.RerankingParser": 2, "nltk.parse.util": 4, "nltk.parse.pchart": 4, "nltk.tokenize.api": 34, "nltk.parse.bllip": 4, "nltk.parse.corenlp": 4, "nltk.parse.earleychart": 2, "nltk.parse.evaluate": 2, "nltk.parse.nonprojectivedependencyparser": 2, "nltk.parse.projectivedependencyparser": 2, "nltk.parse.recursivedescent": 2, "nltk.parse.shiftreduce": 2, "nltk.parse.transitionparser": 2, "nltk.parse.viterbi": 2, "profile": 2, "pstats": 6, "sklearn.datasets": 2, "nltk.tbl.template": 2, "nltk.tbl.feature": 4, "nltk.tbl.rule": 4, "nltk.tbl.erroranalysis": 2, "nltk.tag.brill": 6, "nltk.tbl": 6, "nltk.stem.arlstem": 2, "nltk.stem.arlstem2": 2, "nltk.stem.cistem": 2, "nltk.stem.isri": 2, "nltk.stem.lancaster": 2, "nltk.stem.regexp": 2, "nltk.stem.rslp": 2, "nltk.stem.snowball": 4, "nltk.stem.wordnet": 2, "nltk.stem.util": 2, "nltk.inference.api": 12, "nltk.inference.discourse": 2, "nltk.inference.mace": 4, "nltk.inference.prover9": 8, "nltk.inference.resolution": 2, "nltk.inference.tableau": 2, "nltk.tokenize.casual": 4, "nltk.sentiment": 2, "sklearn.svm": 2, "nltk.sentiment.sentiment_analyzer": 2, "nltk.sentiment.vader": 2, "nltk.draw.cfg": 2, "nltk.draw.dispersion": 2, "pycrfsuite": 2, "nltk.tag.brill_trainer": 2, "nltk.tag.tnt": 2, "nltk.tag.hunpos": 2, "nltk.tag.stanford": 2, "nltk.tag.hmm": 2, "nltk.tag.senna": 2, "nltk.tag.crf": 2, "nltk.tag.perceptron": 2, "nltk.tokenize.util": 14, "nltk.tokenize.destructive": 4, "nltk.tokenize.legality_principle": 2, "nltk.tokenize.mwe": 2, "nltk.tokenize.punkt": 2, "nltk.tokenize.regexp": 2, "nltk.tokenize.repp": 2, "nltk.tokenize.sexpr": 2, "nltk.tokenize.simple": 4, "nltk.tokenize.sonority_sequencing": 2, "nltk.tokenize.stanford_segmenter": 2, "nltk.tokenize.texttiling": 2, "nltk.tokenize.toktok": 2, "nltk.tokenize.treebank": 2, "nltk.ccg.api": 4, "nltk.ccg.chart": 2, "nltk.ccg.combinator": 4, "nltk.ccg.lexicon": 4, "nltk.ccg.logic": 2, "nltk.corpus.reader.xmldocs": 20, "nltk.corpus.reader.wordlist": 4, "nltk.corpus.reader.plaintext": 6, "nltk.toolbox": 2, "nltk.corpus.reader.bracket_parse": 6, "nltk.corpus.reader.tagged": 4, "nltk.corpus.reader.cmudict": 2, "nltk.corpus.reader.conll": 2, "nltk.corpus.reader.chunked": 2, "nltk.corpus.reader.ppattach": 2, "nltk.corpus.reader.senseval": 2, "nltk.corpus.reader.ieer": 2, "nltk.corpus.reader.sinica_treebank": 2, "nltk.corpus.reader.indian": 2, "nltk.corpus.reader.toolbox": 2, "nltk.corpus.reader.timit": 4, "nltk.corpus.reader.ycoe": 2, "nltk.corpus.reader.rte": 2, "nltk.corpus.reader.string_category": 2, "nltk.corpus.reader.propbank": 2, "nltk.corpus.reader.verbnet": 2, "nltk.corpus.reader.bnc": 2, "nltk.corpus.reader.nps_chat": 2, "nltk.corpus.reader.switchboard": 2, "nltk.corpus.reader.dependency": 2, "nltk.corpus.reader.nombank": 2, "nltk.corpus.reader.ipipan": 2, "nltk.corpus.reader.pl196x": 2, "nltk.corpus.reader.knbc": 2, "nltk.corpus.reader.chasen": 2, "nltk.corpus.reader.childes": 2, "nltk.corpus.reader.aligned": 2, "nltk.corpus.reader.lin": 2, "nltk.corpus.reader.semcor": 2, "nltk.corpus.reader.framenet": 2, "nltk.corpus.reader.udhr": 2, "nltk.corpus.reader.sentiwordnet": 2, "nltk.corpus.reader.twitter": 2, "nltk.corpus.reader.nkjp": 2, "nltk.corpus.reader.crubadan": 2, "nltk.corpus.reader.mte": 2, "nltk.corpus.reader.reviews": 2, "nltk.corpus.reader.opinion_lexicon": 2, "nltk.corpus.reader.pros_cons": 2, "nltk.corpus.reader.categorized_sents": 2, "nltk.corpus.reader.comparative_sents": 2, "nltk.corpus.reader.panlex_lite": 2, "nltk.corpus.reader.panlex_swadesh": 2, "nltk.corpus.reader.bcp47": 2, "ossaudiodev": 2, "pygame.mixer": 2, "mdit_plain.renderer": 2, "mdit_py_plugins.front_matter": 2, "nltk.help": 2, "nltk.corpus.europarl_raw": 2, "cryptography.hazmat._oid": 4, "cryptography.x509.general_name": 6, "cryptography.hazmat.primitives.asymmetric.types": 8, "cryptography.x509.base": 4, "cryptography.x509.name": 8, "cryptography.x509.oid": 10, "cryptography.x509.certificate_transparency": 2, "cryptography.hazmat.backends.openssl.backend": 20, "cryptography.hazmat.primitives.hashes": 4, "cryptography.hazmat.primitives.constant_time": 2, "cryptography.hazmat.bindings.openssl._conditional": 2, "cryptography.hazmat.primitives.kdf": 14, "cryptography.hazmat.primitives.twofactor": 4, "cryptography.hazmat.primitives.twofactor.hotp": 2, "cryptography.hazmat.primitives._serialization": 4, "cryptography.hazmat.primitives.serialization.base": 2, "cryptography.hazmat.primitives.serialization.ssh": 2, "email.base64mime": 2, "cryptography.utils": 11, "bcrypt": 2, "cryptography.hazmat.decrepit.ciphers.algorithms": 2, "cryptography.hazmat.primitives._cipheralgorithm": 10, "cryptography.hazmat.primitives.ciphers.base": 2, "cryptography.hazmat.primitives._asymmetric": 6, "cryptography.hazmat.bindings.openssl": 2, "cryptography.hazmat.primitives.asymmetric.padding": 2, "past.utils": 4, "past": 2, "basestring": 1, "olddict": 1, "oldstr": 1, "lib2to3.pgen2.parse": 1, "lib2to3.refactor": 1, "libfuturize": 1, "past.types": 2, "past.builtins.noniterators": 1, "past.builtins.misc": 1, "railroad": 2, "pygments.plugin": 8, "pygments.lexers.c_cpp": 14, "pygments.lexers.d": 8, "pygments.lexers.factor": 2, "pygments.lexers.iolang": 2, "pygments.lexers.jvm": 10, "pygments.lexers.lisp": 6, "pygments.lexers.perl": 6, "pygments.lexers.ruby": 10, "pygments.lexers.scripting": 6, "pygments.lexers.tcl": 2, "pygments.lexers._asy_builtins": 2, "pygments.lexers._ada_builtins": 2, "pygments.lexers._cocoa_builtins": 2, "pygments.lexers._usd_builtins": 2, "pygments.lexers.actionscript": 4, "pygments.lexers.css": 10, "pygments.lexers.data": 10, "pygments.lexers.html": 14, "pygments.lexers.javascript": 12, "pygments.lexers.php": 4, "pygments.lexers.webmisc": 2, "pygments.lexers.web": 4, "pygments.lexers.theorem": 4, "pygments.lexers.shell": 6, "pygments.lexers._sourcemod_builtins": 2, "pygments.lexers._scheme_builtins": 2, "pygments.lexers._cl_builtins": 2, "pygments.lexers._lua_builtins": 2, "pygments.lexers._luau_builtins": 2, "pygments.modeline": 2, "pygments.lexers.automation": 2, "pygments.lexers.basic": 4, "pygments.lexers.business": 4, "pygments.lexers.configs": 4, "pygments.lexers.dsls": 2, "pygments.lexers.ecl": 2, "pygments.lexers.esoteric": 2, "pygments.lexers.graphics": 4, "pygments.lexers.installers": 4, "pygments.lexers.modeling": 4, "pygments.lexers.pawn": 2, "pygments.lexers.prolog": 4, "pygments.lexers.rebol": 2, "pygments.lexers.robotframework": 2, "pygments.lexers.smalltalk": 2, "pygments.lexers.smv": 2, "pygments.lexers.snobol": 2, "pygments.lexers.sql": 4, "pygments.lexers.testing": 2, "pygments.lexers.textedit": 4, "pygments.lexers.urbi": 2, "pygments.lexers.dotnet": 2, "pygments.lexers.objective": 4, "pygments.lexers.lean": 2, "pygments.lexers.ada": 2, "pygments.lexers.c_like": 2, "pygments.lexers.crystal": 2, "pygments.lexers.dylan": 2, "pygments.lexers.felix": 2, "pygments.lexers.fortran": 2, "pygments.lexers.go": 2, "pygments.lexers.ml": 4, "pygments.lexers.nimrod": 2, "pygments.lexers.ooc": 2, "pygments.lexers.pascal": 2, "pygments.lexers.rust": 2, "pygments.lexers._lilypond_builtins": 2, "pygments.lexers.lilypond": 2, "pygments.lexers._css_builtins": 2, "pygments.lexers.modula2": 2, "pygments.scanner": 2, "pygments.lexers._csound_builtins": 2, "pygments.lexers.erlang": 2, "pygments.lexers.haskell": 2, "pygments.lexers._vim_builtins": 2, "pygments.lexers._openedge_builtins": 2, "pygments.lexers.mime": 2, "pygments.lexers._qlik_builtins": 2, "pygments.lexers.console": 2, "pygments.lexers.haxe": 2, "pygments.lexers.make": 2, "pygments.lexers.markup": 4, "pygments.lexers.sgf": 2, "pygments.lexers.textfmts": 2, "pygments.lexers.algebra": 2, "pygments.lexers.idl": 2, "pygments.lexers.julia": 2, "pygments.lexers.matlab": 2, "pygments.lexers.r": 2, "pygments.lexers._php_builtins": 2, "pygments.lexers._mysql_builtins": 2, "pygments.lexers._postgres_builtins": 2, "pygments.unistring": 2, "pygments.lexers._lasso_builtins": 2, "pygments.lexers._julia_builtins": 2, "pygments.lexers._stata_builtins": 2, "pygments.console": 6, "pygments.formatters._mapping": 2, "pygments.styles._mapping": 2, "streamlit.watcher.local_sources_watcher": 2, "streamlit.watcher.path_watcher": 6, "streamlit.watcher": 10, "watchdog": 4, "streamlit.watcher.polling_path_watcher": 2, "streamlit.watcher.event_based_path_watcher": 2, "streamlit.watcher.folder_black_list": 2, "streamlit.runtime.pages_manager": 17, "streamlit.config": 1, "streamlit.git_util": 4, "streamlit.web.server": 8, "streamlit.runtime.caching.storage.local_disk_cache_storage": 4, "streamlit.runtime.caching.storage": 6, "streamlit.web.bootstrap": 1, "streamlit.runtime.credentials": 2, "streamlit.web.cache_storage_manager_config": 4, "streamlit.hello": 4, "streamlit.temporary_directory": 2, "streamlit.proto.Delta_pb2": 1, "google.protobuf.json_format": 4, "streamlit.proto.ClientState_pb2": 8, "streamlit.proto.Common_pb2": 14, "streamlit.proto.GitInfo_pb2": 2, "streamlit.proto.NewSession_pb2": 2, "streamlit.runtime.forward_msg_queue": 4, "streamlit.proto.BackMsg_pb2": 6, "streamlit.runtime.script_data": 8, "streamlit.runtime.scriptrunner.script_cache": 16, "streamlit.runtime.uploaded_file_manager": 26, "streamlit.source_util": 15, "streamlit.runtime.stats": 19, "streamlit.connections": 8, "requests.exceptions": 2, "streamlit.proto.PageProfile_pb2": 4, "streamlit.runtime.scriptrunner_utils.exceptions": 10, "streamlit.runtime.media_file_storage": 8, "cachetools": 6, "streamlit.runtime.session_manager": 10, "streamlit.runtime.app_session": 7, "streamlit.runtime.runtime": 2, "streamlit.components.lib.local_component_registry": 2, "streamlit.runtime.forward_msg_cache": 3, "streamlit.runtime.media_file_manager": 4, "streamlit.runtime.memory_session_storage": 4, "streamlit.runtime.runtime_util": 6, "streamlit.runtime.websocket_session_manager": 2, "streamlit.components.types.base_component_registry": 8, "streamlit.error_util": 4, "streamlit.time_util": 10, "streamlit.proto.openmetrics_data_model_pb2": 4, "streamlit.connections.util": 6, "sqlalchemy.engine.base": 2, "sqlalchemy.exc": 2, "snowflake.snowpark._internal.utils": 2, "streamlit.connections.base_connection": 2, "streamlit.connections.snowflake_connection": 2, "streamlit.connections.snowpark_connection": 2, "streamlit.connections.sql_connection": 2, "snowflake.connector": 4, "snowflake.connector.cursor": 2, "snowflake.snowpark.session": 4, "snowflake.snowpark.context": 4, "snowflake.connector.pandas_tools": 2, "snowflake.snowpark.exceptions": 2, "streamlit.proto.Exception_pb2": 4, "wave": 2, "streamlit.elements.lib.subtitle_utils": 2, "streamlit.elements.lib.utils": 52, "streamlit.proto.Audio_pb2": 2, "streamlit.proto.Video_pb2": 2, "tensorflow.python.keras.utils": 2, "streamlit.elements.lib.dicttools": 3, "streamlit.elements.lib.event_utils": 4, "streamlit.elements.lib.policies": 48, "streamlit.proto.ArrowVegaLiteChart_pb2": 2, "streamlit.dataframe_util": 26, "streamlit.elements.lib.color_util": 6, "streamlit.proto.GraphVizChart_pb2": 2, "streamlit.proto.Snow_pb2": 2, "streamlit.proto.Html_pb2": 2, "streamlit.proto.Empty_pb2": 2, "streamlit.proto.Skeleton_pb2": 2, "streamlit.elements.lib.column_config_utils": 4, "streamlit.elements.lib.pandas_styler_utils": 4, "streamlit.proto.Arrow_pb2": 10, "streamlit.proto.Code_pb2": 4, "streamlit.proto.IFrame_pb2": 2, "streamlit.proto.DeckGlJsonChart_pb2": 4, "streamlit.proto.Metric_pb2": 4, "streamlit.proto.Heading_pb2": 4, "streamlit.proto.Markdown_pb2": 4, "streamlit.proto.DocString_pb2": 2, "streamlit.runtime.scriptrunner.script_runner": 4, "streamlit.proto.Text_pb2": 4, "streamlit.proto.Toast_pb2": 4, "streamlit.proto.Balloons_pb2": 2, "plotly.io": 11, "plotly.tools": 3, "streamlit.elements.lib.streamlit_plotly_theme": 2, "streamlit.proto.PlotlyChart_pb2": 2, "plotly.basedatatypes": 1067, "streamlit.elements.lib.image_utils": 10, "streamlit.proto.Image_pb2": 6, "streamlit.proto.Progress_pb2": 2, "streamlit.proto.Spinner_pb2": 2, "streamlit.proto.Block_pb2": 14, "streamlit.proto.Json_pb2": 4, "bokeh": 2, "streamlit.proto.BokehChart_pb2": 2, "bokeh.plotting.figure": 2, "bokeh.embed": 2, "streamlit.proto.Alert_pb2": 4, "streamlit.proto.PageConfig_pb2": 2, "streamlit.file_util": 6, "streamlit.proto.Navigation_pb2": 2, "streamlit.runtime.state.query_params": 6, "streamlit.hello.utils": 8, "_testcapi": 2, "langchain.callbacks.base": 2, "langchain.schema": 2, "streamlit.external.langchain.streamlit_callback_handler": 2, "streamlit.proto.ChatInput_pb2": 6, "streamlit.proto.LabelVisibilityMessage_pb2": 2, "streamlit.runtime.state.common": 26, "plotly.graph_objects": 19, "streamlit.proto.DateInput_pb2": 4, "streamlit.proto.TimeInput_pb2": 4, "streamlit.elements.lib.options_selector_utils": 10, "streamlit.proto.Slider_pb2": 6, "streamlit.elements.lib.file_uploader_utils": 8, "streamlit.proto.CameraInput_pb2": 2, "streamlit.proto.MultiSelect_pb2": 4, "streamlit.proto.TextArea_pb2": 4, "streamlit.proto.TextInput_pb2": 4, "streamlit.elements.lib.js_number": 4, "streamlit.proto.ColorPicker_pb2": 4, "streamlit.proto.Button_pb2": 4, "streamlit.proto.DownloadButton_pb2": 2, "streamlit.proto.LinkButton_pb2": 2, "streamlit.proto.PageLink_pb2": 2, "streamlit.proto.FileUploader_pb2": 2, "streamlit.proto.AudioInput_pb2": 2, "streamlit.proto.Radio_pb2": 4, "streamlit.proto.ButtonGroup_pb2": 4, "streamlit.proto.Selectbox_pb2": 4, "streamlit.proto.Checkbox_pb2": 4, "streamlit.proto.NumberInput_pb2": 4, "streamlit.components.v1.custom_component": 4, "streamlit.elements.lib": 3, "streamlit.proto.Components_pb2": 4, "streamlit.components.v1.component_registry": 4, "streamlit.components.types.base_custom_component": 6, "streamlit.proto.Element_pb2": 4, "streamlit.runtime.caching.storage.dummy_cache_storage": 4, "streamlit.runtime.memory_media_file_storage": 6, "streamlit.runtime.state.safe_session_state": 10, "streamlit.runtime.state.session_state": 8, "streamlit.testing.v1.element_tree": 4, "streamlit.testing.v1.local_script_runner": 2, "streamlit.testing.v1.util": 2, "streamlit.proto.WidgetStates_pb2": 12, "streamlit.testing.v1.app_test": 4, "streamlit.runtime.memory_uploaded_file_manager": 6, "streamlit.vendor.pympler.asizeof": 4, "streamlit.runtime.state.query_params_proxy": 2, "streamlit.runtime.state.session_state_proxy": 4, "streamlit.runtime.state.widgets": 2, "streamlit.runtime.scriptrunner_utils.script_requests": 10, "streamlit.runtime.scriptrunner.exec_code": 2, "streamlit.runtime.caching.cache_type": 12, "streamlit.runtime.caching.cache_errors": 12, "streamlit.runtime.caching.cache_utils": 4, "streamlit.runtime.caching.cached_message_replay": 6, "streamlit.runtime.caching.storage.cache_storage_protocol": 10, "streamlit.runtime.caching.hashing": 8, "streamlit.runtime.caching.cache_data_api": 2, "streamlit.runtime.caching.cache_resource_api": 2, "streamlit.runtime.caching.legacy_cache_api": 2, "streamlit.runtime.caching.storage.in_memory_cache_storage_wrapper": 4, "streamlit.web.server.routes": 6, "streamlit.web.server.app_static_file_handler": 2, "streamlit.web.server.browser_websocket_handler": 4, "streamlit.web.server.component_request_handler": 4, "streamlit.web.server.media_file_handler": 2, "streamlit.web.server.server_util": 12, "streamlit.web.server.stats_request_handler": 4, "streamlit.web.server.upload_file_request_handler": 2, "streamlit.web.server.oauth_authlib_routes": 2, "streamlit.web.server.oidc_mixin": 4, "streamlit.web.server.server": 2, "streamlit.web.server.authlib_tornado_integration": 2, "pylint.checkers.utils": 37, "pylint.config._pylint_config": 2, "pylint.config.config_initialization": 2, "pylint.config.exceptions": 5, "pylint.config.utils": 3, "pylint.constants": 21, "pylint.lint.base_options": 2, "pylint.lint.pylinter": 19, "pylint.reporters.base_reporter": 7, "pylint.checkers.base_checker": 4, "pylint.config.arguments_manager": 3, "pylint.interfaces": 46, "pylint.lint.caching": 2, "pylint.lint.expand_modules": 2, "pylint.lint.message_state_handler": 1, "pylint.lint.parallel": 2, "pylint.lint.report_functions": 2, "pylint.lint.utils": 4, "pylint.message": 10, "pylint.reporters.text": 1, "pylint.reporters.ureports": 2, "pylint.utils.pragma_parser": 2, "astroid.brain.brain_dataclasses": 1, "pylint.checkers": 67, "enchant": 1, "enchant.tokenize": 1, "astroid.helpers": 2, "pylint.config.arguments_provider": 3, "pylint.exceptions": 6, "pylint.message.message_definition": 3, "pylint.checkers.deprecated": 1, "pylint.graph": 1, "pylint.utils.linterstats": 2, "pylint.config.deprecation_actions": 1, "pylint.config.argument": 2, "pylint.config.help_formatter": 2, "pylint.config.find_default_config_files": 1, "pylint.config.config_file_parser": 1, "pylint.message.message_id_store": 2, "pylint.message.message": 3, "pylint.message.message_definition_store": 1, "pylint.message._deleted_message_ids": 1, "pylint.utils.ast_walker": 1, "pylint.utils.docs": 1, "pylint.utils.file_state": 1, "pylint.utils.utils": 2, "isort.api": 1, "pylint.extensions": 1, "pylint.extensions._check_docs_utils": 1, "mccabe": 1, "pylint.checkers.exceptions": 1, "pylint.pyreverse.printer": 5, "pylint.pyreverse.utils": 8, "pylint.pyreverse.dot_printer": 1, "pylint.pyreverse.mermaidjs_printer": 1, "pylint.pyreverse.plantuml_printer": 1, "pylint.pyreverse": 2, "pylint.pyreverse.diagrams": 3, "pylint.pyreverse.printer_factory": 1, "pylint.pyreverse.diadefslib": 1, "pylint.reporters.collecting_reporter": 1, "pylint.reporters.json_reporter": 4, "pylint.reporters.multi_reporter": 1, "pylint.reporters.reports_handler_mix_in": 1, "pylint.reporters": 2, "pylint.reporters.ureports.text_writer": 1, "pylint.testutils.constants": 3, "pylint.testutils.checker_test_case": 2, "pylint.testutils.functional.test_file": 3, "pylint.testutils.output_line": 5, "pylint.testutils.reporter_for_tests": 3, "pylint.testutils.decorator": 1, "pylint.testutils.functional": 1, "pylint.testutils.get_test_info": 1, "pylint.testutils.global_test_linter": 2, "pylint.testutils.lint_module_test": 3, "pylint.testutils.tokenize_str": 1, "pylint.testutils.unittest_linter": 2, "pylint.testutils.utils": 1, "pylint.testutils._primer": 2, "pylint.testutils._primer.primer_command": 4, "pylint.testutils._primer.primer_compare_command": 1, "pylint.testutils._primer.primer_prepare_command": 1, "pylint.testutils._primer.primer_run_command": 1, "pylint.testutils._primer.package_to_lint": 2, "pylint.testutils.functional.find_functional_tests": 1, "pylint.testutils.functional.lint_module_output_update": 1, "pylint.reporters.ureports.base_writer": 3, "pylint.config._pylint_config.main": 1, "pylint.config._pylint_config.setup": 1, "pylint.config._pylint_config.help_message": 3, "pylint.config._pylint_config.generate_command": 1, "pylint.checkers.classes.class_checker": 1, "pylint.checkers.classes.special_methods_checker": 1, "pylint.checkers.refactoring.implicit_booleaness_checker": 1, "pylint.checkers.refactoring.not_checker": 1, "pylint.checkers.refactoring.recommendation_checker": 1, "pylint.checkers.refactoring.refactoring_checker": 1, "pylint.checkers.base.basic_error_checker": 2, "pylint.checkers.base.basic_checker": 7, "pylint.checkers.base.comparison_checker": 1, "pylint.checkers.base.docstring_checker": 1, "pylint.checkers.base.function_checker": 1, "pylint.checkers.base.name_checker": 1, "pylint.checkers.base.name_checker.checker": 2, "pylint.checkers.base.pass_checker": 1, "pylint.checkers.base.name_checker.naming_style": 2, "mypyc.ir.ops": 124, "mypyc.ir.class_ir": 50, "mypyc.irbuild.util": 12, "mypyc.primitives.bytes_ops": 8, "mypyc.primitives.dict_ops": 20, "mypyc.primitives.exc_ops": 12, "mypyc.primitives.float_ops": 4, "mypyc.primitives.generic_ops": 16, "mypyc.primitives.int_ops": 10, "mypyc.primitives.list_ops": 16, "mypyc.primitives.misc_ops": 22, "mypyc.primitives.registry": 38, "mypyc.primitives.set_ops": 6, "mypyc.primitives.str_ops": 10, "mypyc.primitives.tuple_ops": 8, "mypyc.rt_subtype": 6, "mypyc.sametype": 16, "mypyc.irbuild.builder": 32, "mypyc.irbuild.context": 10, "mypyc.irbuild.targets": 14, "mypyc.irbuild.ast_helpers": 4, "mypyc.irbuild.for_helpers": 6, "mypyc.irbuild.generator": 4, "mypyc.irbuild.nonlocalcontrol": 6, "match": 2, "mypyc.irbuild.constant_fold": 4, "mypyc.crash": 4, "mypyc.irbuild.ll_builder": 18, "mypyc.irbuild.mapper": 10, "mypyc.irbuild.prebuildvisitor": 4, "mypyc.irbuild.prepare": 6, "mypyc.irbuild.function": 4, "mypyc.irbuild.env_class": 4, "mypyc.irbuild.classdef": 2, "mypyc.irbuild.expression": 4, "mypyc.irbuild.statement": 4, "mypyc.irbuild.format_str_tokenizer": 4, "mypyc.analysis.attrdefined": 2, "mypyc.ir.module_ir": 10, "mypyc.irbuild.visitor": 2, "mypyc.irbuild.vtable": 4, "mypyc.irbuild.specialize": 2, "mypyc.irbuild.callable_class": 2, "mypyc.analysis.dataflow": 8, "mypyc.analysis.selfleaks": 2, "mypyc.test.testutil": 18, "mypyc.transform.exceptions": 6, "mypyc.transform.flag_elimination": 6, "mypyc.transform.lower": 4, "mypyc.transform.refcount": 8, "mypyc.transform.uninit": 10, "mypyc.primitives": 2, "mypyc.codegen.literals": 8, "mypyc.transform.copy_propagation": 4, "mypyc.build": 2, "mypyc.test.config": 4, "mypyc.test.test_serialization": 2, "mypyc.analysis": 2, "mypyc.transform": 2, "mypyc.analysis.blockfreq": 4, "mypyc.analysis.ircheck": 4, "mypyc.codegen.emitclass": 4, "mypyc.codegen.emit": 14, "mypyc.codegen.emitfunc": 6, "mypyc.codegen.emitwrapper": 6, "mypyc.irbuild.main": 4, "mypyc.codegen.cstring": 2, "mypyc.lower.registry": 8, "mypyc.lower": 2, "mypyc.transform.ir_transform": 6, "pint": 1, "whitelist_utils": 1, "networkx.readwrite.graph6": 6, "networkx.readwrite.adjlist": 2, "networkx.readwrite.edgelist": 2, "networkx.readwrite.gexf": 2, "networkx.readwrite.gml": 4, "networkx.readwrite.graphml": 4, "networkx.readwrite.json_graph": 10, "networkx.readwrite.leda": 2, "networkx.readwrite.multiline_adjlist": 2, "networkx.readwrite.pajek": 2, "networkx.readwrite.sparse6": 2, "networkx.readwrite.text": 4, "layout": 2, "nx_latex": 2, "nx_pylab": 2, "networkx.drawing.layout": 2, "networkx.linalg.algebraicconnectivity": 2, "networkx.linalg.attrmatrix": 2, "networkx.linalg.bethehessianmatrix": 2, "networkx.linalg.graphmatrix": 2, "networkx.linalg.laplacianmatrix": 2, "networkx.linalg.modularitymatrix": 2, "networkx.linalg.spectrum": 2, "networkx.classes.coreviews": 10, "networkx.classes.reportviews": 12, "digraph": 2, "graph": 2, "graphviews": 2, "multidigraph": 2, "multigraph": 2, "networkx.classes.graph": 4, "networkx.classes.digraph": 2, "networkx.classes.multigraph": 2, "networkx.classes.filters": 2, "networkx.algorithms.matching": 6, "networkx.algorithms.components": 8, "networkx.algorithms.assortativity": 4, "networkx.algorithms.asteroidal": 2, "networkx.algorithms.bipartite": 8, "networkx.algorithms.boundary": 2, "networkx.algorithms.bridges": 2, "networkx.algorithms.broadcasting": 2, "networkx.algorithms.centrality": 4, "networkx.algorithms.chains": 2, "networkx.algorithms.chordal": 2, "networkx.algorithms.clique": 2, "networkx.algorithms.cluster": 2, "networkx.algorithms.coloring": 2, "networkx.algorithms.communicability_alg": 4, "networkx.algorithms.connectivity": 10, "networkx.algorithms.core": 2, "networkx.algorithms.covering": 4, "networkx.algorithms.cuts": 2, "networkx.algorithms.cycles": 2, "networkx.algorithms.d_separation": 2, "networkx.algorithms.dag": 2, "networkx.algorithms.distance_measures": 4, "networkx.algorithms.distance_regular": 2, "networkx.algorithms.dominance": 2, "networkx.algorithms.dominating": 2, "networkx.algorithms.efficiency_measures": 2, "networkx.algorithms.euler": 2, "networkx.algorithms.flow": 18, "networkx.algorithms.graph_hashing": 2, "networkx.algorithms.graphical": 2, "networkx.algorithms.hierarchy": 2, "networkx.algorithms.hybrid": 2, "networkx.algorithms.isolate": 2, "networkx.algorithms.isomorphism": 4, "networkx.algorithms.isomorphism.vf2pp": 6, "networkx.algorithms.link_analysis": 2, "networkx.algorithms.link_prediction": 2, "networkx.algorithms.lowest_common_ancestors": 2, "networkx.algorithms.minors": 4, "networkx.algorithms.mis": 2, "networkx.algorithms.moral": 4, "networkx.algorithms.non_randomness": 2, "networkx.algorithms.operators": 2, "networkx.algorithms.planar_drawing": 4, "networkx.algorithms.planarity": 4, "networkx.algorithms.polynomials": 2, "networkx.algorithms.reciprocity": 2, "networkx.algorithms.regular": 4, "networkx.algorithms.richclub": 2, "networkx.algorithms.shortest_paths": 2, "networkx.algorithms.similarity": 4, "networkx.algorithms.simple_paths": 6, "networkx.algorithms.smallworld": 2, "networkx.algorithms.smetric": 2, "networkx.algorithms.sparsifiers": 2, "networkx.algorithms.structuralholes": 2, "networkx.algorithms.summarization": 2, "networkx.algorithms.swap": 2, "networkx.algorithms.time_dependent": 2, "networkx.algorithms.tournament": 4, "networkx.algorithms.traversal": 2, "networkx.algorithms.tree.branchings": 2, "networkx.algorithms.tree.coding": 2, "networkx.algorithms.tree.decomposition": 4, "networkx.algorithms.tree.mst": 4, "networkx.algorithms.tree.operations": 2, "networkx.algorithms.tree.recognition": 2, "networkx.algorithms.triads": 2, "networkx.algorithms.vitality": 2, "networkx.algorithms.voronoi": 2, "networkx.algorithms.walks": 2, "networkx.algorithms.wiener": 2, "distance_measures": 2, "networkx.algorithms.shortest_paths.weighted": 8, "networkx.generators.classic": 18, "backends": 2, "networkx.utils.backends": 2, "networkx.utils.configs": 4, "networkx.utils.decorators": 32, "networkx.utils.heaps": 2, "networkx.utils.misc": 8, "networkx.utils.random_sequence": 2, "networkx.utils.rcm": 2, "networkx.utils.union_find": 2, "networkx.generators.atlas": 4, "networkx.generators.cographs": 2, "networkx.generators.community": 2, "networkx.generators.degree_seq": 12, "networkx.generators.directed": 4, "networkx.generators.duplication": 2, "networkx.generators.ego": 2, "networkx.generators.expanders": 4, "networkx.generators.geometric": 2, "networkx.generators.harary_graph": 4, "networkx.generators.internet_as_graphs": 4, "networkx.generators.intersection": 2, "networkx.generators.interval_graph": 4, "networkx.generators.joint_degree_seq": 4, "networkx.generators.lattice": 2, "networkx.generators.line": 2, "networkx.generators.mycielski": 2, "networkx.generators.nonisomorphic_trees": 2, "networkx.generators.random_clustered": 2, "networkx.generators.random_graphs": 2, "networkx.generators.small": 2, "networkx.generators.social": 2, "networkx.generators.spectral_graph_forge": 4, "networkx.generators.stochastic": 2, "networkx.generators.sudoku": 2, "networkx.generators.time_series": 2, "networkx.generators.trees": 2, "networkx.generators.triads": 2, "scipy.special": 2, "networkx.algorithms.operators.product": 4, "scipy.sparse.linalg": 2, "utils.misc": 2, "classic": 2, "degree_seq": 2, "networkx.algorithms.isomorphism.isomorph": 10, "networkx.utils.mapped_queue": 4, "networkx.classes.tests.dispatch_interface": 2, "networkx.classes.tests": 8, "branchings": 2, "coding": 2, "decomposition": 2, "mst": 2, "operations": 2, "recognition": 4, "networkx.algorithms.isomorphism.ismags": 2, "networkx.algorithms.isomorphism.matchhelpers": 2, "networkx.algorithms.isomorphism.temporalisomorphvf2": 2, "networkx.algorithms.isomorphism.tree_isomorphism": 4, "networkx.algorithms.isomorphism.vf2userfunc": 2, "isomorphvf2": 2, "networkx.algorithms.link_analysis.hits_alg": 4, "networkx.algorithms.link_analysis.pagerank_alg": 4, "networkx.algorithms.operators.all": 2, "networkx.algorithms.operators.binary": 2, "networkx.algorithms.operators.unary": 2, "connectivity": 2, "cuts": 2, "disjoint_paths": 2, "edge_augmentation": 2, "edge_kcomponents": 2, "kcomponents": 2, "kcutsets": 2, "stoerwagner": 2, "networkx.algorithms.approximation": 26, "networkx.algorithms.approximation.clique": 2, "networkx.algorithms.approximation.clustering_coefficient": 2, "networkx.algorithms.approximation.connectivity": 2, "networkx.algorithms.approximation.distance_measures": 2, "networkx.algorithms.approximation.dominating_set": 2, "networkx.algorithms.approximation.kcomponents": 4, "networkx.algorithms.approximation.matching": 2, "networkx.algorithms.approximation.maxcut": 2, "networkx.algorithms.approximation.ramsey": 2, "networkx.algorithms.approximation.steinertree": 4, "networkx.algorithms.approximation.traveling_salesman": 4, "networkx.algorithms.approximation.treewidth": 4, "networkx.algorithms.approximation.vertex_cover": 2, "matching": 2, "networkx.algorithms.centrality.flow_matrix": 6, "networkx.algorithms.centrality.betweenness": 6, "betweenness": 2, "betweenness_subset": 2, "closeness": 2, "current_flow_betweenness": 2, "current_flow_betweenness_subset": 2, "current_flow_closeness": 2, "degree_alg": 2, "dispersion": 2, "eigenvector": 2, "group": 2, "harmonic": 2, "katz": 2, "laplacian": 2, "load": 2, "percolation": 2, "reaching": 2, "second_order": 2, "subgraph_alg": 2, "trophic": 2, "voterank_alg": 2, "networkx.algorithms.traversal.edgedfs": 6, "networkx.algorithms.threshold": 2, "networkx.algorithms.bipartite.basic": 2, "networkx.algorithms.bipartite.centrality": 2, "networkx.algorithms.bipartite.cluster": 4, "networkx.algorithms.bipartite.covering": 2, "networkx.algorithms.bipartite.edgelist": 2, "networkx.algorithms.bipartite.extendability": 2, "networkx.algorithms.bipartite.generators": 2, "networkx.algorithms.bipartite.matching": 6, "networkx.algorithms.bipartite.matrix": 4, "networkx.algorithms.bipartite.projection": 2, "networkx.algorithms.bipartite.redundancy": 2, "networkx.algorithms.bipartite.spectral": 2, "attracting": 2, "biconnected": 2, "connected": 2, "semiconnected": 2, "strongly_connected": 2, "weakly_connected": 2, "beamsearch": 2, "breadth_first_search": 2, "depth_first_search": 2, "edgebfs": 2, "edgedfs": 2, "networkx.algorithms.minors.contraction": 2, "networkx.algorithms.shortest_paths.generic": 4, "networkx.algorithms.shortest_paths.astar": 2, "networkx.algorithms.shortest_paths.dense": 2, "networkx.algorithms.shortest_paths.unweighted": 2, "networkx.algorithms.assortativity.connectivity": 2, "networkx.algorithms.assortativity.correlation": 4, "networkx.algorithms.assortativity.mixing": 4, "networkx.algorithms.assortativity.neighbor_degree": 2, "networkx.algorithms.assortativity.pairs": 6, "networkx.algorithms.community.asyn_fluid": 2, "networkx.algorithms.community.centrality": 2, "networkx.algorithms.community.community_utils": 6, "networkx.algorithms.community.divisive": 2, "networkx.algorithms.community.kclique": 2, "networkx.algorithms.community.kernighan_lin": 2, "networkx.algorithms.community.label_propagation": 2, "networkx.algorithms.community.louvain": 2, "networkx.algorithms.community.lukes": 2, "networkx.algorithms.community.modularity_max": 2, "networkx.algorithms.community.quality": 6, "networkx.algorithms.community": 10, "networkx.algorithms.coloring.equitable_coloring": 2, "networkx.algorithms.coloring.greedy_coloring": 2, "networkx.algorithms.flow.utils": 6, "edmondskarp": 8, "boykovkolmogorov": 4, "dinitz_alg": 4, "preflowpush": 4, "shortestaugmentingpath": 4, "capacityscaling": 2, "gomory_hu": 2, "maxflow": 2, "mincost": 2, "networksimplex": 2, "base_test": 6, "generators": 2, "networkx.algorithms.centrality.subgraph_alg": 2, "networkx.algorithms.connectivity.kcomponents": 2, "networkx.algorithms.connectivity.kcutsets": 2, "networkx.algorithms.connectivity.edge_augmentation": 2, "networkx.algorithms.connectivity.edge_kcomponents": 2, "networkx.classes.function": 2, "networkx.algorithms.tree": 2, "test_multigraph": 4, "historical_tests": 4, "test_graph": 6, "test_digraph": 2, "test_multidigraph": 2, "networkx.readwrite.json_graph.adjacency": 2, "networkx.readwrite.json_graph.cytoscape": 2, "networkx.readwrite.json_graph.node_link": 2, "networkx.readwrite.json_graph.tree": 2, "networkx.readwrite.p2g": 2, "_trace": 20, "connection_pool": 12, "http11": 16, "interfaces": 28, "http_proxy": 8, "socks_proxy": 8, "h2.exceptions": 4, "h2.settings": 4, "_backends.auto": 6, "h11": 4, "astroid.nodes.as_string": 1, "astroid.nodes.const": 2, "astroid.nodes.node_ng": 2, "astroid.protocols": 1, "astroid.brain.brain_numpy_utils": 5, "astroid.brain": 1, "astroid.filter_statements": 1, "astroid.nodes.scoped_nodes.utils": 3, "astroid.nodes.scoped_nodes.mixin": 2, "astroid.nodes.scoped_nodes.scoped_nodes": 1, "astroid.interpreter.dunder_lookup": 1, "models.progress_tracker": 1, "simple_analyzer": 1, "utils.config_utils": 1, "vibe_check.core.utils": 2, "vibe_check.core.analysis.import_analyzer": 2, "vibe_check.core.analysis.import_visualizer": 1, "vibe_check.core.config": 3, "vibe_check.core.utils.async_utils": 2, "error_handling": 3, "vibe_check.core.logging": 77, "plugin_base": 2, "vibe_check.cli.watch_mode": 1, "vibe_check.cli.completion": 1, "vibe_check.cli.parallel_processing": 1, "error_handler": 3, "core.dependency_manager": 2, "ui.gui": 1, "core.simple_analyzer": 2, "core.config": 2, "vibe_check.cli.output_formats": 1, "vibe_check.core.vcs.memory_manager": 1, "core.version": 1, "monitor": 1, "core.knowledge.framework_knowledge_base": 1, "vibe_check.core.logging.setup": 1, "vibe_check.cli.formatters": 1, "plugins.manager": 1, "core.logging": 1, "core.utils.dict_utils": 1, "core.vcs.engine": 1, "core.vcs.models": 1, "core.vcs.config": 1, "ui.tui": 1, "ui.web": 1, "vibe_check.ai.infrastructure": 6, "vibe_check.core.vcs.integration.meta_analyzer": 3, "templates": 7, "weasyprint": 1, "github_actions": 1, "gitlab_ci": 1, "jenkins": 1, "azure_devops": 1, "static_assets": 1, "rest": 1, "graphql": 1, "websocket": 1, "monitoring": 1, "smtplib": 1, "email.mime.text": 1, "email.mime.multipart": 1, "cache_manager": 3, "distributed_processor": 1, "python_semantic_analyzer": 7, "framework_detector": 2, "semantic_rules": 3, "utils.file_utils": 1, "file_analyzer": 1, "metrics_aggregator": 1, "framework_rules": 2, "project_meritocracy_analyzer": 2, "semantic_output_formatter": 1, "performance_optimizer": 1, "astor": 1, "type_analyzer": 2, "utils.fs_utils": 2, "tool_executor": 1, "result_processor": 1, "tools.runners.tool_registry": 1, "meta_analyzer": 1, "import_analyzer": 4, "visualization": 9, "error_handling.exceptions": 1, "config_utils": 1, "gitignore_utils": 1, "vibe_check.tools.runners.tool_registry": 2, "dict_utils": 1, "rich.status": 3, "directory_metrics": 1, "file_metrics": 2, "models.project_metrics": 2, "trend_storage": 1, "ui.visualization.interactive_charts": 1, "framework_knowledge_base": 1, "analysis.python_semantic_analyzer": 1, "dependency_tracker": 1, "vibe_check.core.vcs.cache": 1, "metrics_collector": 1, "query_engine": 1, "time_series_storage": 2, "contextual_logger": 1, "plugin_interface": 3, "plugin_loader": 1, "unified_reporter": 1, "style_rules": 1, "security_rules": 1, "complexity_rules": 1, "documentation_rules": 1, "import_rules": 1, "type_rules": 1, "advanced_python_rules": 1, "performance_rules": 1, "plotly.subplots": 6, "html_generators": 1, "base_parser": 9, "parser_registry": 8, "base_runner": 9, "tool_registry": 8, "core.analysis.standalone_analyzer": 1, "custom_rules.python_rules": 1, "core.models.project_metrics": 10, "core.fs_utils": 2, "core.analysis.import_analyzer": 1, "charts": 2, "exporters": 1, "interactive_charts": 1, "report_generator": 3, "markdown": 1, "state_manager": 5, "header_footer": 4, "vibe_check.core": 3, "keyboard": 4, "components": 1, "vibe_check.ui.web.components": 1, "vibe_check.ui.web.state_manager": 1, "simple_gui": 1, "themes": 1, "objc": 385, "backports.datetime_fromisoformat": 1, "marshmallow.constants": 4, "AVFoundation": 5, "categories": 1, "properties": 1, "CoreData": 2, "Quartz": 18, "Security": 3, "CoreFoundation": 10, "JavaScriptCore": 1, "FSEvents": 1, "CoreServices": 5, "LaunchServices": 1, "Cocoa": 4, "streamlit.elements.lib.layout_utils": 29, "streamlit.elements": 3, "rich.box": 1, "CoreAudio": 2, "Accounts": 1, "CoreLocation": 2, "MetalPerformanceShaders": 1, "Metal": 4, "_inlines": 15, "typer._types": 1, "IOBluetooth": 2, "dispatch": 1, "plugin": 6, "cProfile": 3, "aspectlib": 1, "timers": 2, "stats": 3, "pygaljs": 1, "pygal.graph.box": 1, "pygal.style": 1, "fixture": 2, "_pytest.config.findpaths": 1, "pytest_benchmark.csv": 1, "storage.file": 1, "storage.elasticsearch": 1, "cpuinfo": 2, "histogram": 1, "__pypy__.time": 1, "pytest_benchmark.cli": 1, "UserNotifications": 1, "uc_micro.categories": 1, "uc_micro.properties": 1, "ucre": 1, "SceneKit": 1, "zipp.compat.overlay": 1, "DiscRecording": 1, "Foundation._context": 1, "Foundation._functiondefines": 1, "Foundation._nsindexset": 1, "Foundation._nsobject": 1, "Foundation._nsurl": 1, "markdown_it.rules_block": 14, "mdit_py_plugins.utils": 12, "markdown_it.common.utils": 6, "markdown_it.renderer": 9, "markdown_it.utils": 9, "LocalAuthentication": 1, "CoreText": 1, "HIServices": 1, "CoreML": 1, "CoreMedia": 6, "_cached": 1, "_cachedmethod": 1, "textual": 52, "textual._arrange": 2, "textual._callback": 9, "textual._compositor": 2, "textual._context": 18, "textual._path": 3, "textual._spatial_map": 2, "textual._types": 17, "textual.actions": 4, "textual.await_complete": 8, "textual.binding": 27, "textual.css.match": 6, "textual.css.parse": 8, "textual.css.query": 11, "textual.dom": 21, "textual.errors": 3, "textual.geometry": 52, "textual.keys": 6, "textual.layout": 10, "textual.renderables.background_screen": 1, "textual.renderables.blank": 4, "textual.selection": 7, "textual.signal": 3, "textual.timer": 10, "textual.widget": 49, "textual.widgets._toast": 2, "textual.command": 4, "textual.message_pump": 7, "textual.message": 32, "textual._wait": 2, "textual.drivers.headless_driver": 2, "textual.events": 18, "textual.color": 29, "textual.css.types": 13, "textual.style": 11, "textual.content": 16, "textual.cache": 11, "textual.visual": 10, "textual._compat": 4, "textual._on": 2, "textual._time": 3, "textual.constants": 4, "textual.css.model": 7, "rich.color_triplet": 1, "rich.terminal_theme": 9, "textual._color_constants": 1, "textual.css.scalar": 9, "textual.css.tokenize": 7, "textual.suggestions": 4, "textual.fuzzy": 1, "textual.types": 2, "textual.widgets.option_list": 1, "textual.worker": 6, "textual.css.styles": 15, "textual._animator": 6, "textual._compose": 2, "textual._debug": 3, "textual._dispatch_key": 2, "textual._easing": 3, "textual._extrema": 1, "textual._styles_cache": 1, "textual.await_remove": 4, "textual.box_model": 2, "textual.layouts.vertical": 2, "textual.messages": 4, "textual.notifications": 3, "textual.rlock": 1, "textual.strip": 17, "textual.scrollbar": 2, "textual.css.tokenizer": 7, "textual.design": 1, "textual.markup": 4, "textual._ansi_theme": 3, "textual._border": 3, "textual._opacity": 1, "textual._segment_tools": 4, "textual.filter": 5, "textual.renderables.text_opacity": 1, "textual.renderables.tint": 1, "textual._cells": 10, "textual._loop": 10, "textual.expand_tabs": 5, "rich.protocol": 5, "tree_sitter": 4, "textual.canvas": 2, "textual.layouts.grid": 2, "textual._log": 1, "textual._work_decorator": 1, "textual.case": 2, "textual._import_app": 1, "textual.pilot": 2, "textual._widget_navigation": 1, "textual.widgets._directory_tree": 3, "textual.widgets._input": 4, "textual.widgets._option_list": 5, "textual.widgets._placeholder": 2, "textual.widgets._select": 3, "textual._markup_playground": 1, "textual._partition": 1, "rich.control": 2, "textual.map_geometry": 1, "rich._wrap": 1, "textual.css.scalar_animation": 2, "textual._ansi_sequences": 2, "textual._keyboard_protocol": 1, "textual._parser": 3, "textual_speedups": 1, "textual._event_broker": 1, "textual._files": 1, "textual.css.errors": 7, "textual.css.stylesheet": 1, "textual.driver": 6, "textual.features": 1, "textual.file_monitor": 1, "textual.theme": 2, "textual.worker_manager": 2, "textual_dev.client": 1, "textual.system_commands": 1, "textual_dev.redirect_output": 1, "textual.drivers.windows_driver": 1, "textual.drivers.linux_driver": 1, "textual.drivers.linux_inline_driver": 1, "textual.render": 2, "textual._win_sleep": 1, "textual.demo.demo_app": 2, "textual._box_drawing": 1, "textual._node_list": 1, "textual.css._error_tools": 9, "textual.css.constants": 6, "textual.walk": 1, "_plotly_utils.files": 1, "_subplots": 2, "plotly.validators": 3, "plotly": 29, "plotly.files": 2, "plotly.figure_factory": 12, "basedatatypes": 3, "optional_imports": 2, "_plotly_utils.importers": 1462, "plotly.version": 1, "express": 1, "_plotly_utils.basevalidators": 12275, "_plotly_utils.utils": 3, "_plotly_utils.exceptions": 2, "validators": 1, "plotly.offline.offline": 3, "plotly.io._utils": 7, "plotly._subplots": 2, "plotly.io.kaleido": 1, "validator_cache": 1, "plotly.utils": 3, "_plotly_utils.data_utils": 1, "graph_objects": 1, "plotly.io._renderers": 1, "callbacks": 1, "serializers": 1, "_plotly_utils.optional_imports": 5, "Intents": 1, "SpriteKit": 1, "asyncio.base_subprocess": 1, "png": 1, "Contacts": 1, "objc._pythonify": 1, "ExceptionHandling": 1, "PyObjCTools": 1, "numpy.core.multiarray": 1, "FileProvider": 1, "narwhals._typing_compat": 9, "narwhals._utils": 68, "pyspark.sql.connect.dataframe": 2, "narwhals._ibis.namespace": 3, "pyspark": 1, "narwhals._enum": 1, "embed": 1, "engine": 3, "SharedWithYouCore": 1, "compression": 2, "PyObjCTools.KeyValueCoding": 1, "objc._objc": 10, "objc._new": 1, "objc._transform": 1, "objc._convenience": 9, "_new": 5, "_transform": 1, "_convenience": 1, "_convenience_nsobject": 1, "_convenience_nsdecimal": 1, "_convenience_nsdata": 1, "_convenience_nsdictionary": 1, "_convenience_nsset": 1, "_convenience_nsarray": 1, "_convenience_nsstring": 1, "_convenience_mapping": 1, "_convenience_sequence": 1, "_dyld": 1, "_protocols": 1, "_descriptors": 1, "_category": 1, "_bridges": 1, "_pythonify": 1, "_locking": 1, "_context": 1, "_properties": 1, "_lazyimport": 1, "_bridgesupport": 1, "_informal_protocol": 2, "objc._convenience_mapping": 1, "objc._framework": 1, "cpuinfo.cpuinfo": 1, "_keyboard_event": 5, "_generic": 2, "_canonical_names": 5, "_mouse_event": 5, "_nixcommon": 2, "items": 1, "PrintCore": 1, "CFOpenDirectory": 1, "authlib.oauth2.rfc6749.util": 1, "userinfo": 1, "authlib.oauth2.rfc6749.authorization_server": 1, "authlib.oauth2.rfc6749.resource_protector": 1, "authlib.oauth2.rfc6749.hooks": 1, "rfc6749.hooks": 1, "authlib.oidc.discovery.models": 1, "registration": 1, "rfc6749.authenticate_client": 1, "rfc6749.requests": 1, "authlib.oauth2.rfc6749.grants": 1, "narwhals._duration": 9, "dask.dataframe.api": 1, "pyarrow.__lib_pxi.table": 1, "narwhals._compliant.window": 6, "ibis.expr.types": 9, "narwhals._ibis.utils": 5, "narwhals._ibis.expr": 9, "ibis.expr.operations": 1, "narwhals._ibis.group_by": 1, "ibis.expr.datatypes": 3, "narwhals._ibis.selectors": 1, "narwhals._ibis.expr_dt": 1, "narwhals._ibis.expr_list": 1, "narwhals._ibis.expr_str": 1, "narwhals._ibis.expr_struct": 1, "cupy": 2, "pyspark.sql.connect": 1, "pyspark.sql.connect.window": 1, "test_parallel": 1, "_posixsubprocess": 1, "_implementation": 4, "_toml_compat": 3, "pip._vendor.tomli_w._writer": 1, "resolvers.criterion": 1, "criterion": 4, "abstract": 2, "resolution": 1, "pip._internal.req.req_dependency_group": 1, "pip._vendor.dependency_groups": 1, "pip._internal.models.pylock": 1, "_swatches": 7, "plotly.express._core": 2, "colorbrewer": 3, "cmocean": 2, "carto": 3, "plotlyjs": 2, "_plotly_utils": 1, "meta": 1, "safety.tool.decorators": 1, "safety.tool.auth": 2, "plotly.basewidget": 1, "_bar": 6, "_barpolar": 4, "_box": 6, "_candlestick": 4, "_carpet": 7, "_choropleth": 4, "_choroplethmap": 4, "_choroplethmapbox": 4, "_cone": 4, "_contour": 10, "_contourcarpet": 4, "_densitymap": 4, "_densitymapbox": 4, "_deprecations": 1, "_figure": 1, "_frame": 1, "_funnel": 4, "_funnelarea": 4, "_heatmap": 4, "_histogram": 4, "_histogram2d": 4, "_histogram2dcontour": 4, "_icicle": 4, "_image": 5, "_indicator": 4, "_isosurface": 4, "_layout": 5, "_mesh3d": 4, "_ohlc": 4, "_parcats": 4, "_parcoords": 4, "_pie": 4, "_sankey": 4, "_scatter": 4, "_scatter3d": 4, "_scattercarpet": 4, "_scattergeo": 4, "_scattergl": 4, "_scattermap": 4, "_scattermapbox": 4, "_scatterpolar": 4, "_scatterpolargl": 4, "_scattersmith": 4, "_scatterternary": 4, "_splom": 4, "_streamtube": 4, "_sunburst": 4, "_surface": 8, "_table": 4, "_treemap": 4, "_violin": 4, "_volume": 4, "_waterfall": 4, "graph_objs._figurewidget": 2, "missing_anywidget": 2, "plotly.validators.layout": 2, "_doc": 1, "_imshow": 1, "_chart_types": 1, "_special_inputs": 2, "trendline_functions": 1, "plotly.colors": 13, "imshow_utils": 1, "_orca": 2, "google.colab": 1, "plotly.io._base_renderers": 1, "plotly.io.orca": 1, "_kaleido": 2, "_templates": 1, "_html": 1, "_renderers": 1, "kaleido": 1, "plotly.io._defaults": 1, "kaleido.scopes.plotly": 1, "kaleido.errors": 1, "choreographer.cli.defaults": 1, "_base_renderers": 1, "plotly.io._orca": 1, "plotly.io.json": 2, "plotly.optional_imports": 2, "plotly.graph_objs.layout": 1, "plotly.offline": 1, "graph_objs": 1, "_plotly_utils.colors": 1, "plotly.matplotlylib.mplexporter": 2, "plotly.matplotlylib": 1, "plotly.matplotlylib.renderer": 1, "_frames": 1, "_data": 4, "plotly.validators.heatmap": 1, "plotly.exceptions": 2, "plotly.figure_factory._2d_density": 1, "plotly.figure_factory._annotated_heatmap": 1, "plotly.figure_factory._bullet": 1, "plotly.figure_factory._candlestick": 1, "plotly.figure_factory._dendrogram": 1, "plotly.figure_factory._distplot": 1, "plotly.figure_factory._facet_grid": 1, "plotly.figure_factory._gantt": 1, "plotly.figure_factory._ohlc": 2, "plotly.figure_factory._quiver": 1, "plotly.figure_factory._scatterplot": 1, "plotly.figure_factory._streamline": 1, "plotly.figure_factory._table": 1, "plotly.figure_factory._trisurf": 1, "plotly.figure_factory._violin": 1, "plotly.figure_factory._county_choropleth": 1, "plotly.figure_factory._hexbin_mapbox": 1, "plotly.figure_factory._ternary_contour": 1, "plotly.express._doc": 1, "plotly.express._chart_types": 1, "skimage": 1, "offline": 1, "_plotlyjs_version": 1, "_visible": 97, "_unselected": 40, "_uirevision": 65, "_uid": 49, "_texttemplatesrc": 19, "_texttemplate": 26, "_textsrc": 40, "_textpositionsrc": 14, "_textposition": 23, "_textfont": 102, "_sum": 2, "_subplot": 11, "_stream": 98, "_showlegend": 42, "_selectedpoints": 24, "_selected": 38, "_opacity": 118, "_name": 121, "_mode": 15, "_metasrc": 50, "_marker": 138, "_line": 136, "_legendwidth": 51, "_legendrank": 50, "_legendgrouptitle": 102, "_legendgroup": 41, "_legend": 52, "_idssrc": 48, "_ids": 48, "_hovertextsrc": 40, "_hovertext": 43, "_hovertemplatesrc": 42, "_hovertemplate": 44, "_hoveron": 8, "_hoverlabel": 98, "_hoverinfosrc": 43, "_hoverinfo": 47, "_fillcolor": 22, "_fill": 34, "_customdatasrc": 50, "_customdata": 50, "_csrc": 1, "_connectgaps": 14, "_cliponaxis": 8, "_c": 1, "_bsrc": 4, "_b": 9, "_asrc": 4, "_a": 4, "_yhoverformat": 22, "_yaxes": 2, "_xhoverformat": 22, "_xaxes": 2, "_showupperhalf": 1, "_showlowerhalf": 1, "_dimensiondefaults": 3, "_dimensions": 3, "_diagonal": 2, "_tickfont": 112, "_sortpaths": 1, "_labelfont": 10, "_domain": 38, "_countssrc": 1, "_counts": 1, "_bundlecolors": 1, "_arrangement": 2, "_zsrc": 19, "_zhoverformat": 11, "_z": 45, "_ysrc": 22, "_y": 112, "_xsrc": 24, "_x": 114, "_wsrc": 2, "_whoverformat": 2, "_w": 2, "_vsrc": 2, "_vhoverformat": 2, "_v": 2, "_usrc": 2, "_uhoverformat": 2, "_u": 2, "_starts": 2, "_sizeref": 14, "_showscale": 39, "_scene": 9, "_reversescale": 53, "_maxdisplayed": 6, "_lightposition": 12, "_lighting": 12, "_colorscale": 58, "_colorbar": 78, "_coloraxis": 54, "_cmin": 44, "_cmid": 43, "_cmax": 44, "_cauto": 43, "_autocolorscale": 53, "_yperiodalignment": 8, "_yperiod0": 8, "_yperiod": 8, "_ycalendar": 12, "_yaxis": 24, "_y0": 12, "_xperiodalignment": 10, "_xperiod0": 10, "_xperiod": 10, "_xcalendar": 14, "_xaxis": 22, "_x0": 12, "_error_y": 10, "_error_x": 10, "_dy": 9, "_dx": 9, "_zorder": 15, "_width": 93, "_spanmode": 1, "_span": 1, "_side": 47, "_scalemode": 1, "_scalegroup": 3, "_quartilemethod": 2, "_points": 1, "_pointpos": 2, "_orientation": 50, "_offsetgroup": 7, "_meanline": 2, "_jitter": 2, "_bandwidth": 1, "_alignmentgroup": 7, "_valuesuffix": 1, "_valueformat": 3, "_node": 2, "_link": 2, "_whiskerwidth": 2, "_upperfencesrc": 1, "_upperfence": 1, "_sizemode": 14, "_showwhiskers": 1, "_sdsrc": 1, "_sdmultiple": 1, "_sd": 1, "_q3src": 1, "_q3": 1, "_q1src": 1, "_q1": 1, "_notchwidth": 1, "_notchspansrc": 1, "_notchspan": 1, "_notched": 1, "_mediansrc": 1, "_median": 1, "_meansrc": 1, "_mean": 1, "_lowerfencesrc": 1, "_lowerfence": 1, "_boxpoints": 1, "_boxmean": 1, "_zmin": 11, "_zmid": 10, "_zmax": 11, "_zauto": 10, "_locationssrc": 10, "_locations": 10, "_geojson": 4, "_featureidkey": 4, "_below": 8, "_ybins": 6, "_ybingroup": 2, "_xbins": 6, "_xbingroup": 2, "_ncontours": 3, "_nbinsy": 3, "_nbinsx": 3, "_histnorm": 3, "_histfunc": 3, "_contours": 8, "_bingroup": 3, "_autocontour": 3, "_autobiny": 3, "_autobinx": 3, "_value": 72, "_title": 110, "_number": 2, "_gauge": 2, "_delta": 2, "_align": 54, "_widthsrc": 28, "_totals": 2, "_textinfo": 7, "_textangle": 8, "_outsidetextfont": 16, "_offsetsrc": 3, "_offset": 7, "_measuresrc": 1, "_measure": 1, "_insidetextfont": 18, "_insidetextanchor": 4, "_increasing": 8, "_decreasing": 8, "_constraintext": 4, "_connector": 4, "_waterfallmode": 1, "_waterfallgroupgap": 1, "_waterfallgap": 1, "_violinmode": 1, "_violingroupgap": 1, "_violingap": 1, "_updatemenudefaults": 1, "_updatemenus": 1, "_uniformtext": 2, "_treemapcolorway": 1, "_transition": 4, "_ternary": 2, "_template": 2, "_sunburstcolorway": 1, "_spikedistance": 1, "_smith": 2, "_sliderdefaults": 1, "_sliders": 1, "_shapedefaults": 1, "_shapes": 1, "_separators": 1, "_selectiondefaults": 1, "_selections": 1, "_selectionrevision": 1, "_selectdirection": 1, "_scattermode": 1, "_scattergap": 1, "_polar": 2, "_plot_bgcolor": 1, "_piecolorway": 1, "_paper_bgcolor": 1, "_newshape": 2, "_newselection": 2, "_modebar": 2, "_minreducedwidth": 1, "_minreducedheight": 1, "_margin": 2, "_mapbox": 2, "_map": 2, "_imagedefaults": 1, "_images": 1, "_iciclecolorway": 1, "_hoversubplots": 1, "_hovermode": 2, "_hoverdistance": 1, "_hidesources": 1, "_hiddenlabelssrc": 1, "_hiddenlabels": 1, "_height": 5, "_grid": 2, "_geo": 4, "_funnelmode": 1, "_funnelgroupgap": 1, "_funnelgap": 1, "_funnelareacolorway": 1, "_font": 342, "_extendtreemapcolors": 1, "_extendsunburstcolors": 1, "_extendpiecolors": 1, "_extendiciclecolors": 1, "_extendfunnelareacolors": 1, "_editrevision": 1, "_dragmode": 2, "_datarevision": 1, "_computed": 1, "_colorway": 1, "_clickmode": 1, "_calendar": 7, "_boxmode": 1, "_boxgroupgap": 1, "_boxgap": 1, "_barnorm": 1, "_barmode": 2, "_bargroupgap": 1, "_bargap": 2, "_barcornerradius": 1, "_autotypenumbers": 10, "_autosize": 1, "_annotationdefaults": 2, "_annotations": 2, "_activeshape": 2, "_activeselection": 2, "_tickwidth": 56, "_opensrc": 2, "_open": 2, "_lowsrc": 2, "_low": 2, "_highsrc": 2, "_high": 2, "_closesrc": 2, "_close": 2, "_zcalendar": 3, "_vertexcolorsrc": 1, "_vertexcolor": 1, "_ksrc": 1, "_k": 1, "_jsrc": 1, "_j": 1, "_isrc": 1, "_intensitysrc": 1, "_intensitymode": 1, "_intensity": 1, "_i": 1, "_flatshading": 3, "_facecolorsrc": 1, "_facecolor": 1, "_delaunayaxis": 1, "_color": 478, "_alphahull": 1, "_lonsrc": 5, "_lon": 9, "_latsrc": 5, "_lat": 9, "_cluster": 4, "_rangefont": 2, "_labelside": 1, "_labelangle": 1, "_ytype": 2, "_xtype": 2, "_transpose": 3, "_hoverongaps": 2, "_thetaunit": 4, "_thetasrc": 3, "_theta0": 3, "_theta": 3, "_rsrc": 3, "_r0": 3, "_r": 8, "_dtheta": 3, "_dr": 3, "_zsmooth": 3, "_ygap": 3, "_xgap": 3, "_radiussrc": 2, "_radius": 4, "_anchor": 3, "_basesrc": 2, "_surfacecolorsrc": 1, "_surfacecolor": 2, "_opacityscale": 2, "_hidesurface": 1, "_traces": 1, "_group": 1, "_baseframe": 1, "_db": 2, "_da": 2, "_cheaterslope": 1, "_baxis": 4, "_b0": 2, "_aaxis": 4, "_a0": 2, "_valuessrc": 10, "_values": 12, "_labelssrc": 5, "_labels": 5, "_label0": 2, "_dlabel": 2, "_baseratio": 1, "_aspectratio": 3, "_btype": 1, "_atype": 1, "_source": 5, "_colormodel": 1, "_sort": 4, "_rotation": 5, "_pullsrc": 1, "_pull": 1, "_insidetextorientation": 2, "_hole": 2, "_direction": 4, "_automargin": 4, "_locationmode": 2, "_stackgroup": 1, "_stackgaps": 1, "_groupnorm": 1, "_fillpattern": 2, "_fillgradient": 2, "_header": 2, "_columnwidthsrc": 1, "_columnwidth": 1, "_columnordersrc": 1, "_columnorder": 1, "_cells": 2, "_surfaceaxis": 1, "_projection": 6, "_error_z": 2, "_tiling": 4, "_root": 6, "_pathbar": 4, "_parentssrc": 3, "_parents": 3, "_maxdepth": 3, "_level": 3, "_leaf": 4, "_count": 6, "_branchvalues": 3, "_valuesrc": 3, "_valuehoverformat": 2, "_spaceframe": 4, "_slices": 4, "_isomin": 2, "_isomax": 2, "_caps": 4, "_cumulative": 2, "_realsrc": 1, "_real": 1, "_imagsrc": 1, "_imag": 1, "_token": 49, "_maxpoints": 49, "_weightsrc": 84, "_weight": 281, "_variantsrc": 84, "_variant": 277, "_textcasesrc": 81, "_textcase": 274, "_stylesrc": 84, "_style": 283, "_sizesrc": 107, "_size": 344, "_shadowsrc": 81, "_shadow": 274, "_linepositionsrc": 81, "_lineposition": 274, "_familysrc": 84, "_family": 281, "_colorsrc": 143, "_namelengthsrc": 46, "_namelength": 47, "_bordercolorsrc": 46, "_bordercolor": 96, "_bgcolorsrc": 55, "_bgcolor": 111, "_alignsrc": 48, "_smoothing": 10, "_dash": 23, "_backoffsrc": 5, "_backoff": 5, "_symbolsrc": 12, "_symbol": 20, "_standoffsrc": 6, "_standoff": 10, "_sizemin": 12, "_opacitysrc": 20, "_gradient": 12, "_anglesrc": 11, "_angleref": 6, "_angle": 14, "_typesrc": 6, "_type": 38, "_yref": 45, "_ypad": 39, "_yanchor": 50, "_xref": 45, "_xpad": 39, "_xanchor": 51, "_tickvalssrc": 57, "_tickvals": 57, "_ticktextsrc": 54, "_ticktext": 54, "_ticksuffix": 54, "_ticks": 54, "_tickprefix": 54, "_tickmode": 54, "_ticklen": 55, "_ticklabelstep": 47, "_ticklabelposition": 41, "_ticklabeloverflow": 41, "_tickformatstopdefaults": 52, "_tickformatstops": 52, "_tickformat": 55, "_tickcolor": 55, "_tickangle": 53, "_tick0": 56, "_thicknessmode": 39, "_thickness": 57, "_showticksuffix": 54, "_showtickprefix": 54, "_showticklabels": 54, "_showexponent": 52, "_separatethousands": 52, "_outlinewidth": 39, "_outlinecolor": 41, "_nticks": 54, "_minexponent": 52, "_lenmode": 40, "_len": 40, "_labelalias": 54, "_exponentformat": 52, "_dtick": 56, "_borderwidth": 47, "_templateitemname": 70, "_enabled": 57, "_dtickrange": 52, "_start": 13, "_end": 12, "_valueminus": 11, "_tracerefminus": 11, "_traceref": 11, "_symmetric": 11, "_arraysrc": 11, "_arrayminussrc": 11, "_arrayminus": 11, "_array": 11, "_copy_ystyle": 4, "_currentbin": 1, "_pattern": 21, "_cornerradius": 3, "_soliditysrc": 9, "_solidity": 9, "_shapesrc": 9, "_fillmode": 9, "_fgopacity": 9, "_fgcolorsrc": 9, "_fgcolor": 9, "_edgeshape": 2, "_squarifyratio": 1, "_pad": 12, "_packing": 1, "_flip": 2, "_depthfade": 1, "_colorssrc": 5, "_colors": 5, "_row": 17, "_column": 17, "_t": 5, "_l": 5, "_show": 25, "_vertexnormalsepsilon": 5, "_specular": 6, "_roughness": 6, "_fresnel": 6, "_facenormalsepsilon": 5, "_diffuse": 6, "_ambient": 6, "_split": 2, "_copy_zstyle": 2, "_scale": 4, "_suffixsrc": 2, "_suffix": 5, "_prefixsrc": 2, "_prefix": 5, "_formatsrc": 2, "_format": 2, "_simplify": 1, "_stop": 1, "_position": 5, "_showlines": 3, "_showlabels": 3, "_operation": 3, "_labelformat": 3, "_coloring": 3, "_startlinewidth": 2, "_startlinecolor": 2, "_startline": 2, "_showline": 14, "_showgrid": 18, "_rangemode": 9, "_range": 15, "_minorgridwidth": 2, "_minorgriddash": 2, "_minorgridcount": 2, "_minorgridcolor": 2, "_linewidth": 14, "_linecolor": 14, "_labelsuffix": 2, "_labelprefix": 2, "_labelpadding": 2, "_gridwidth": 18, "_griddash": 15, "_gridcolor": 18, "_fixedrange": 4, "_endlinewidth": 2, "_endlinecolor": 2, "_endline": 2, "_cheatertype": 2, "_categoryorder": 10, "_categoryarraysrc": 10, "_categoryarray": 10, "_autorange": 9, "_arraytick0": 2, "_arraydtick": 2, "_usecolormap": 3, "_project": 6, "_highlightwidth": 3, "_highlightcolor": 3, "_highlight": 3, "_stepsrc": 2, "_step": 5, "_maxzoom": 4, "_allowoverlap": 2, "_multiselect": 1, "_label": 13, "_constraintrange": 1, "_caxis": 2, "_valign": 3, "_traceorder": 1, "_tracegroupgap": 1, "_itemwidth": 1, "_itemsizing": 1, "_itemdoubleclick": 1, "_itemclick": 1, "_indentation": 1, "_grouptitlefont": 4, "_groupclick": 1, "_entrywidthmode": 1, "_entrywidth": 1, "_ordering": 1, "_easing": 2, "_duration": 2, "_sector": 1, "_radialaxis": 2, "_gridshape": 1, "_angularaxis": 2, "_ysizemode": 1, "_y1shift": 1, "_y1": 2, "_y0shift": 1, "_xsizemode": 1, "_x1shift": 1, "_x1": 2, "_x0shift": 1, "_layer": 14, "_fillrule": 2, "_editable": 1, "_removesrc": 1, "_remove": 1, "_addsrc": 1, "_add": 1, "_activecolor": 2, "_subtitle": 2, "_realaxis": 2, "_imaginaryaxis": 2, "_subunitwidth": 1, "_subunitcolor": 1, "_showsubunits": 1, "_showrivers": 1, "_showocean": 1, "_showland": 1, "_showlakes": 1, "_showframe": 1, "_showcountries": 1, "_showcoastlines": 1, "_scope": 1, "_riverwidth": 1, "_rivercolor": 1, "_resolution": 1, "_oceancolor": 1, "_lonaxis": 2, "_lataxis": 2, "_landcolor": 1, "_lakecolor": 1, "_framewidth": 1, "_framecolor": 1, "_fitbounds": 1, "_countrywidth": 1, "_countrycolor": 1, "_coastlinewidth": 1, "_coastlinecolor": 1, "_center": 8, "_stepdefaults": 2, "_steps": 2, "_minorticklen": 1, "_currentvalue": 2, "_activebgcolor": 1, "_active": 2, "_yshift": 2, "_yclick": 1, "_xshift": 2, "_xclick": 1, "_startstandoff": 2, "_startarrowsize": 2, "_startarrowhead": 2, "_showarrow": 2, "_clicktoshow": 1, "_captureevents": 2, "_borderpad": 2, "_ayref": 1, "_ay": 2, "_axref": 1, "_ax": 2, "_arrowwidth": 2, "_arrowsize": 2, "_arrowside": 2, "_arrowhead": 2, "_arrowcolor": 2, "_sizing": 1, "_sizey": 1, "_sizex": 1, "_minsize": 1, "_zoom": 2, "_pitch": 2, "_layerdefaults": 2, "_layers": 2, "_bounds": 6, "_bearing": 2, "_zerolinewidth": 5, "_zerolinecolor": 5, "_zeroline": 5, "_tickson": 2, "_ticklabelstandoff": 2, "_ticklabelshift": 2, "_ticklabelmode": 2, "_ticklabelindexsrc": 2, "_ticklabelindex": 2, "_spikethickness": 5, "_spikesnap": 2, "_spikemode": 2, "_spikedash": 2, "_spikecolor": 5, "_showspikes": 5, "_showdividers": 2, "_shift": 1, "_scaleratio": 2, "_scaleanchor": 2, "_rangebreakdefaults": 2, "_rangebreaks": 2, "_overlaying": 2, "_mirror": 5, "_minor": 4, "_minallowed": 12, "_maxallowed": 12, "_matches": 3, "_insiderange": 2, "_hoverformat": 12, "_dividerwidth": 2, "_dividercolor": 2, "_constraintoward": 2, "_constrain": 2, "_autotickangles": 3, "_autoshift": 1, "_autorangeoptions": 12, "_accesstoken": 1, "_rangeslider": 2, "_rangeselector": 2, "_sequentialminus": 1, "_sequential": 1, "_diverging": 1, "_autoexpand": 1, "_zaxis": 2, "_camera": 2, "_aspectmode": 1, "_showactive": 1, "_buttondefaults": 2, "_buttons": 2, "_drawdirection": 1, "_yside": 1, "_xside": 1, "_rows": 1, "_roworder": 1, "_columns": 1, "_padding": 2, "_method": 2, "_execute": 2, "_args2": 1, "_args": 2, "_up": 2, "_eye": 2, "_spikesides": 3, "_showbackground": 3, "_showaxeslabels": 3, "_backgroundcolor": 3, "_includesrc": 6, "_include": 6, "_clipmin": 6, "_clipmax": 6, "_dvalue": 2, "_stepmode": 1, "_west": 2, "_south": 2, "_north": 2, "_east": 2, "_sourcetype": 2, "_sourcelayer": 2, "_sourceattribution": 2, "_minzoom": 2, "_coordinates": 2, "_circle": 4, "_placement": 2, "_iconsize": 2, "_icon": 2, "_dashsrc": 2, "_tilt": 1, "_parallels": 1, "_distance": 1, "_roll": 1, "_period": 1, "_min": 3, "_relative": 1, "_reference": 1, "_threshold": 2, "_axis": 4, "_outliercolor": 4, "_outlierwidth": 2, "_targetsrc": 1, "_target": 1, "_sourcesrc": 1, "_labelsrc": 2, "_hovercolorsrc": 1, "_hovercolor": 1, "_colorscaledefaults": 1, "_colorscales": 1, "_arrowlen": 1, "_groups": 1, "_displayindex": 1, "IPython.core.display": 1, "exporter": 4, "pandas.tseries.converter": 1, "vincent": 1, "vega_renderer": 1, "vincent_renderer": 1, "fake_renderer": 1, "statsmodels.api": 1, "plotly.data": 1, "_dimension": 3, "_annotation": 2, "_selection": 1, "_slider": 1, "_updatemenu": 1, "_tickformatstop": 52, "_rangebreak": 2, "_button": 2, "textual.demo.page": 4, "textual.demo.data": 1, "textual.suggester": 2, "textual.demo.game": 1, "textual.demo.home": 1, "textual.demo.projects": 1, "textual.demo.widgets": 1, "textual.renderables._blend_colors": 2, "textual.drivers._input_reader_windows": 1, "textual.drivers._input_reader_linux": 1, "textual.drivers": 1, "textual.drivers._writer_thread": 2, "textual._xterm_parser": 4, "textual._binary_encode": 1, "textual.drivers._byte_stream": 1, "textual.drivers._input_reader": 1, "rich.errors": 1, "textual.css._help_text": 2, "textual.css.transition": 3, "textual.layouts.factory": 2, "textual.css._help_renderables": 5, "textual._duration": 1, "textual.css._style_properties": 1, "textual.css._styles_builder": 1, "textual._profile": 1, "textual.document._document": 6, "textual.document._wrapped_document": 3, "textual._wrap": 1, "textual.document._edit": 3, "textual._resolve": 3, "textual.layouts.horizontal": 1, "textual.widgets._tree": 3, "textual.widgets._tabbed_content": 3, "textual.widgets._tabs": 3, "textual.widgets._content_switcher": 2, "textual.renderables.digits": 1, "textual.widgets._collapsible": 2, "textual.widgets._button": 3, "textual.widgets._checkbox": 1, "textual.widgets._data_table": 2, "textual.widgets._digits": 1, "textual.widgets._footer": 1, "textual.widgets._header": 1, "textual.widgets._help_panel": 1, "textual.widgets._key_panel": 1, "textual.widgets._label": 1, "textual.widgets._link": 1, "textual.widgets._list_item": 2, "textual.widgets._list_view": 1, "textual.widgets._loading_indicator": 1, "textual.widgets._log": 1, "textual.widgets._markdown": 3, "textual.widgets._masked_input": 1, "textual.widgets._pretty": 1, "textual.widgets._progress_bar": 1, "textual.widgets._radio_button": 2, "textual.widgets._radio_set": 1, "textual.widgets._rich_log": 1, "textual.widgets._rule": 2, "textual.widgets._selection_list": 2, "textual.widgets._sparkline": 1, "textual.widgets._static": 5, "textual.widgets._switch": 1, "textual.widgets._text_area": 2, "textual.widgets._tooltip": 1, "textual.widgets._welcome": 1, "textual.clock": 1, "textual.eta": 1, "textual.renderables.bar": 2, "textual._text_area_theme": 2, "textual._tree_sitter": 1, "textual.document._document_navigator": 2, "textual.document._history": 2, "textual.document._syntax_aware_document": 2, "textual.validation": 2, "textual.widgets._toggle_button": 3, "textual._slug": 1, "textual._two_way_dict": 1, "textual.coordinate": 1, "textual.renderables.styled": 1, "textual.renderables.sparkline": 1, "textual._line_split": 1, "textual._immutable_sequence_view": 1, "index": 11, "markdown_it.rules_core": 5, "markdown_it.helpers": 1, "elasticsearch": 1, "elasticsearch.serializer": 1, "streamlit.web": 1, "streamlit.runtime.context_util": 1, "streamlit.proto.WidthConfig_pb2": 5, "streamlit.proto.GapSize_pb2": 1, "streamlit.proto.HeightConfig_pb2": 2, "CoreServices.SearchKit": 1, "Any": 1, "Cc": 1, "Cf": 1, "P": 1, "Z": 1, "vibe_check.core.vcs.plugins": 1, "vibe_check.core.vcs.plugins.plugin_interface": 1, "orchestrator": 2, "pat_core.plugin": 10, "components.sidebar": 2, "components.visualizations": 12, "pages.dashboard": 2, "pages.issues": 2, "pages.files": 2, "pages.meta_systems": 2, "analyzer": 2, "visualizer": 2, "file_manager": 2, "PAT_tool.pipeline": 8, "pipeline": 10, "PAT_tool.hypothesis_stage": 2, "hypothesis_stage": 4, "PAT_tool.tool_stage": 40, "tool_stage": 40, "reporter": 4, "gudhi": 2, "mpld3": 2, "pyvis.network": 2, "PAT_tool.visualization": 6, "chunked_prompt_generator": 4, "PAT_project_analysis.PAT_tool.bandit_stage": 2, "PAT_project_analysis.PAT_tool.black_stage": 2, "PAT_project_analysis.PAT_tool.call_graph_stage": 2, "PAT_project_analysis.PAT_tool.call_graph_visualizer": 2, "PAT_project_analysis.PAT_tool.chunked_prompt_generator": 2, "PAT_project_analysis.PAT_tool.complexity_analyzer": 2, "PAT_project_analysis.PAT_tool.content_extractor": 2, "PAT_project_analysis.PAT_tool.coverage_stage": 2, "PAT_project_analysis.PAT_tool.dependency_analyzer": 2, "PAT_project_analysis.PAT_tool.dependency_stage": 2, "PAT_project_analysis.PAT_tool.effect_overlay_stage": 2, "PAT_project_analysis.PAT_tool.flake8_stage": 2, "PAT_project_analysis.PAT_tool.graph_overlay_stage": 2, "PAT_project_analysis.PAT_tool.hypothesis_stage": 2, "PAT_project_analysis.PAT_tool.isort_stage": 2, "PAT_project_analysis.PAT_tool.meta_system_stage": 2, "PAT_project_analysis.PAT_tool.meta_system_visualizer": 2, "PAT_project_analysis.PAT_tool.models": 2, "PAT_project_analysis.PAT_tool.mypy_stage": 2, "PAT_project_analysis.PAT_tool.pipeline": 2, "PAT_project_analysis.PAT_tool.pre_analysis": 2, "PAT_project_analysis.PAT_tool.protocol_extraction_stage": 2, "PAT_project_analysis.PAT_tool.pycontract_stage": 2, "PAT_project_analysis.PAT_tool.pydocstyle_stage": 2, "PAT_project_analysis.PAT_tool.pylint_stage": 2, "PAT_project_analysis.PAT_tool.pyre_stage": 2, "PAT_project_analysis.PAT_tool.pyright_stage": 2, "PAT_project_analysis.PAT_tool.reporter": 2, "PAT_project_analysis.PAT_tool.ruff_stage": 2, "PAT_project_analysis.PAT_tool.structure_analyzer": 2, "PAT_project_analysis.PAT_tool.tda_overlay_stage": 2, "PAT_project_analysis.PAT_tool.tla_stage": 2, "PAT_project_analysis.PAT_tool.utils": 2, "PAT_project_analysis.PAT_tool.visualization": 2, "bandit_stage": 2, "black_stage": 2, "call_graph_stage": 2, "call_graph_visualizer": 2, "complexity_analyzer": 2, "content_extractor": 2, "coverage_stage": 2, "dependency_analyzer": 2, "dependency_stage": 2, "effect_overlay_stage": 2, "flake8_stage": 2, "graph_overlay_stage": 2, "isort_stage": 2, "meta_system_stage": 2, "mypy_stage": 2, "pre_analysis": 2, "protocol_extraction_stage": 2, "pycontract_stage": 2, "pydocstyle_stage": 2, "pylint_stage": 2, "pyre_stage": 2, "pyright_stage": 2, "ruff_stage": 2, "structure_analyzer": 2, "tda_overlay_stage": 4, "tla_stage": 2, "PAT_project_analysis.PAT_tool.dependency_audit": 2, "dependency_audit": 2, "PAT_tool.tda_overlay_stage": 2, "scripts.project_analyse_tool.main": 2, "vibe_check.core.actor_system.actor": 6, "vibe_check.core.actor_system.message": 10, "vibe_check.core.actor_system.components": 2, "vibe_check.core.actor_system.context_wave": 4, "vibe_check.core.analyzer.project_analyzer": 2, "vibe_check.core.models.progress_tracker": 2, "vibe_check.core.models.project_metrics": 14, "pytest_asyncio": 1, "vibe_check.core.actor_system": 5, "vibe_check.core.actor_system.actor_initializer": 1, "vibe_check.core.actor_system.diagnostics": 1, "vibe_check.core.actor_system.logging": 1, "vibe_check.core.actor_system.enhanced_actor": 2, "vibe_check.core.actor_system.dependency_injection": 3, "vibe_check.core.actor_system.diagnostics.enhanced_tracker": 3, "vibe_check.core.actor_system.consolidated_initializer": 3, "vibe_check.core.actor_system.actor_state": 2, "vibe_check.core.actor_system.integration": 2, "vibe_check.cli.commands": 7, "vibe_check.cli.error_handler": 5, "vibe_check.core.utils.file_utils": 1, "vibe_check.core.config.config_manager": 1, "vibe_check.core.actor_system.messaging.processor": 1, "vibe_check.core.actor_system.initialization.dependency_resolver": 1, "vibe_check.core.actor_system.initialization.synchronization": 1, "vibe_check.core.models.file_metrics": 11, "vibe_check.core.models.directory_metrics": 9, "manager": 1, "manager_provider": 1, "vibe_check.core.actor_system.actors": 1, "generator": 2, "content_collector": 2, "chunk_builder": 2, "manifest": 2, "verification": 2, "schema_validator": 2, "pat_plugins.analyzers.caw_analyzer": 2, "pat_plugins.analyzers.folded_mind_analyzer": 2, "pat_plugins.analyzers.memory_analyzer": 2, "interrogate_plugin": 2, "pat_plugins.core.shared_context": 2, "plugin_manager": 2, "bandit_plugin": 2, "pyright_plugin": 2, "parsers": 2, "metrics": 2, "sidebar": 2, "forms": 2, "visualizations": 2, "plotly.express": 10, "pat_backend": 2, "home": 2, "analysis": 2, "PAT_project_analysis": 1, "vibe_check.compat": 1, "vibe_check.ai.refactoring": 3, "vibe_check.ai.refactoring.refactoring_engine": 1, "vibe_check.ai.infrastructure.model_manager": 2, "vibe_check.ai.explanation": 3, "vibe_check.ai.temporal": 3, "vibe_check.ai.visualization": 1, "vibe_check.ai.visualization.dashboard_engine": 1, "vibe_check.ai.temporal.temporal_engine": 1, "vibe_check.ai.explanation.explanation_engine": 1, "vibe_check.core.analysis.file_analyzer": 2, "vibe_check.core.analysis.project_analyzer": 2, "vibe_check.core.analysis.metrics_aggregator": 2, "PAT_project_analysis.cli": 1, "vibe_check.ui.visualization": 1, "vibe_check.tools.parsers.base_parser": 1, "vibe_check.tools.parsers.parser_registry": 1, "vibe_check.tools.parsers.ruff_parser": 1, "vibe_check.tools.parsers.mypy_parser": 1, "vibe_check.tools.parsers.bandit_parser": 1, "vibe_check.tools.parsers.complexity_parser": 1, "vibe_check.tools.runners.custom_rules_runner": 1, "vibe_check.tools.parsers.custom_rules_parser": 1, "vibe_check.tools.custom_rules.python_rules": 1, "vibe_check.tools.runners.pylint_runner": 1, "vibe_check.tools.runners.pyflakes_runner": 1, "vibe_check.tools.parsers.pylint_parser": 1, "vibe_check.tools.parsers.pyflakes_parser": 1, "vibe_check.core.progress": 1, "vibe_check.core.fs_utils": 1, "vibe_check.plugins.manager": 1, "vibe_check.plugins.base_plugin": 1, "vibe_check.core.analysis.dependency_analyzer": 1, "vibe_check.core.analysis.python_semantic_analyzer": 2, "vibe_check.core.analysis.semantic_rules": 1, "vibe_check.core.analysis.type_analyzer": 1, "vibe_check.core.analysis.standalone_analyzer": 1, "vibe_check.core.analysis.tool_executor": 1, "vibe_check.core.utils.dict_utils": 1, "vibe_check.core.trend_analysis.trend_visualizer": 1, "vibe_check.core.trend_analysis.trend_analyzer": 2, "vibe_check.core.trend_analysis.trend_storage": 3, "vibe_check.core.vcs.rules.security_rules": 1, "vibe_check.core.vcs.rules.style_rules": 1, "vibe_check.tools.runners.ruff_runner": 1, "vibe_check.tools.runners.bandit_runner": 1, "vibe_check.ui.visualization.interactive_charts": 1}, "recommendations": ["Consider removing rarely used dependencies: ['aiofiles', 'vibe_check_monitoring_engine', 'vibe_check.core.vcs.rules.rule_loader', 'vibe_check.core.analysis', 'vibe_check.tools.runners', 'vibe_check.tools.parsers', 'pat_webui.app', 'PAT_tool.main', 'pat_plugins.analyzers', 'pat_plugins.performance', 'pat_synergy', 'pat_core.report_generator', 'pat_plugins.analyzers.codebase_graph_analyzer', 'meta_system_analysis', 'verify_phase_0_completion', 'PAT_tool.meta_system_visualizer', 'pat_project_analysis', 'cli.main', 'src.utils', 'src.calculator', 'calculator', 'flask.debughelpers', 'flask.logging', 'flask.sessions', 'site_package', 'config_module_app', 'config_package_app', 'namespace.package2', 'site_app', 'installed_package', 'flask.json.tag', 'blueprintapp', 'flask.helpers', 'flask.json.provider', 'flask.cli', 'cliapp.app', 'flask.json', 'pallets_sphinx_themes', 'docutils.parsers.rst.roles', 'itsdangerous', 'json.tag', 'sansio.blueprints', 'werkzeug', 'rlcompleter', 'werkzeug.wsgi', 'asgiref.sync', 'jinja2.loaders', 'werkzeug.test', 'scaffold', 'json.provider', 'provider', 'task_app', 'js_example', 'celery', 'celery.result', 'flaskr', 'werkzeug.security', 'db', 'flask.templating', 'blueprintapp.apps.admin', 'blueprintapp.apps.frontend', 'hello', 'grequests', 'flake8.util', 'pipes', '_pytest.junitxml', 'hypothesis.statistics', 'hypothesis.extra._patching', '_pytest._py.error', '_pytest._py.path', 'astroid._backport_stdlib_names', 'astroid.__pkginfo__', 'astroid.astroid_manager', 'astroid.brain.brain_builtin_inference', 'astroid._ast', 'astroid.transforms', 'astroid.interpreter._import.spec', 'astroid.interpreter.objectmodel', 'astroid.constraint', 'marshmallow.base', 'marshmallow.validate', 'marshmallow.error_store', 'marshmallow.orderedset', 'mdurl._decode', 'mdurl._encode', 'mdurl._format', 'mdurl._parse', 'pipenv.project', 'dependencies', 'poetry.packages.locker', '_sync.connection_pool', '_async', '_backends.mock', '_sync', '_backends.anyio', '_backends.trio', 'networkx.drawing', 'networkx.readwrite', 'toml.tz', 'toml.decoder', '_speedups', '_native', 'sorteddict', 'sortedset', 'vulture.core', 'vulture.version', 'vulture', 'vulture.config', 'vulture.reachability', 'vulture.utils', 'h11._connection', 'h11._events', 'h11._state', 'h11._util', 'h11._version', '_readers', '_writers', 'pylint.__pkginfo__', 'pylint.pyreverse.main', 'pylint.checkers.symilar', 'streamlit.elements.alert', 'streamlit.elements.arrow', 'streamlit.elements.balloons', 'streamlit.elements.bokeh_chart', 'streamlit.elements.code', 'streamlit.elements.doc_string', 'streamlit.elements.empty', 'streamlit.elements.form', 'streamlit.elements.graphviz_chart', 'streamlit.elements.html', 'streamlit.elements.iframe', 'streamlit.elements.image', 'streamlit.elements.json', 'streamlit.elements.layouts', 'streamlit.elements.map', 'streamlit.elements.markdown', 'streamlit.elements.media', 'streamlit.elements.metric', 'streamlit.elements.plotly_chart', 'streamlit.elements.progress', 'streamlit.elements.pyplot', 'streamlit.elements.snow', 'streamlit.elements.text', 'streamlit.elements.toast', 'streamlit.elements.vega_charts', 'streamlit.elements.widgets.audio_input', 'streamlit.elements.widgets.button', 'streamlit.elements.widgets.button_group', 'streamlit.elements.widgets.camera_input', 'streamlit.elements.widgets.chat', 'streamlit.elements.widgets.checkbox', 'streamlit.elements.widgets.color_picker', 'streamlit.elements.widgets.data_editor', 'streamlit.elements.widgets.number_input', 'streamlit.elements.widgets.radio', 'streamlit.elements.widgets.selectbox', 'streamlit.elements.widgets.text_widgets', 'streamlit.elements.write', 'authlib', 'streamlit.column_config', 'streamlit.elements.dialog_decorator', 'streamlit.runtime.connection_factory', 'streamlit.runtime.context', 'streamlit.user_info', 'streamlit.commands.experimental_query_params', 'streamlit.commands.echo', 'streamlit.commands.logo', 'streamlit.commands.page_config', 'streamlit.commands.execution_control', 'streamlit.emojis', 'streamlit.material_icon_names', 'streamlit.web.cli', 'attr.setters', 'attr.validators', 'attr._next_gen', 'attr.exceptions', 'attr.converters', 'attr.filters', 'pygments.formatters.latex', 'pygments.formatters.terminal256', 'pygments.lexers.special', 'pygments.regexopt', 'pygments.cmdline', 'ast_transforms', 'c_lexer', 'plyparser', 'c_parser', 'c_ast', 'lextab', 'yacctab', '_ast_gen', '_pytest.doctest', '_pytest.freeze_support', '_pytest.legacypath', '_pytest.logging', '_pytest.recwarn', '_completion_classes', 'click.exceptions', 'click.termui', 'click.formatting', 'click.types', 'rich.emoji', 'rich_utils', 'typer.main', '_legacy', '_password_hasher', '_argon2_cffi_bindings', 'diagram', 'idtracking', 'optimizer', '_identifier', 'defaults', 'filters', 'tests', 'sandbox', 'cryptography.__about__', 'cryptography.hazmat.primitives.hmac', 'nltk.collections', 'numpypy', 'nltk.downloader', '_bcrypt', 'metaclass', 'contracts.utils', 'enabling', 'useful_contracts', 'backported', 'docstring_parsing', 'inspection', 'decorator', 'library.extensions', 'main_actual', 'importlib._bootstrap', 'cffi.api', 'setuptools.command.build_py', 'distutils.msvc9compiler', 'verifier', 'recompiler', 'cffi_opcode', 'pycparser.lextab', 'pycparser.yacctab', 'commontypes', 'gitignore', 'patterns.gitwildmatch', 'rules_block.state_block', 'rules_core', 'rules_inline.state_inline', 'linkify_it', 'parser_block', 'parser_core', 'parser_inline', 'renderer', 'misc.timeTools', 'ttLib', 'ttLib.tables._c_m_a_p', 'ttLib.tables._g_l_y_f', 'ttLib.tables.O_S_2f_2', 'cffLib', 'varLib.builder', 'varLib', 'feaLib.builder', 'ttLib.tables._f_v_a_r', 'fontTools.misc.sstruct', 'EasyDialogs', 'altair.vegalite.v5.theme', 'altair.jupyter', 'altair._magics', '_ffi', '_textwrap', '_termui_impl', '_winconsole', 'after', 'before', 'before_sleep', 'nap', 'stop', 'tenacity.asyncio', 'tenacity.tornadoweb', 'libcst._typed_visitor_base', 'libcst._version', 'checker', 'snowballstemmer', 'pydocstyle', 'wordlists', 'pytz.lazy', 'isort.utils', 'isort.literal', 'place', 'pylama.lint', 'isort.comments', 'sections', '_vendored', 'deprecated.finders', 'isort.parse', 'logo', 'isort.main', 'isort.format', 'identify', 'shellingham._core', 'bandit.cli', 'scipy_doctest.conftest', 'numpy.rec', 'numpy.char', '_expired_attrs_2_0', '_globals', 'numpy.__config__', 'lib._arraypad_impl', 'lib._arraysetops_impl', 'lib._function_base_impl', 'lib._histograms_impl', 'lib._index_tricks_impl', 'lib._nanfunctions_impl', 'lib._npyio_impl', 'lib._polynomial_impl', 'lib._shape_base_impl', 'lib._stride_tricks_impl', 'lib._twodim_base_impl', 'lib._type_check_impl', 'lib._ufunclike_impl', 'matrixlib', '_array_api_info', 'charset_normalizer.cd', 'legacy', 'cd', '_multibytecodec', 'PyQt6.QtCore', 'PyQt6.QtGui', 'PySide6.QtCore', 'PySide6.QtGui', 'PcxImagePlugin', 'JpegImagePlugin', 'TiffTags', 'defusedxml', 'IPython.lib.pretty', 'TiffImagePlugin', 'JpegPresets', 'MpoImagePlugin', '_imaging', '_imagingft', 'urllib3.fields', 'urllib3.filepost', 'urllib3.poolmanager', 'urllib3.util.retry', 'urllib3.util.ssl_', 'urllib3.contrib.socks', 'safety.formatters.bare', 'safety.formatters.html', 'safety.formatters.screen', 'safety.formatters.text', 'safety.asyncio_patch', 'safety.alerts', 'safety.auth', 'safety.firewall.command', 'safety_schemas.config.schemas.v3_0', 'scan.main', 'pydantic.json', 'output_utils', 'dparse.updater', 'asyncio.proactor_events', 'typer.rich_utils', 'safety.auth.cli_utils', 'safety.cli', 'safety_schemas.models.events.constants', 'mando.core', 'argcomplete', 'mando.napoleon', 'funcsigs', 'rst2ansi', 'streams.buffered', '_core._resources', '_core._signals', '_core._tempfile', '_interpqueues', '_interpreters', 'tornado.speedups', 'tornado._locale_data', 'tornado.autoreload', 'dataclasses_json.stringcase', 'dataclasses_json.api', 'dataclasses_json.cfg', 'dataclasses_json.core', 'dataclasses_json.mm', 'marshmallow_enum', 'buf', 'mman', 'contourpy._version', 'contourpy.chunk', '_contourpy', '_urlparse', '_transports', '_main', 'h2', '_transports.base', '_transports.default', 'func_inspect', 'hashing', 'memory', 'dask.distributed', 'dask.sizeof', 'dask.utils', 'distributed.utils', 'numpy_pickle_compat', 'externals.loky.backend', 'externals.loky.backend.resource_tracker', 'externals', 'executor', 'pool', 'externals.loky.reusable_executor', 'pipdeptree._models.package', 'pipdeptree._cli', 'pipdeptree._detect_env', 'pipdeptree._discovery', 'pipdeptree._render', 'pipdeptree._validate', 'curio.meta', 'converters', '_cmp', '_next_gen', '_version_info', 'annotationlib', 'jsonschema._format', 'rfc3987', 'webcolors', 'jsonpointer', 'uri_template', 'isoduration', 'fqdn', 'rfc3986_validator', 'rfc3339_validator', 'pkgutil_resolve_name', 'narwhals.group_by', 'narwhals.series_cat', 'narwhals.series_dt', 'narwhals.series_list', 'narwhals.series_str', 'narwhals.series_struct', 'sqlframe', 'sqlframe._version', 'narwhals.stable.v1._namespace', 'narwhals.expr_cat', 'narwhals.expr_dt', 'narwhals.expr_list', 'narwhals.expr_name', 'narwhals.expr_str', 'narwhals.expr_struct', 'generic', 'refinement', '_dist_ver', 'keras', 'tensorflow', '_tqdm_pandas', 'dask.callbacks', 'autonotebook', 'mypy.partially_defined', 'mypy.semanal_pass1', 'mypy.freetree', 'mypy.metastore', 'mypy.plugins.default', 'mypy.renaming', 'mypy.stats', 'mypy.refinfo', 'mypy.plugins.dataclasses', 'orjson', '_curses', 'mypy.bogus_type', 'mypy.semanal_classprop', 'mypy.semanal_infer', 'mypy.semanal_typeargs', 'mypy.copytype', 'mypy.fswatcher', 'mypy.suggestions', 'sqlite3.dbapi2', 'mypy.split_namespace', 'mypy.binder', 'mypy.checkpattern', 'mypy.semanal_namedtuple', 'mypy.semanal_newtype', 'mypy.semanal_typeddict', 'mypy.evalexpr', 'radon', '_cext', 'Stemmer', 'arabic_stemmer', 'armenian_stemmer', 'basque_stemmer', 'catalan_stemmer', 'danish_stemmer', 'dutch_stemmer', 'english_stemmer', 'finnish_stemmer', 'french_stemmer', 'german_stemmer', 'greek_stemmer', 'hindi_stemmer', 'hungarian_stemmer', 'indonesian_stemmer', 'irish_stemmer', 'italian_stemmer', 'lithuanian_stemmer', 'nepali_stemmer', 'norwegian_stemmer', 'porter_stemmer', 'portuguese_stemmer', 'romanian_stemmer', 'russian_stemmer', 'serbian_stemmer', 'spanish_stemmer', 'swedish_stemmer', 'tamil_stemmer', 'turkish_stemmer', 'yiddish_stemmer', 'graphs', 'jupyter_integration', 'quoting', 'backend.execute', 'rich.json', 'rich._windows_renderer', 'rich.styled', 'rich._inspect', '_stack', 'rich.containers', 'pgen2', 'pgen2.grammar', 'pgen2.token', 'testslide.lib', 'patch', 'testslide.matchers', 'asyncio.log', 'testslide.patch_attribute', 'testslide.runner', 'testslide.dsl', 'runner', 'strict_mock', 'testslide.import_profiler', '_manager', '_callers', 'nodejs_wheel', 'matplotlib._path', 'matplotlib.backend_managers', 'backends.registry', '_mathtext', 'matplotlib._cm', 'matplotlib._cm_bivar', 'matplotlib._cm_listed', 'matplotlib._cm_multivar', 'style.core', 'lines', 'matplotlib._animation_data', 'textpath', 'matplotlib._constrained_layout', 'matplotlib._tight_layout', 'matplotlib.typing', 'ticker', 'IPython.core.pylabtools', 'matplotlib.hatch', 'kiwisolver', 'matplotlib._image', 'matplotlib._layoutgrid', '_color_data', 'dumper', 'loader', 'emitter', 'composer', 'reader', 'modulefinder', 'pycompat', 'stdlib_list', 'render_context', 'pydeps.configs', 'arguments', 'dummymodule', 'pystdlib', 'pydeps', 'depgraph2dot', 'watchdog.events', 'hypothesis.entry_points', 'hypothesis.internal.detection', 'hypothesis.internal.scrutineer', 'hypothesis.stateful', 'hypothesis.strategies._internal.featureflags', 'multidict', 'middlewares', 'black.concurrency', 'aiohttp.typedefs', 'aiohttp.web_middlewares', 'aiohttp.web_request', 'aiohttp.web_response', 'blackd', 'contrib.emscripten', 'util.ssltransport', 'util.wait', '_utilities', 'jsonschema_specifications._core', 'packaging.licenses', '_distutils._modified', 'depends', 'org.python.modules.posix.PosixModule', '_static', 'installer', 'command.bdist_wheel', '_typeshed.importlib', 'consts', '_internal._repr', 'functional_serializers', 'validate_call_decorator', 'deprecated.class_validators', 'deprecated.config', 'deprecated.tools', 'pydantic._internal', '_internal._core_utils', '_internal._validators', 'pydantic.main', 'deprecated.parse', 'deprecated.json', '_parse', 'pathspec.patterns.gitwildmatch', 'black.handle_ipynb_magics', 'blib2to3', 'blib2to3.pgen2.parse', 'tokenize_rt', 'IPython.core.inputtransformer2', 'json.decoder', 'black.const', 'black.files', 'black.linegen', 'black.parsing', 'black.ranges', 'black.brackets', 'blib2to3.pgen2.token', 'black.rusty', 'black.numerics', 'black.trans', 'black._width_table', '_pslinux', '_pswindows', '_pssunos', 'lib2to3.main', 'libfuturize.fixes', 'coverage.tomlconfig', 'coverage.annotate', 'coverage.collector', 'coverage.context', 'coverage.html', 'coverage.inorout', 'coverage.jsonreport', 'coverage.lcovreport', 'coverage.multiproc', 'coverage.report', 'coverage.xmlreport', 'coverage.templite', 'coverage.plugins', 'coverage.execfile', 'ox_profile.core.launchers', 'eventlet.greenthread', 'gevent', 'coverage.pytracer', 'coverage.sysmon', 'coverage.tracer', 'coverage.numbits', 'coverage.sqlitedb', 'pudb', 'coverage.parser', 'coverage.regions', '_pypy_irc_topic', '_structseq', 'coverage.cmdline', 'testrepository', 'pbr.pbr_json', 'reno', 'sha', '__info__', 'session', 'dbm.ndbm', 'xdrlib', '_pyio', 'diff', '_shims', 'detect', 'pointers', 'temp', 'libpasteurize.fixes', 'typeguard.importhook', '_pydantic_core', 'core_schema', 'exc', 'git.objects.blob', 'tomlkit.source', 'tomlkit.parser', 'IPython.core.interactiveshell', 'traitlets.config', 'pandas.core.config_init', 'pandas.core.computation.api', 'pandas.core.reshape.api', 'pandas.io.api', 'pandas.tseries.api', 'pandas.util._tester', 'tz.win', 'docutils.parsers', 'driver', 'hook', 'nbextension', 'twisted.trial.unittest', 'twisted.trial.itrial', 'zope.interface', 'py', '_pytest._io.wcwidth', '_pytest.warnings', 'argcomplete.completers', '_pytest.pytester_assertions', 'pyarrow._orc', 'pyarrow.orc', 'pyarrow._flight', 'pyarrow._substrait', '_generated_version', 'setuptools_scm.git', 'pyarrow._dataset_orc', 'pyarrow._dataset_parquet', 'pyarrow._dataset_parquet_encryption', 'pyarrow._cuda', 'pyarrow._feather', 'concurrent.futures.thread', 'pandas.api.internals', 'pyarrow._fs', 'pyarrow._azurefs', 'pyarrow._hdfs', 'pyarrow._gcsfs', 'pyarrow._s3fs', 'pyarrow._acero', 'pyarrow._csv', 'pyarrow._json', 'pyarrow.vendored', '_pytest._pluggy', 'pytest_metadata.ci', 'pytest_metadata', 'conftest', 'tzdata', 'numba.cuda.cudadrv.devicearray', 'pyarrow._pyarrow_cpp_tests', 'pyarrow.cffi', 'pyarrow.jvm', 'jpype.imports', 'java.lang', 'pyarrow.tests.test_io', 'sparse', 'test_fs', 'test_cython', 'pandas_examples', 'test_extension_type', 'from_dataframe', 'pyarrow.interchange.buffer', 'pyarrow._parquet_encryption', 'pandas.api.interchange', 'findpaths', 'argparsing', '_pytest.helpconfig', '_pytest._argcomplete', 'expression', '_code', '_pytest._code.source', '_pytest._io.pprint', '_pytest.assertion.rewrite', 'importlib.resources.abc', 'importlib.readers', 'importlib.resources.readers', 'terminalwriter', '_code.source', 'data_utils.binary_transfer', '_frontend', 'debounce', 'bindings.view_state', 'color_scales', 'viewport_helpers', 'io.html', 'base_map_provider', 'map_styles', 'deck', 'light_settings', 'pydeck.exceptions', 'data_utils', 'stevedore.dispatch', 'stevedore.extension', 'stevedore.example2', 'testtools.matchers', 'ordereddict', 'configobj', 'ruamel.yaml.main', 'ruamel.yaml.composer', 'ruamel.yaml.parser', 'ruamel.yaml.reader', 'ruamel.yaml.dumper', 'ruamel.yaml.loader', 'isoparser', '_factories', 'win', 'tz', 'pandas._libs.ops_dispatch', 'pandas.core.util', 'pandas.core.methods.describe', 'pandas.core.interchange.dataframe', 'pandas.core.methods.to_dict', 'pandas.io.formats.xml', 'pandas._libs.indexing', 'pandas.core._numba.executor', 'pandas.core._numba.extensions', 'pandas.compat.pickle_compat', 'pandas.core.computation.pytables', 'bs4', 'lxml.html', 'pandas.io.clipboards', 'pandas.io.gbq', 'pandas.io.sas', 'pandas.io.spss', 'botocore.exceptions', 'sqlalchemy.sql.expression', 'sqlalchemy.types', 'google.auth.credentials', 'hypothesis.extra.dateutil', 'pandas._testing._io', 'pandas._testing._warnings', 'pandas._testing.asserters', 'pandas._testing.compat', 'pandas._libs.testing', 'pandas._libs.pandas_parser', 'pandas._libs.pandas_datetime', 'pandas.plotting._misc', 'pandas._config.display', 'pandas.plotting._matplotlib.boxplot', 'pandas.plotting._matplotlib.timeseries', 'fsspec.implementations.memory', 'fsspec.registry', 'boto3', 'pandas.tests.io.generate_legacy_storage_files', 'adbc_driver_manager', 'adbc_driver_postgresql', 'adbc_driver_sqlite', 'sqlalchemy.dialects.postgresql', 'sqlalchemy.sql', 'sqlalchemy.dialects.mysql', 'botocore.response', 'pandas.api.typing', 'pandas.tests.api.test_api', 'pandas.tests.indexing.test_floats', 'pandas._libs.index', '_csv', 's3fs', 'xlrd.biffh', 'botocore', 'pandas.tests.extension.date', 'pandas.tests.extension.array_with_attr', 'pandas.tests.extension.array_with_attr.array', 'pandas.tests.extension.date.array', 'pandas.tests.extension.base.accumulate', 'pandas.tests.extension.base.casting', 'pandas.tests.extension.base.constructors', 'pandas.tests.extension.base.dim2', 'pandas.tests.extension.base.dtype', 'pandas.tests.extension.base.getitem', 'pandas.tests.extension.base.groupby', 'pandas.tests.extension.base.index', 'pandas.tests.extension.base.interface', 'pandas.tests.extension.base.io', 'pandas.tests.extension.base.methods', 'pandas.tests.extension.base.missing', 'pandas.tests.extension.base.ops', 'pandas.tests.extension.base.printing', 'pandas.tests.extension.base.reduce', 'pandas.tests.extension.base.reshaping', 'pandas.tests.extension.base.setitem', 'pandas.io.parsers.arrow_parser_wrapper', 'pandas.io.parsers.python_parser', 'pandas.io.formats.html', 'pandas.io.formats.string', 'pandas.io.formats.csvs', 'pandas.io.formats._color_data', 'pandas.io.formats.console', 'openpyxl.descriptors.serialisable', 'openpyxl.workbook', 'openpyxl.cell.cell', 'pandas.io.excel._calamine', 'pandas.io.excel._odfreader', 'pandas.io.excel._pyxlsb', 'pandas.io.excel._xlrd', 'pandas.io.excel._odswriter', 'pandas.io.excel._xlsxwriter', 'pyxlsb', 'odf.style', 'odf.config', 'odf.element', 'odf.office', 'xlsxwriter', 'pandas.io.sas.sas_constants', 'pandas._libs.sas', 'pandas.io.sas.sas_xport', 'qtpy', 'PyQt4', 'qtpy.QtWidgets', 'PyQt5.QtWidgets', 'PyQt4.QtGui', 'pandas.core.indexers.utils', 'pandas._libs.window.indexers', 'pandas._libs.reshape', 'pandas.core.reshape.encoding', 'pandas._libs.hashing', 'pandas.core.interchange.buffer', 'pandas.core.groupby.categorical', 'pandas.core.groupby.indexing', 'pandas.core.internals.api', 'pandas.core.internals.concat', 'pandas.core.internals.ops', 'pandas.core.computation.align', 'numba.core', 'numba.core.datamodel', 'numba.core.extending', 'numba.core.imputils', 'numba.typed', 'pandas.core.window.online', 'pandas.core.window.ewm', 'pandas.core.ops.dispatch', 'pandas.core.ops.docstrings', 'pandas.core.roperator', 'pandas.core.indexes.category', 'pandas.core.arrays.arrow.accessors', 'pandas.core.arrays.sparse.scipy_sparse', 'pandas.core.arrays.sparse.accessor', 'numba.extending', 'pandas.core._numba.kernels.mean_', 'pandas.core._numba.kernels.min_max_', 'pandas.core._numba.kernels.var_', 'submodule', 'git.refs.log', 'git.objects.fun', 'git.refs.tag', 'git.refs.symbolic', 'root', 'feature_base', 'multiprocess', 'test_mixins', 'test_dictviews', 'test_classdef', 'xml', 'dill.tests.__main__', 'dill.source', 'dill.detect', 'dill._objects', 'test_moduledict', 'dill.temp', 'test_source', 'pox', 'dill.logger', 'gitdb.db.mem', 'virtualenv', 'testresources', 'copy_reg', '_markupbase', 'markupbase', 'socketserver', 'SocketServer', 'UserList', 'UserString', 'future.types.newstr', 'future.utils.surrogateescape', 'newbytes', 'newint', 'newstr', 'newdict', 'newlist', 'newobject', 'newrange', 'future.moves.subprocess', 'future.types.newdict', 'future.types.newint', 'future_builtins', 'future.builtins.new_min_max', 'future.builtins.newnext', 'future.builtins.newround', 'future.builtins.newsuper', 'future.builtins.iterators', 'future.builtins.misc', '_weakref', 'misc', '_strptime', '_datetime', 'future.moves.test', 'future.moves.dbm', 'future.backports.test', 'future.backports.http.cookiejar', 'ftplib', 'nturl2path', '_scproxy', 'future.backports.html.entities', 'future.backports.http.client', 'future.backports.xmlrpc.client', 'xml.parsers', 'future.backports.email.errors', 'future.backports.email.quoprimime', 'future.standard_library.email._policybase', 'future.standard_library.email.headerregistry', 'future.standard_library.email.utils', 'future.backports.email.parser', 'uu', 'future.backports.email._encoded_words', 'future.backports.email.generator', 'future.backports.email.iterators', 'quopri', 'future.backports.email.feedparser', 'future.backports.email.message', 'future.backports.email.header', 'future.backports.email._parseaddr', 'future.backports.email.mime.base', 'sndhdr', 'imghdr', 'test.test_support', 'urllib.response', 'urllib.robotparser', 'robotparser', 'tkinter.scrolledtext', 'ScrolledText', 'tkinter.colorchooser', 'tkColorChooser', 'tkinter.commondialog', 'tkCommonDialog', 'tkMessageBox', 'tkinter.dialog', 'Dialog', 'tkinter.constants', 'Tkconstants', 'tkinter.dnd', 'Tkdnd', 'Tkinter', 'ttk', 'FileDialog', 'tkFileDialog', 'tkFont', 'tkinter.tix', 'Tix', 'SimpleDialog', 'Cookie', 'BaseHTTPServer', 'CGIHTTPServer', 'SimpleHTTPServer', 'cookielib', 'dbm.gnu', 'gdbm', 'anydbm', 'whichdb', 'dbm.dumb', 'dumbdbm', 'xmlrpc.server', 'libpasteurize.fixes.fix_division', 'lib2to3.fixes.fix_input', 'lib2to3.fixes.fix_xrange', 'lib2to3.fixes.fix_imports', 'lib2to3.fixes.fix_urllib', 'lib2to3.fixes.fix_import', 'libfuturize.fixes.fix_print', 'psutil._psutil_linux', 'psutil._psposix', 'psutil.tests.test_process_all', 'win32con', 'win32process', 'wmi', 'psutil._pswindows', 'pydantic.color', 'pydantic.v1.decorator', 'pydantic.v1.env_settings', 'pydantic.v1.tools', 'pydantic.v1.color', 'eval_type_backport', 'pydantic.fields', '_validate_call', '_serializers', '_std_types_schema', 'pydantic._internal._serializers', '_repr', 'pydantic._internal._internal_dataclass', '_loader', 'alias_generators', '_internal._import_utils', 'rfc6749.parameters', 'rfc7636', 'rfc7518', 'rfc7519', 'rfc8037', 'rfc5849.errors', 'client_auth', 'rsa', 'sync_openid', 'assertion_session', 'oauth1_session', 'endpoints', 'django.utils.module_loading', 'django.utils.functional', 'client_mixin', 'functions', 'tokens_mixins', 'starlette.datastructures', 'starlette.responses', 'base_client.async_app', 'base_client.async_openid', 'httpx_client', 'cache', 'assertion_client', 'oauth1_client', 'jwt', 'asymmetric_key', 'jwk', 'authlib.jose.rfc7516.models', 'jwe', 'cryptography.hazmat.primitives.padding', 'jwe_algs', 'jwe_encs', 'jwe_zips', 'jws_algs', 'cryptography.hazmat.primitives.asymmetric.utils', 'cryptography.hazmat.primitives.keywrap', '_jwe_algorithms', '_jwe_enc_cryptography', '_jwe_enc_cryptodome', 'authlib.jose.rfc7518', 'authlib.jose.rfc8037', 'cryptography.hazmat.primitives.ciphers.aead', 'Cryptodome.Cipher', 'cryptography.hazmat.primitives.asymmetric.ed448', 'cryptography.hazmat.primitives.asymmetric.ed25519', 'cryptography.hazmat.primitives.asymmetric.x448', 'cryptography.hazmat.primitives.asymmetric.x25519', 'jws_eddsa', 'jws', 'authlib.oauth2.rfc8414', 'authlib.oauth2.rfc8414.models', 'authlib.oauth2.rfc6749.grants.authorization_code', 'hybrid', 'device_code', 'token_endpoint', 'rfc7591', 'rfc7591.claims', 'jwt_bearer', 'challenge', 'parameter', 'authlib.oauth2.rfc6750.token', 'authlib.jose.rfc7519', 'rfc7662', 'authlib.oauth2.rfc6750.validator', 'authorization_code', 'client_credentials', 'refresh_token', 'resource_owner_password_credentials', 'pkg1', 'pkg1.pkg2', 'nspkg', 'nspkg.subpkg', 'test_resources', 'distutils.command.install_egg_info', 'mod', 'mod2', 'py39', 'email.headerregistry', '_apply_pyprojecttoml', '_distutils_hack', 'setuptools.logging', 'setuptools._core_metadata', 'config.downloads', 'setuptools.tests.textwrap', 'setuptools.command.build_clib', 'setuptools.command.build', 'setuptools.command.install_scripts', 'integration.helpers', 'setuptools.package_index', 'test_easy_install', 'setuptools.command.develop', 'setuptools.command.editable_wheel', 'setuptools._normalization', 'setuptools.tests.server', 'dl', 'Cython.Distutils.build_ext', 'build_py', 'dist_info', 'install', 'install_scripts', '_vendor.wheel.wheelfile', 'modified', 'setuptools.unicode_utils', 'bdist_egg', 'easy_install', 'compat.numpy', 'compilers.C.cygwin', 'distutils.versionpredicate', 'distutils.version', 'distutils.command.install_data', 'distutils.unixccompiler', 'xx', 'distutils.tests.support', 'distutils.command.install_headers', 'distutils.command.clean', 'distutils.command.check', 'distutils.command.bdist_dumb', 'distutils.file_util', 'distutils.tests.test_dist', 'distutils.command.config', '_msvccompiler', 'docutils.frontend', 'docutils.utils', 'filelist', 'text_file', 'archive_util', '_macos_compat', 'distutils.compat', 'distutils.tests.compat.py39', 'distutils.cygwinccompiler', 'test.support.import_helper', 'jaraco.test.cpython', 'setuptools.config.setupcfg', 'tomli_w', 'ini2toml.api', 'setuptools.config._apply_pyprojecttoml', 'downloads', 'setuptools.compat.py310', 'extra_validations', 'fastjsonschema_validations', 'setuptools._vendor.packaging', 'trove_classifiers', 'vendored.packaging.requirements', '_bdist_wheel', 'vendored.packaging', 'macosx_libfile', 'wheel.util', 'more', 'autocommand.errors', 'typeguard._exceptions', 'typeguard._importhook', 'typeguard._utils', '_importhook', '_suppression', 'typeshed.stdlib.types', '_union_transformer', 'vendored.packaging.tags', 'unpack', 'pack', 'convert', 'inflect', 'jaraco.context', '_operator', 'pyodide.ffi', 'urllib3.connection', 'hypothesis.strategies._internal.datetime', 'hypothesis.strategies._internal.ipaddress', 'hypothesis.extra.django._impl', 'dpcontracts', 'hypothesis.extra._array_helpers', 'lark', 'lark.grammar', 'lark.lark', 'lark.lexer', 'hypothesis.strategies._internal.deferred', 'hypothesis.strategies._internal.flatmapped', 'hypothesis.utils.terminal', '_hypothesis_pytestplugin', 'django.contrib.staticfiles', 'django.core.exceptions', 'django.db', 'hypothesis.extra.django._fields', 'django.contrib.auth.forms', 'django.core.validators', 'hypothesis.extra.pandas.impl', 'hypothesis.internal.conjecture.pareto', 'hypothesis.internal.conjecture.shrinking', 'hypothesis.internal.conjecture.shrinking.choicetree', 'hypothesis.internal.constants_ast', 'hypothesis.internal.conjecture.datatree', 'hypothesis.internal.conjecture.optimiser', 'hypothesis.internal.conjecture.dfa', 'hypothesis.internal.conjecture.shrinking.bytes', 'hypothesis.internal.conjecture.shrinking.floats', 'hypothesis.internal.conjecture.shrinking.ordering', 'hypothesis.internal.conjecture.shrinking.string', 'strategies', 'hypothesis.internal.cathetus', 'hypothesis.internal.charmap', 'hypothesis.strategies._internal.functions', 'hypothesis.strategies._internal.recursive', 'hypothesis.strategies._internal.shared', 'hypothesis.strategies._internal.strings', 'hypothesis.strategies._internal.random', 'hypothesis.strategies._internal.attrs', '_hypothesis_ftz_detector', 're._constants', 're._parser', 'sre_constants', 'sre_parse', '_triangulation', '_tricontour', '_trifinder', '_triinterpolate', '_tripcolor', '_triplot', '_trirefine', '_tritools', 'matplotlib.tri._triinterpolate', 'matplotlib.tri._tritools', 'matplotlib.inset', 'matplotlib.stackplot', 'matplotlib.streamplot', 'matplotlib.axes._secondary_axes', '_axes', 'sphinx', 'matplotlib.backends.qt_editor.figureoptions', 'backend_qtagg', 'cairocffi', 'matplotlib._tight_bbox', 'wx.lib.wxcairo', 'ipykernel.comm', 'wx.svg', 'backend_qtcairo', '_tkagg', 'shiboken6', 'sip', 'shiboken2', 'PySide2', '_afm', 'matplotlib._mathtext_data', 'matplotlib.backends._backend_agg', 'matplotlib.backends.backend_webagg_core', 'matplotlib.backends._backend_pdf_ps', 'matplotlib.backends.backend_pgf', 'matplotlib._type1font', 'matplotlib.animation', 'matplotlib.backends.backend_qt5agg', 'matplotlib.backends.backend_qt5cairo', 'matplotlib.textpath', 'PIL.TiffTags', 'matplotlib.sankey', 'matplotlib.dviread', 'matplotlib.style.core', 'matplotlib.backends.backend_template', 'matplotlib.backends.backend_qtagg', 'matplotlib.projections.geo', 'matplotlib.legend_handler', 'sphinx_gallery', 'filecmp', 'geo', 'polar', 'matplotlib.style', 'compare', 'Duration', 'Epoch', 'EpochConverter', 'StrConverter', 'UnitDbl', 'UnitDblConverter', 'UnitDblFormatter', 'blib2to3.pgen2.driver', 'dot_command', 'execute', 'piping', 'rendering', 'unflattening', 'upstream_version', 'viewing', 'engines', 'radon.contrib.flake8', 'test_complexity_visitor', 'mypy.dmypy_os', 'mypy.server.mergecheck', 'mypy.test.testfinegrained', 'mypy.stubgen', 'mypy.server.subexpr', 'mypy.test.update_data', 'mypy.plugins.functools', 'mypy.server.astmerge', 'mypy.server.objgraph', 'slack', 'telegram', 'discord', 'requests.utils', 'slack_sdk', 'type_variable_operators', 'narwhals.stable', 'narwhals._polars.group_by', 'polars.dataframe.group_by', 'polars.lazyframe.group_by', 'narwhals._polars.typing', 'narwhals._dask.group_by', 'dask_expr._groupby', 'narwhals._dask.selectors', 'narwhals._dask.expr_dt', 'narwhals._dask.expr_str', 'narwhals._arrow.group_by', 'narwhals._arrow.series_cat', 'narwhals._arrow.series_dt', 'narwhals._arrow.series_list', 'narwhals._arrow.series_str', 'narwhals._arrow.series_struct', 'narwhals._arrow.selectors', 'pyarrow._stubs_typing', 'narwhals._duckdb.group_by', 'narwhals._duckdb.series', 'narwhals._duckdb.selectors', 'narwhals._duckdb.expr_dt', 'narwhals._duckdb.expr_list', 'narwhals._duckdb.expr_str', 'narwhals._duckdb.expr_struct', 'narwhals._duckdb.typing', 'ibis.selectors', 'narwhals._ibis.series', 'modin.pandas.utils', 'narwhals._pandas_like.series_cat', 'narwhals._pandas_like.series_dt', 'narwhals._pandas_like.series_list', 'narwhals._pandas_like.series_str', 'narwhals._pandas_like.series_struct', 'narwhals._pandas_like.selectors', 'narwhals._pandas_like.typing', 'narwhals._spark_like.group_by', 'sqlframe.base.functions', 'sqlframe.base.types', 'pyspark.pandas.spark.functions', 'sqlframe.base.session', 'narwhals._spark_like.selectors', 'narwhals._spark_like.expr_dt', 'narwhals._spark_like.expr_list', 'narwhals._spark_like.expr_str', 'narwhals._spark_like.expr_struct', 'narwhals._spark_like.typing', 'narwhals.stable.v1.dtypes', 'narwhals.selectors', 'narwhals.stable.v1._dtypes', 'atheris', 'curio', 'ecosystem', 'scan', 'config_protocol', 'report_protocol', 'policy_file', 'events.payloads', 'onboarding', 'freeze', 'json_tree', 'mermaid', 'pipdeptree._models.dag', 'pipdeptree._freeze', 'dag', 'joblib._utils', 'joblib.disk', 'joblib.executor', 'joblib.pool', 'joblib.numpy_pickle_utils', 'joblib.test', 'test_memory', 'posix', 'joblib.externals.loky', '_openmp_test_helper.parallel_sum', 'joblib.externals.loky.process_executor', 'memory_profiler', 'joblib.test.test_func_inspect_special_encoding', 'distributed.metrics', 'distributed.utils_test', 'joblib._dask', 'joblib.test.test_parallel', 'tmp_joblib_', 'joblib.logger', 'opcode', '_collections_abc', 'cloudpickle', 'cloudpickle_wrapper', 'reusable_executor', 'backend.queues', 'backend.utils', 'initializers', '_posix_reduction', 'joblib.externals', 'process', 'queues', 'synchronize', 'multiprocessing.popen_spawn_win32', 'resource_tracker', 'popen_loky_win32', 'popen_loky_posix', 'asgi', 'default', 'wsgi', 'contourpy.util.mpl_util', 'contourpy.util._build_config', 'bokeh.io', 'bokeh.io.export', 'bokeh.layouts', 'bokeh.models.annotations.labels', 'bokeh.palettes', 'bokeh.plotting', 'contourpy.util.bokeh_util', 'bokeh.core.enums', 'bokeh.models', 'selenium.webdriver.remote.webdriver', 'smmap.buf', 'smmap.test.lib', 'tornado.auth', 'wsgiref.validate', 'tornado.curl_httpclient', 'tornado.test.httpclient_test', 'twisted', 'twisted.names', 'tornado.platform.caresresolver', 'tornado.locale', 'tornado.test.runtests', 'twisted.internet.abstract', 'twisted.names.cache', 'twisted.names.client', 'twisted.names.hosts', 'twisted.names.resolve', 'twisted.python', 'pip._internal.operations.build.wheel', 'pip._internal.operations.build.wheel_editable', 'pip._internal.operations.build.wheel_legacy', '_macos', '_openssl', 'fallback', '_cmsgpack', '__pypy__.builders', 'pip._vendor.pygments.formatters.latex', 'pip._vendor.pygments.formatters.terminal', 'pip._vendor.pygments.formatters.terminal256', 'pip._vendor.pygments.lexers.special', 'pip._vendor.pygments.regexopt', 'pip._vendor.pygments.cmdline', '_abcoll', '_osx_support', '_aix_support', '_frozen_importlib_external', '_frozen_importlib', 'java', 'resources', 'pip._vendor.cachecontrol.filewrapper', 'pip._vendor.cachecontrol.wrapper', 'pip._vendor.urllib3.fields', 'pip._vendor.urllib3.filepost', 'pip._vendor.urllib3.util.retry', 'pip._vendor.urllib3.contrib.socks', '_in_process', 'pip._vendor.rich.json', 'pip._vendor.rich._windows_renderer', 'pip._vendor.rich.cells', 'pip._vendor.rich.tree', 'pip._vendor.rich.styled', 'pip._vendor.rich._inspect', 'pip._vendor.rich.repr', 'pip._vendor.rich.containers', 'pip._vendor.rich.padding', 'pip._vendor.rich.measure', 'pip._vendor.rich.traceback', 'urllib3_secure_extra', 'util.queue', 'packages.backports.weakref_finalize', 'pip._internal.utils._jaraco_text', 'pip._vendor.platformdirs.windows', 'pip._vendor.platformdirs.macos', 'pip._vendor.platformdirs.unix', 'pip._vendor.platformdirs.android', 'contrib', '_securetransport.bindings', '_securetransport.low_level', 'cryptography.hazmat.backends.openssl', 'google.appengine.api', 'ntlm', 'pip._vendor.cachecontrol.caches.file_cache', 'pip._vendor.cachecontrol.caches.redis_cache', 'pip._vendor.pygments.modeline', 'pip._vendor.pygments.formatters._mapping', 'pip._vendor.pygments.styles._mapping', 'pip._vendor.packaging.licenses._spdx', 'keyring', 'pip._vendor.requests.auth', 'pip._vendor.requests.utils', 'pip._internal.network.auth', 'pip._internal.utils.glibc', 'pip._vendor.cachecontrol', 'pip._vendor.urllib3.connectionpool', 'pip._vendor.cachecontrol.caches', 'pip._vendor.rich.logging', 'pip._vendor.rich.progress', 'pip._internal.cli.autocompletion', 'pip._internal.resolution.resolvelib.resolver', 'pip._internal.resolution.legacy.resolver', 'pip._internal.distributions.installed', 'pip._internal.network.download', 'pip._internal.network.lazy_wheel', 'pip._internal.operations.build.metadata', 'pip._internal.operations.build.metadata_editable', 'pip._internal.operations.build.metadata_legacy', 'pip._internal.operations.install.editable_legacy', 'pip._internal.operations.install.wheel', 'pip._internal.pyproject', 'pip._internal.req.req_uninstall', 'req_file', 'req_install', 'req_set', 'pip._internal.vcs.bazaar', 'pip._internal.vcs.git', 'pip._internal.vcs.mercurial', 'pip._internal.vcs.subversion', 'pip._internal.commands.search', 'pip._internal.network.xmlrpc', 'pip._internal.models.installation_report', 'pip._internal.distributions.sdist', 'pip._internal.distributions.wheel', '_envs', 'pip._vendor.pkg_resources', 'pip._vendor.resolvelib.providers', 'pip._vendor.resolvelib.reporters', 'found_candidates', 'pip._internal.resolution.resolvelib.provider', 'pip._internal.resolution.resolvelib.reporter', 'pip._vendor.resolvelib.structs', 'compileall', 'pip._vendor.distlib.scripts', 'pip._vendor.distlib.util', 'trio.from_thread', 'outcome', 'trio.socket', 'trio.to_thread', 'trio.testing', 'asyncio.base_events', 'anyio._core._asyncio_selector_thread', 'streams.stapled', 'streams.tls', 'mando.napoleon.docstring', 'mando.napoleon.iterators', 'mando.napoleon.pycompat', 'sphinx.ext.napoleon', 'safety.init.models', 'render', 'tool', 'cli_utils', 'safety.auth.server', 'authlib.integrations.base_client.errors', 'requests.adapters', 'safety.formatters.schemas.zero_five', 'obj', 'tools', 'vulnerabilities', 'safety.scan.decorators', 'safety.scan.finder.file_finder', 'safety.scan.fun_mode.easter_eggs', 'spdx_tools.spdx.model', 'spdx_tools.spdx.model.spdx_no_assertion', 'spdx_tools.spdx.writer.write_utils', 'spdx_tools.spdx', 'ecosystems.base', 'ecosystems.target', 'init.main', 'tool.interceptors', 'github', 'tool_inspector', 'definitions', 'safety.tool.pip', 'safety.tool.poetry', 'safety.tool.uv.main', 'interceptors', 'safety.tool.typosquatting', 'scan.util', 'unix', 'windows', 'safety.tool.intents', 'safety.tool.pip.parser', 'pip.command', 'safety.tool.poetry.parser', 'emission', 'safety_schemas.models.events.context', 'event_bus', 'types.base', 'conditions', 'safety.init.types', 'safety.events.utils.context', 'safety.firewall.events.utils', 'safety.scan.fun_mode.celebration_effects', 'file_finder', 'python.main', 'python.dependencies', 'safety_schemas.models.package', 'common.schemas', 'scans.schemas.base', 'common.const', 'common.exceptions', 'pydantic.validators', 'charset_normalizer.md', 'charset_normalizer.models', 'charset_normalizer.version', '_convertions', 'pickle5', 'py3k', 'numpy._core.defchararray', '_linalg', 'extras', 'numpy._utils._inspect', 'einsumfunc', 'function_base', 'getlimits', 'memmap', 'records', 'shape_base', '_machar', '_ctypes', '_asarray', '_dtype', '_string_helpers', '_type_aliases', 'code_generators.genapi', 'code_generators.numpy_api', 'printoptions', '_dtype_like', '_nbit', '_scalars', '_ufunc', 'PyInstaller.compat', 'PyInstaller.utils.hooks', 'numpy._typing._add_docstring', 'numpy.f2py._backends', 'numpy_distutils', 'numpy_distutils.command.build_flib', 'numpy.distutils.fcompiler', 'numpy_distutils.fcompiler', 'numpy.distutils.cpuinfo', 'numpy_distutils.command.cpuinfo', 'numpy_distutils.cpuinfo', '_private', '_private.utils', '_scimath_impl', '_array_utils_impl', '_ufunclike_impl', '_user_array_impl', '_arrayterator_impl', '_npyio_impl', '_stride_tricks_impl', '_datasource', '_iotools', 'numpy.matrixlib', 'numpy.lib._histograms_impl', '_helper', '_pocketfft', 'defmatrix', 'chebyshev', 'hermite', 'hermite_e', 'laguerre', 'legendre', 'numpy.polynomial.chebyshev', 'numpy.polynomial.hermite_e', 'numpy.polynomial.laguerre', 'numpy.polynomial.legendre', 'numpy.polynomial.hermite', 'numpy.linalg.tests.test_linalg', 'numpy.random._common', 'numpy.random.bit_generator', 'numpy.random._examples.numba', 'numpy.random._examples.cffi', 'numpy.lib._arraypad_impl', 'numpy.lib._datasource', 'numpy.lib._nanfunctions_impl', 'win32pdh', 'unittest.case', '_meson', '_distutils', 'numpy.distutils.core', 'numpy.f2py.tests', 'numpy.f2py._src_pyf', 'numpy.f2py.auxfuncs', 'numpy._core._type_aliases', 'numpy.f2py.symbolic', 'numpy.f2py._backends._meson', 'numpy.typing.mypy_plugin', 'numpy.lib.user_array', 'limited_api1', 'limited_api2', 'limited_api_latest', 'numpy._core._machar', 'array_interface_testing', 'numpy.lib._shape_base_impl', 'numpy.lib.tests.test_io', 'mem_policy', 'numpy._core._operand_flag_tests', 'numpy._core._umath_tests', 'numpy._core.getlimits', 'numpy.testing.overrides', '_testbuffer', 'Cython', 'Cython.Build', 'numpy.lib.mixins', 'numpy.linalg.lapack_lite', 'numpy.linalg._linalg', 'bandit.core.utils', 'bandit.plugins', 'sarif_om', 'jschema_to_python.to_json', 'pipreqs', 'pip_api', 'tomli._parser', 'axes_grid', 'axes3d', 'floating_axes', 'grid_helper_curvelinear', 'mpl_toolkits.axisartist.axis_artist', 'mpl_toolkits.axisartist.angle_helper', 'mpl_toolkits.axisartist.grid_helper_curvelinear', 'mpl_toolkits.axisartist.floating_axes', 'mpl_toolkits.mplot3d.art3d', 'mpl_toolkits.mplot3d.axes3d', 'mpl_toolkits.axes_grid1.mpl_axes', 'mpl_toolkits.axes_grid1.anchored_artists', 'mpl_toolkits.axes_grid1.axes_rgb', 'hypothesmith', 'libcst.codegen.transforms', 'libcst._parser.conversions.expression', 'libcst._parser.conversions.module', 'libcst._parser.conversions.params', 'libcst._parser.conversions.statement', 'libcst._parser.conversions.terminals', 'libcst._parser.base_parser', 'libcst._parser', 'libcst._parser.python_parser', 'libcst.native', 'libcst.display.graphviz', 'libcst.matchers._visitors', 'libcst.matchers._return_types', 'libcst.metadata.accessor_provider', 'libcst.metadata.file_path_provider', 'libcst.metadata.parent_node_provider', 'libcst.metadata.reentrant_codegen', 'libcst.helpers._template', 'libcst.helpers.node_fields', 'libcst.codemod._dummy_pool', 'libcst.codemod._cli', 'libcst.codemod._command', 'libcst.codemod._testing', 'libcst.codemod.visitors._gather_comments', 'libcst.codemod.commands.unnecessary_format_string', 'libcst.codemod.commands.fix_pyre_directives', 'libcst.codemod.commands.remove_unused_imports', 'libcst.codemod.commands.convert_type_comments', 'libcst.codemod.commands.convert_format_to_fstring', 'libcst.codemod.commands.convert_union_to_or', 'libcst.codemod.commands.rename_typing_generic_aliases', 'libcst.codemod.commands.convert_percent_format_to_fstring', 'libcst.codemod.commands.ensure_import_present', 'libcst.codemod.commands.noop', 'libcst.codemod.commands.convert_namedtuple_to_dataclass', 'libcst.codemod.commands.fix_variadic_callable', 'libcst.codemod.commands.add_trailing_commas', 'libcst.codemod.commands.strip_strings_from_types', 'libcst.codemod.commands.remove_pyre_directive', 'libcst.codemod.commands.add_pyre_directive', 'libcst.metadata.tests.test_type_inference_provider', 'libcst.tests.test_pyre_integration', 'libcst.tool', 'libcst._parser.types.py_token', 'libcst._parser.parso.python.py_token', 'libcst._parser.parso.pgen2.grammar_parser', 'libcst.codegen.generate', 'tenacity.stop', 'tenacity.wait', 'vegafusion', 'vl_convert', 'altair.utils._dfi_types', 'vegafusion.runtime', '_vegafusion_data', 'v5', 'v5.api', 'v5.schema', 'altair.utils.selection', 'jupyter_chart', 'altair.utils.compiler', 'altair.vegalite.v5', 'altair.vegalite.v5.compiler', 'altair.utils.mimebundle', 'altair.vegalite.display', 'altair.utils._show', 'display', 'schema._typing', 'schema._config', 'schema.channels', 'schema.core', 'altair.utils.save', 'altair.utils.server', 'altair.utils._transformed_data', 'fontTools.cu2qu.benchmark', 'fontTools.pens.qu2cuPen', 'psOperators', 'roundTools', 'textTools', 'fontTools.encodings.codecs', 'fontTools.misc.xmlWriter', 'xattr', 'ufoLib2', 'defcon', 'ufo', 'fontTools.subset.svg', 'fontTools.ttLib.tables._n_a_m_e', 'fontTools.ttLib.tables.S_V_G_', 'fontTools.subset', 'fontTools.voltLib.ast', 'fontTools.voltLib.lexer', 'fontTools.feaLib', 'fontTools.voltLib', 'fontTools.voltLib.parser', 'fontTools.merge.unicode', 'fontTools.merge.tables', 'fontTools.merge.layout', 'fontTools.merge.options', 'fontTools.merge', 'fontTools.designspaceLib.split', 'fontTools.ttLib.tables.ttProgram', 'fontTools.varLib.stat', 'cff', 'fontTools.pens.cairoPen', 'fontTools.varLib.interpolatableHelpers', 'scipy.optimize', 'munkres', 'scipy.sparse.csgraph', 'interpolatableTestContourOrder', 'interpolatableTestStartingPoint', 'interpolatablePlot', 'fontTools.pens.areaPen', 'fontTools.misc.dictTools', 'fontTools.ttLib.tables._c_m_a_p', 'fontTools.misc.testTools', 'fontTools.mtiLib', 'PyQt5.QtGui', 'fontTools.qu2cu', 'reportlab.graphics.shapes', 'reportlab.lib', 'reportlab.graphics', 'fontTools.misc.symfont', 'freetype', 'freetype.ft_enums', 'freetype.ft_errors', 'freetype.ft_structs', 'freetype.ft_types', 'freetype.raw', 'Quartz.CoreGraphics', 'fs.copy', 'fs.subfs', 'fs.tempfs', 'fs.tools', 'fs.zipfs', 'fontTools.ufoLib.converters', 'fontTools.ufoLib.glifLib', 'fs.path', 'fontTools.misc.plistlib', 'fontTools.misc.etree', 'fontTools.designspaceLib.statNames', 'fontTools.feaLib.parser', 'fontTools.feaLib.lexer', 'fontTools.feaLib.builder', 'fontTools.ttLib.woff2', 'zopfli.zlib', 'pathops', 'tables.DefaultTable', 'reorderGlyphs', 'fontTools.misc.visitor', 'CFFToCFF2', 'CFF2ToCFF', 'width', 'Res', 'fontTools.misc.psOperators', 'Carbon', 'geometry', 'sbixStrike', 'lz4.block', 'otData', 'otConverters', 'fontTools.ttLib.standardGlyphOrder', 'E_B_D_T_', 'fontTools.misc.filenames', 'sbixGlyph', 'uharfbuzz', 'DefaultTable', 'fontTools.ttLib.tables.C_F_F_', 'fontTools.otlLib.optimize', 'featureVars', 'fontTools.cffLib.CFF2ToCFF', 'fontTools.ttLib.removeOverlaps', 'shapes', 'arc', 'common.html_blocks', 'blockquote', 'fence', 'heading', 'hr', 'html_block', 'lheading', 'list', 'paragraph', 'block', 'inline', 'normalize', 'replacements', 'smartquotes', 'text_join', 'entities', 'mdurl', 'autolink', 'backticks', 'balance_pairs', 'entity', 'escape', 'fragments_join', 'html_inline', 'link', 'newline', 'common.entities', 'parse_link_destination', 'parse_link_label', 'parse_link_title', 'gitwildmatch', 'contextlib2', 'numpy_specific', 'types_misc', 'contracts.library.extensions', 'arithmetic', 'attributes', 'comparison', 'dicts', 'dummy', 'files', 'lists', 'map', 'separate_context', 'seq', 'simple_values', 'strings', 'tuple', 'variables', 'extensions', 'isinstance_imp', 'miscellaneous_aliases', 'scoped_variables', 'contracts.inspection', 'contracts.library.simple_values', 'pyparsing_utils', 'array_ops', 'past.builtins', 'contracts.library', 'nose.tools', 'contracts.library.array', 'referencing.retrieval', 'identifiers', 'typeshed', 'platform_aware', 'python_version', 'scheduler_policies', 'shared_memory', 'unwatched', 'persistent', 'pyre_language_server', 'pyre_extensions.type_variable_operators', 'daemon_querier', 'document_formatter', 'pyre_language_server_error', 'source_code_context', 'pyre_server_options', 'query_response', 'tabulate', 'language_server.protocol', 'libcst_vendored_visitors', 'initialize', '_apply_type_annotations', '_gather_global_names', 'commands.codemods', 'commands.configurationless', 'commands.consolidate_nested_configurations', 'commands.expand_target_coverage', 'commands.fix_configuration', 'commands.fixme', 'commands.fixme_all', 'commands.fixme_single', 'commands.global_strictness', 'commands.global_version_update', 'commands.pysa_version_update', 'commands.strict_default', 'commands.support_sqlalchemy', 'commands.targets_to_configuration', 'consolidate_nested_configurations', 'fixme', 'commands.command', 'strict_default', 'client.find_directories', 'encodings.raw_unicode_escape', 'encodings.unicode_escape', 'google.protobuf.duration_pb2', 'google.protobuf.any_pb2', 'google.protobuf.timestamp_pb2', 'google._upb', 'nltk.misc.babelfish', 'nltk.misc.chomsky', 'nltk.misc.minimalset', 'nltk.misc.wordfinder', 'svgling', 'nltk.tree.parsing', 'nltk.tree.probabilistic', 'nltk.metrics.aline', 'nltk.metrics.association', 'nltk.metrics.confusionmatrix', 'nltk.metrics.paice', 'nltk.metrics.scores', 'nltk.metrics.segmentation', 'scipy.stats.stats', 'nltk.twitter.twitterclient', 'nltk.twitter.api', 'twython.exceptions', 'nltk.cluster.api', 'nltk.cluster.em', 'nltk.cluster.gaac', 'nltk.cluster.kmeans', 'nltk.app.chartparser_app', 'nltk.app.chunkparser_app', 'nltk.app.collocations_app', 'nltk.app.concordance_app', 'nltk.app.nemo_app', 'nltk.app.rdparser_app', 'nltk.app.srparser_app', 'nltk.app.wordfreq_app', 'nltk.classify.decisiontree', 'nltk.classify.positivenaivebayes', 'nltk.classify.senna', 'nltk.classify.textcat', 'nltk.classify.weka', 'nltk.classify.tadm', 'sklearn.feature_extraction', 'sklearn.preprocessing', 'sklearn.linear_model', 'sklearn.naive_bayes', 'nltk.chunk.named_entity', 'nltk.chat.eliza', 'nltk.chat.iesha', 'nltk.chat.rude', 'nltk.chat.suntsu', 'nltk.chat.zen', 'norm', 'nltk.translate.api', 'nltk.translate.ibm1', 'nltk.translate.ibm2', 'nltk.translate.ibm3', 'nltk.translate.ibm4', 'nltk.translate.ibm5', 'nltk.translate.metrics', 'nltk.translate.chrf_score', 'nltk.translate.gale_church', 'nltk.translate.gleu_score', 'nltk.translate.phrase_based', 'nltk.lm.smoothing', 'nltk.lm.models', 'nltk.lm.util', 'nltk.sem.boxer', 'nltk.sem.evaluate', 'nltk.sem.lfg', 'nltk.sem.relextract', 'nltk.sem.util', 'bllipparser', 'bllipparser.RerankingParser', 'nltk.parse.earleychart', 'nltk.parse.evaluate', 'nltk.parse.nonprojectivedependencyparser', 'nltk.parse.projectivedependencyparser', 'nltk.parse.recursivedescent', 'nltk.parse.shiftreduce', 'nltk.parse.transitionparser', 'nltk.parse.viterbi', 'profile', 'sklearn.datasets', 'nltk.tbl.template', 'nltk.tbl.erroranalysis', 'nltk.stem.arlstem', 'nltk.stem.arlstem2', 'nltk.stem.cistem', 'nltk.stem.isri', 'nltk.stem.lancaster', 'nltk.stem.regexp', 'nltk.stem.rslp', 'nltk.stem.wordnet', 'nltk.stem.util', 'nltk.inference.discourse', 'nltk.inference.resolution', 'nltk.inference.tableau', 'nltk.sentiment', 'sklearn.svm', 'nltk.sentiment.sentiment_analyzer', 'nltk.sentiment.vader', 'nltk.draw.cfg', 'nltk.draw.dispersion', 'pycrfsuite', 'nltk.tag.brill_trainer', 'nltk.tag.tnt', 'nltk.tag.hunpos', 'nltk.tag.stanford', 'nltk.tag.hmm', 'nltk.tag.senna', 'nltk.tag.crf', 'nltk.tag.perceptron', 'nltk.tokenize.legality_principle', 'nltk.tokenize.mwe', 'nltk.tokenize.punkt', 'nltk.tokenize.regexp', 'nltk.tokenize.repp', 'nltk.tokenize.sexpr', 'nltk.tokenize.sonority_sequencing', 'nltk.tokenize.stanford_segmenter', 'nltk.tokenize.texttiling', 'nltk.tokenize.toktok', 'nltk.tokenize.treebank', 'nltk.ccg.chart', 'nltk.ccg.logic', 'nltk.toolbox', 'nltk.corpus.reader.cmudict', 'nltk.corpus.reader.conll', 'nltk.corpus.reader.chunked', 'nltk.corpus.reader.ppattach', 'nltk.corpus.reader.senseval', 'nltk.corpus.reader.ieer', 'nltk.corpus.reader.sinica_treebank', 'nltk.corpus.reader.indian', 'nltk.corpus.reader.toolbox', 'nltk.corpus.reader.ycoe', 'nltk.corpus.reader.rte', 'nltk.corpus.reader.string_category', 'nltk.corpus.reader.propbank', 'nltk.corpus.reader.verbnet', 'nltk.corpus.reader.bnc', 'nltk.corpus.reader.nps_chat', 'nltk.corpus.reader.switchboard', 'nltk.corpus.reader.dependency', 'nltk.corpus.reader.nombank', 'nltk.corpus.reader.ipipan', 'nltk.corpus.reader.pl196x', 'nltk.corpus.reader.knbc', 'nltk.corpus.reader.chasen', 'nltk.corpus.reader.childes', 'nltk.corpus.reader.aligned', 'nltk.corpus.reader.lin', 'nltk.corpus.reader.semcor', 'nltk.corpus.reader.framenet', 'nltk.corpus.reader.udhr', 'nltk.corpus.reader.sentiwordnet', 'nltk.corpus.reader.twitter', 'nltk.corpus.reader.nkjp', 'nltk.corpus.reader.crubadan', 'nltk.corpus.reader.mte', 'nltk.corpus.reader.reviews', 'nltk.corpus.reader.opinion_lexicon', 'nltk.corpus.reader.pros_cons', 'nltk.corpus.reader.categorized_sents', 'nltk.corpus.reader.comparative_sents', 'nltk.corpus.reader.panlex_lite', 'nltk.corpus.reader.panlex_swadesh', 'nltk.corpus.reader.bcp47', 'ossaudiodev', 'pygame.mixer', 'mdit_plain.renderer', 'mdit_py_plugins.front_matter', 'nltk.help', 'nltk.corpus.europarl_raw', 'cryptography.x509.certificate_transparency', 'cryptography.hazmat.primitives.constant_time', 'cryptography.hazmat.bindings.openssl._conditional', 'cryptography.hazmat.primitives.twofactor.hotp', 'cryptography.hazmat.primitives.serialization.base', 'cryptography.hazmat.primitives.serialization.ssh', 'email.base64mime', 'bcrypt', 'cryptography.hazmat.decrepit.ciphers.algorithms', 'cryptography.hazmat.primitives.ciphers.base', 'cryptography.hazmat.bindings.openssl', 'cryptography.hazmat.primitives.asymmetric.padding', 'past', 'basestring', 'olddict', 'oldstr', 'lib2to3.pgen2.parse', 'lib2to3.refactor', 'libfuturize', 'past.types', 'past.builtins.noniterators', 'past.builtins.misc', 'railroad', 'pygments.lexers.factor', 'pygments.lexers.iolang', 'pygments.lexers.tcl', 'pygments.lexers._asy_builtins', 'pygments.lexers._ada_builtins', 'pygments.lexers._cocoa_builtins', 'pygments.lexers._usd_builtins', 'pygments.lexers.webmisc', 'pygments.lexers._sourcemod_builtins', 'pygments.lexers._scheme_builtins', 'pygments.lexers._cl_builtins', 'pygments.lexers._lua_builtins', 'pygments.lexers._luau_builtins', 'pygments.modeline', 'pygments.lexers.automation', 'pygments.lexers.dsls', 'pygments.lexers.ecl', 'pygments.lexers.esoteric', 'pygments.lexers.pawn', 'pygments.lexers.rebol', 'pygments.lexers.robotframework', 'pygments.lexers.smalltalk', 'pygments.lexers.smv', 'pygments.lexers.snobol', 'pygments.lexers.testing', 'pygments.lexers.urbi', 'pygments.lexers.dotnet', 'pygments.lexers.lean', 'pygments.lexers.ada', 'pygments.lexers.c_like', 'pygments.lexers.crystal', 'pygments.lexers.dylan', 'pygments.lexers.felix', 'pygments.lexers.fortran', 'pygments.lexers.go', 'pygments.lexers.nimrod', 'pygments.lexers.ooc', 'pygments.lexers.pascal', 'pygments.lexers.rust', 'pygments.lexers._lilypond_builtins', 'pygments.lexers.lilypond', 'pygments.lexers._css_builtins', 'pygments.lexers.modula2', 'pygments.scanner', 'pygments.lexers._csound_builtins', 'pygments.lexers.erlang', 'pygments.lexers.haskell', 'pygments.lexers._vim_builtins', 'pygments.lexers._openedge_builtins', 'pygments.lexers.mime', 'pygments.lexers._qlik_builtins', 'pygments.lexers.console', 'pygments.lexers.haxe', 'pygments.lexers.make', 'pygments.lexers.sgf', 'pygments.lexers.textfmts', 'pygments.lexers.algebra', 'pygments.lexers.idl', 'pygments.lexers.julia', 'pygments.lexers.matlab', 'pygments.lexers.r', 'pygments.lexers._php_builtins', 'pygments.lexers._mysql_builtins', 'pygments.lexers._postgres_builtins', 'pygments.unistring', 'pygments.lexers._lasso_builtins', 'pygments.lexers._julia_builtins', 'pygments.lexers._stata_builtins', 'pygments.formatters._mapping', 'pygments.styles._mapping', 'streamlit.watcher.local_sources_watcher', 'streamlit.watcher.polling_path_watcher', 'streamlit.watcher.event_based_path_watcher', 'streamlit.watcher.folder_black_list', 'streamlit.config', 'streamlit.web.bootstrap', 'streamlit.runtime.credentials', 'streamlit.temporary_directory', 'streamlit.proto.Delta_pb2', 'streamlit.proto.GitInfo_pb2', 'streamlit.proto.NewSession_pb2', 'requests.exceptions', 'streamlit.runtime.runtime', 'streamlit.components.lib.local_component_registry', 'streamlit.runtime.websocket_session_manager', 'sqlalchemy.engine.base', 'sqlalchemy.exc', 'snowflake.snowpark._internal.utils', 'streamlit.connections.base_connection', 'streamlit.connections.snowflake_connection', 'streamlit.connections.snowpark_connection', 'streamlit.connections.sql_connection', 'snowflake.connector.cursor', 'snowflake.connector.pandas_tools', 'snowflake.snowpark.exceptions', 'wave', 'streamlit.elements.lib.subtitle_utils', 'streamlit.proto.Audio_pb2', 'streamlit.proto.Video_pb2', 'tensorflow.python.keras.utils', 'streamlit.proto.ArrowVegaLiteChart_pb2', 'streamlit.proto.GraphVizChart_pb2', 'streamlit.proto.Snow_pb2', 'streamlit.proto.Html_pb2', 'streamlit.proto.Empty_pb2', 'streamlit.proto.Skeleton_pb2', 'streamlit.proto.IFrame_pb2', 'streamlit.proto.DocString_pb2', 'streamlit.proto.Balloons_pb2', 'streamlit.elements.lib.streamlit_plotly_theme', 'streamlit.proto.PlotlyChart_pb2', 'streamlit.proto.Progress_pb2', 'streamlit.proto.Spinner_pb2', 'bokeh', 'streamlit.proto.BokehChart_pb2', 'bokeh.plotting.figure', 'bokeh.embed', 'streamlit.proto.PageConfig_pb2', 'streamlit.proto.Navigation_pb2', '_testcapi', 'langchain.callbacks.base', 'langchain.schema', 'streamlit.external.langchain.streamlit_callback_handler', 'streamlit.proto.LabelVisibilityMessage_pb2', 'streamlit.proto.CameraInput_pb2', 'streamlit.proto.DownloadButton_pb2', 'streamlit.proto.LinkButton_pb2', 'streamlit.proto.PageLink_pb2', 'streamlit.proto.FileUploader_pb2', 'streamlit.proto.AudioInput_pb2', 'streamlit.testing.v1.local_script_runner', 'streamlit.testing.v1.util', 'streamlit.runtime.state.query_params_proxy', 'streamlit.runtime.state.widgets', 'streamlit.runtime.scriptrunner.exec_code', 'streamlit.runtime.caching.cache_data_api', 'streamlit.runtime.caching.cache_resource_api', 'streamlit.runtime.caching.legacy_cache_api', 'streamlit.web.server.app_static_file_handler', 'streamlit.web.server.media_file_handler', 'streamlit.web.server.upload_file_request_handler', 'streamlit.web.server.oauth_authlib_routes', 'streamlit.web.server.server', 'streamlit.web.server.authlib_tornado_integration', 'pylint.config._pylint_config', 'pylint.config.config_initialization', 'pylint.lint.base_options', 'pylint.lint.caching', 'pylint.lint.expand_modules', 'pylint.lint.message_state_handler', 'pylint.lint.parallel', 'pylint.lint.report_functions', 'pylint.reporters.text', 'pylint.reporters.ureports', 'pylint.utils.pragma_parser', 'astroid.brain.brain_dataclasses', 'enchant', 'enchant.tokenize', 'astroid.helpers', 'pylint.checkers.deprecated', 'pylint.graph', 'pylint.utils.linterstats', 'pylint.config.deprecation_actions', 'pylint.config.argument', 'pylint.config.help_formatter', 'pylint.config.find_default_config_files', 'pylint.config.config_file_parser', 'pylint.message.message_id_store', 'pylint.message.message_definition_store', 'pylint.message._deleted_message_ids', 'pylint.utils.ast_walker', 'pylint.utils.docs', 'pylint.utils.file_state', 'pylint.utils.utils', 'isort.api', 'pylint.extensions', 'pylint.extensions._check_docs_utils', 'mccabe', 'pylint.checkers.exceptions', 'pylint.pyreverse.dot_printer', 'pylint.pyreverse.mermaidjs_printer', 'pylint.pyreverse.plantuml_printer', 'pylint.pyreverse', 'pylint.pyreverse.printer_factory', 'pylint.pyreverse.diadefslib', 'pylint.reporters.collecting_reporter', 'pylint.reporters.multi_reporter', 'pylint.reporters.reports_handler_mix_in', 'pylint.reporters', 'pylint.reporters.ureports.text_writer', 'pylint.testutils.checker_test_case', 'pylint.testutils.decorator', 'pylint.testutils.functional', 'pylint.testutils.get_test_info', 'pylint.testutils.global_test_linter', 'pylint.testutils.tokenize_str', 'pylint.testutils.unittest_linter', 'pylint.testutils.utils', 'pylint.testutils._primer', 'pylint.testutils._primer.primer_compare_command', 'pylint.testutils._primer.primer_prepare_command', 'pylint.testutils._primer.primer_run_command', 'pylint.testutils._primer.package_to_lint', 'pylint.testutils.functional.find_functional_tests', 'pylint.testutils.functional.lint_module_output_update', 'pylint.config._pylint_config.main', 'pylint.config._pylint_config.setup', 'pylint.config._pylint_config.generate_command', 'pylint.checkers.classes.class_checker', 'pylint.checkers.classes.special_methods_checker', 'pylint.checkers.refactoring.implicit_booleaness_checker', 'pylint.checkers.refactoring.not_checker', 'pylint.checkers.refactoring.recommendation_checker', 'pylint.checkers.refactoring.refactoring_checker', 'pylint.checkers.base.basic_error_checker', 'pylint.checkers.base.comparison_checker', 'pylint.checkers.base.docstring_checker', 'pylint.checkers.base.function_checker', 'pylint.checkers.base.name_checker', 'pylint.checkers.base.name_checker.checker', 'pylint.checkers.base.pass_checker', 'pylint.checkers.base.name_checker.naming_style', 'match', 'mypyc.irbuild.classdef', 'mypyc.analysis.attrdefined', 'mypyc.irbuild.visitor', 'mypyc.irbuild.specialize', 'mypyc.irbuild.callable_class', 'mypyc.analysis.selfleaks', 'mypyc.primitives', 'mypyc.build', 'mypyc.test.test_serialization', 'mypyc.analysis', 'mypyc.transform', 'mypyc.codegen.cstring', 'mypyc.lower', 'pint', 'whitelist_utils', 'networkx.readwrite.adjlist', 'networkx.readwrite.edgelist', 'networkx.readwrite.gexf', 'networkx.readwrite.leda', 'networkx.readwrite.multiline_adjlist', 'networkx.readwrite.pajek', 'networkx.readwrite.sparse6', 'layout', 'nx_latex', 'nx_pylab', 'networkx.drawing.layout', 'networkx.linalg.algebraicconnectivity', 'networkx.linalg.attrmatrix', 'networkx.linalg.bethehessianmatrix', 'networkx.linalg.graphmatrix', 'networkx.linalg.laplacianmatrix', 'networkx.linalg.modularitymatrix', 'networkx.linalg.spectrum', 'digraph', 'graph', 'graphviews', 'multidigraph', 'multigraph', 'networkx.classes.digraph', 'networkx.classes.multigraph', 'networkx.classes.filters', 'networkx.algorithms.asteroidal', 'networkx.algorithms.boundary', 'networkx.algorithms.bridges', 'networkx.algorithms.broadcasting', 'networkx.algorithms.chains', 'networkx.algorithms.chordal', 'networkx.algorithms.clique', 'networkx.algorithms.cluster', 'networkx.algorithms.coloring', 'networkx.algorithms.core', 'networkx.algorithms.cuts', 'networkx.algorithms.cycles', 'networkx.algorithms.d_separation', 'networkx.algorithms.dag', 'networkx.algorithms.distance_regular', 'networkx.algorithms.dominance', 'networkx.algorithms.dominating', 'networkx.algorithms.efficiency_measures', 'networkx.algorithms.euler', 'networkx.algorithms.graph_hashing', 'networkx.algorithms.graphical', 'networkx.algorithms.hierarchy', 'networkx.algorithms.hybrid', 'networkx.algorithms.isolate', 'networkx.algorithms.link_analysis', 'networkx.algorithms.link_prediction', 'networkx.algorithms.lowest_common_ancestors', 'networkx.algorithms.mis', 'networkx.algorithms.non_randomness', 'networkx.algorithms.operators', 'networkx.algorithms.polynomials', 'networkx.algorithms.reciprocity', 'networkx.algorithms.richclub', 'networkx.algorithms.shortest_paths', 'networkx.algorithms.smallworld', 'networkx.algorithms.smetric', 'networkx.algorithms.sparsifiers', 'networkx.algorithms.structuralholes', 'networkx.algorithms.summarization', 'networkx.algorithms.swap', 'networkx.algorithms.time_dependent', 'networkx.algorithms.traversal', 'networkx.algorithms.tree.branchings', 'networkx.algorithms.tree.coding', 'networkx.algorithms.tree.operations', 'networkx.algorithms.tree.recognition', 'networkx.algorithms.triads', 'networkx.algorithms.vitality', 'networkx.algorithms.voronoi', 'networkx.algorithms.walks', 'networkx.algorithms.wiener', 'distance_measures', 'backends', 'networkx.utils.backends', 'networkx.utils.heaps', 'networkx.utils.random_sequence', 'networkx.utils.rcm', 'networkx.utils.union_find', 'networkx.generators.cographs', 'networkx.generators.community', 'networkx.generators.duplication', 'networkx.generators.ego', 'networkx.generators.geometric', 'networkx.generators.intersection', 'networkx.generators.lattice', 'networkx.generators.line', 'networkx.generators.mycielski', 'networkx.generators.nonisomorphic_trees', 'networkx.generators.random_clustered', 'networkx.generators.random_graphs', 'networkx.generators.small', 'networkx.generators.social', 'networkx.generators.stochastic', 'networkx.generators.sudoku', 'networkx.generators.time_series', 'networkx.generators.trees', 'networkx.generators.triads', 'scipy.special', 'scipy.sparse.linalg', 'utils.misc', 'classic', 'degree_seq', 'networkx.classes.tests.dispatch_interface', 'branchings', 'coding', 'decomposition', 'mst', 'operations', 'networkx.algorithms.isomorphism.ismags', 'networkx.algorithms.isomorphism.matchhelpers', 'networkx.algorithms.isomorphism.temporalisomorphvf2', 'networkx.algorithms.isomorphism.vf2userfunc', 'isomorphvf2', 'networkx.algorithms.operators.all', 'networkx.algorithms.operators.binary', 'networkx.algorithms.operators.unary', 'connectivity', 'cuts', 'disjoint_paths', 'edge_augmentation', 'edge_kcomponents', 'kcomponents', 'kcutsets', 'stoerwagner', 'networkx.algorithms.approximation.clique', 'networkx.algorithms.approximation.clustering_coefficient', 'networkx.algorithms.approximation.connectivity', 'networkx.algorithms.approximation.distance_measures', 'networkx.algorithms.approximation.dominating_set', 'networkx.algorithms.approximation.matching', 'networkx.algorithms.approximation.maxcut', 'networkx.algorithms.approximation.ramsey', 'networkx.algorithms.approximation.vertex_cover', 'matching', 'betweenness', 'betweenness_subset', 'closeness', 'current_flow_betweenness', 'current_flow_betweenness_subset', 'current_flow_closeness', 'degree_alg', 'dispersion', 'eigenvector', 'group', 'harmonic', 'katz', 'laplacian', 'load', 'percolation', 'reaching', 'second_order', 'subgraph_alg', 'trophic', 'voterank_alg', 'networkx.algorithms.threshold', 'networkx.algorithms.bipartite.basic', 'networkx.algorithms.bipartite.centrality', 'networkx.algorithms.bipartite.covering', 'networkx.algorithms.bipartite.edgelist', 'networkx.algorithms.bipartite.extendability', 'networkx.algorithms.bipartite.generators', 'networkx.algorithms.bipartite.projection', 'networkx.algorithms.bipartite.redundancy', 'networkx.algorithms.bipartite.spectral', 'attracting', 'biconnected', 'connected', 'semiconnected', 'strongly_connected', 'weakly_connected', 'beamsearch', 'breadth_first_search', 'depth_first_search', 'edgebfs', 'edgedfs', 'networkx.algorithms.minors.contraction', 'networkx.algorithms.shortest_paths.astar', 'networkx.algorithms.shortest_paths.dense', 'networkx.algorithms.shortest_paths.unweighted', 'networkx.algorithms.assortativity.connectivity', 'networkx.algorithms.assortativity.neighbor_degree', 'networkx.algorithms.community.asyn_fluid', 'networkx.algorithms.community.centrality', 'networkx.algorithms.community.divisive', 'networkx.algorithms.community.kclique', 'networkx.algorithms.community.kernighan_lin', 'networkx.algorithms.community.label_propagation', 'networkx.algorithms.community.louvain', 'networkx.algorithms.community.lukes', 'networkx.algorithms.community.modularity_max', 'networkx.algorithms.coloring.equitable_coloring', 'networkx.algorithms.coloring.greedy_coloring', 'capacityscaling', 'gomory_hu', 'maxflow', 'mincost', 'networksimplex', 'generators', 'networkx.algorithms.centrality.subgraph_alg', 'networkx.algorithms.connectivity.kcomponents', 'networkx.algorithms.connectivity.kcutsets', 'networkx.algorithms.connectivity.edge_augmentation', 'networkx.algorithms.connectivity.edge_kcomponents', 'networkx.classes.function', 'networkx.algorithms.tree', 'test_digraph', 'test_multidigraph', 'networkx.readwrite.json_graph.adjacency', 'networkx.readwrite.json_graph.cytoscape', 'networkx.readwrite.json_graph.node_link', 'networkx.readwrite.json_graph.tree', 'networkx.readwrite.p2g', 'astroid.nodes.as_string', 'astroid.nodes.const', 'astroid.nodes.node_ng', 'astroid.protocols', 'astroid.brain', 'astroid.filter_statements', 'astroid.nodes.scoped_nodes.mixin', 'astroid.nodes.scoped_nodes.scoped_nodes', 'astroid.interpreter.dunder_lookup', 'models.progress_tracker', 'simple_analyzer', 'utils.config_utils', 'vibe_check.core.utils', 'vibe_check.core.analysis.import_analyzer', 'vibe_check.core.analysis.import_visualizer', 'vibe_check.core.utils.async_utils', 'plugin_base', 'vibe_check.cli.watch_mode', 'vibe_check.cli.completion', 'vibe_check.cli.parallel_processing', 'core.dependency_manager', 'ui.gui', 'core.simple_analyzer', 'core.config', 'vibe_check.cli.output_formats', 'vibe_check.core.vcs.memory_manager', 'core.version', 'monitor', 'core.knowledge.framework_knowledge_base', 'vibe_check.core.logging.setup', 'vibe_check.cli.formatters', 'plugins.manager', 'core.logging', 'core.utils.dict_utils', 'core.vcs.engine', 'core.vcs.models', 'core.vcs.config', 'ui.tui', 'ui.web', 'weasyprint', 'github_actions', 'gitlab_ci', 'jenkins', 'azure_devops', 'static_assets', 'rest', 'graphql', 'websocket', 'monitoring', 'smtplib', 'email.mime.text', 'email.mime.multipart', 'distributed_processor', 'framework_detector', 'utils.file_utils', 'file_analyzer', 'metrics_aggregator', 'framework_rules', 'project_meritocracy_analyzer', 'semantic_output_formatter', 'performance_optimizer', 'astor', 'type_analyzer', 'utils.fs_utils', 'tool_executor', 'result_processor', 'tools.runners.tool_registry', 'meta_analyzer', 'error_handling.exceptions', 'config_utils', 'gitignore_utils', 'vibe_check.tools.runners.tool_registry', 'dict_utils', 'directory_metrics', 'file_metrics', 'models.project_metrics', 'trend_storage', 'ui.visualization.interactive_charts', 'framework_knowledge_base', 'analysis.python_semantic_analyzer', 'dependency_tracker', 'vibe_check.core.vcs.cache', 'metrics_collector', 'query_engine', 'time_series_storage', 'contextual_logger', 'plugin_loader', 'unified_reporter', 'style_rules', 'security_rules', 'complexity_rules', 'documentation_rules', 'import_rules', 'type_rules', 'advanced_python_rules', 'performance_rules', 'html_generators', 'core.analysis.standalone_analyzer', 'custom_rules.python_rules', 'core.fs_utils', 'core.analysis.import_analyzer', 'charts', 'exporters', 'interactive_charts', 'markdown', 'components', 'vibe_check.ui.web.components', 'vibe_check.ui.web.state_manager', 'simple_gui', 'themes', 'backports.datetime_fromisoformat', 'categories', 'properties', 'CoreData', 'JavaScriptCore', 'FSEvents', 'LaunchServices', 'rich.box', 'CoreAudio', 'Accounts', 'CoreLocation', 'MetalPerformanceShaders', 'typer._types', 'IOBluetooth', 'dispatch', 'aspectlib', 'timers', 'pygaljs', 'pygal.graph.box', 'pygal.style', 'fixture', '_pytest.config.findpaths', 'pytest_benchmark.csv', 'storage.file', 'storage.elasticsearch', 'cpuinfo', 'histogram', '__pypy__.time', 'pytest_benchmark.cli', 'UserNotifications', 'uc_micro.categories', 'uc_micro.properties', 'ucre', 'SceneKit', 'zipp.compat.overlay', 'DiscRecording', 'Foundation._context', 'Foundation._functiondefines', 'Foundation._nsindexset', 'Foundation._nsobject', 'Foundation._nsurl', 'LocalAuthentication', 'CoreText', 'HIServices', 'CoreML', '_cached', '_cachedmethod', 'textual._arrange', 'textual._compositor', 'textual._spatial_map', 'textual.renderables.background_screen', 'textual.widgets._toast', 'textual._wait', 'textual.drivers.headless_driver', 'textual._on', 'rich.color_triplet', 'textual._color_constants', 'textual.fuzzy', 'textual.types', 'textual.widgets.option_list', 'textual._compose', 'textual._dispatch_key', 'textual._extrema', 'textual._styles_cache', 'textual.box_model', 'textual.layouts.vertical', 'textual.rlock', 'textual.scrollbar', 'textual.design', 'textual._opacity', 'textual.renderables.text_opacity', 'textual.renderables.tint', 'textual.canvas', 'textual.layouts.grid', 'textual._log', 'textual._work_decorator', 'textual.case', 'textual._import_app', 'textual.pilot', 'textual._widget_navigation', 'textual.widgets._placeholder', 'textual._markup_playground', 'textual._partition', 'rich.control', 'textual.map_geometry', 'rich._wrap', 'textual.css.scalar_animation', 'textual._ansi_sequences', 'textual._keyboard_protocol', 'textual_speedups', 'textual._event_broker', 'textual._files', 'textual.css.stylesheet', 'textual.features', 'textual.file_monitor', 'textual.theme', 'textual.worker_manager', 'textual_dev.client', 'textual.system_commands', 'textual_dev.redirect_output', 'textual.drivers.windows_driver', 'textual.drivers.linux_driver', 'textual.drivers.linux_inline_driver', 'textual.render', 'textual._win_sleep', 'textual.demo.demo_app', 'textual._box_drawing', 'textual._node_list', 'textual.walk', '_plotly_utils.files', '_subplots', 'plotly.files', 'optional_imports', 'plotly.version', 'express', '_plotly_utils.exceptions', 'validators', 'plotly._subplots', 'plotly.io.kaleido', 'validator_cache', '_plotly_utils.data_utils', 'graph_objects', 'plotly.io._renderers', 'callbacks', 'serializers', 'Intents', 'SpriteKit', 'asyncio.base_subprocess', 'png', 'Contacts', 'objc._pythonify', 'ExceptionHandling', 'PyObjCTools', 'numpy.core.multiarray', 'FileProvider', 'pyspark.sql.connect.dataframe', 'pyspark', 'narwhals._enum', 'embed', 'SharedWithYouCore', 'compression', 'PyObjCTools.KeyValueCoding', 'objc._new', 'objc._transform', '_transform', '_convenience', '_convenience_nsobject', '_convenience_nsdecimal', '_convenience_nsdata', '_convenience_nsdictionary', '_convenience_nsset', '_convenience_nsarray', '_convenience_nsstring', '_convenience_mapping', '_convenience_sequence', '_dyld', '_protocols', '_descriptors', '_category', '_bridges', '_pythonify', '_locking', '_context', '_properties', '_lazyimport', '_bridgesupport', '_informal_protocol', 'objc._convenience_mapping', 'objc._framework', 'cpuinfo.cpuinfo', '_generic', '_nixcommon', 'items', 'PrintCore', 'CFOpenDirectory', 'authlib.oauth2.rfc6749.util', 'userinfo', 'authlib.oauth2.rfc6749.authorization_server', 'authlib.oauth2.rfc6749.resource_protector', 'authlib.oauth2.rfc6749.hooks', 'rfc6749.hooks', 'authlib.oidc.discovery.models', 'registration', 'rfc6749.authenticate_client', 'rfc6749.requests', 'authlib.oauth2.rfc6749.grants', 'dask.dataframe.api', 'pyarrow.__lib_pxi.table', 'ibis.expr.operations', 'narwhals._ibis.group_by', 'narwhals._ibis.selectors', 'narwhals._ibis.expr_dt', 'narwhals._ibis.expr_list', 'narwhals._ibis.expr_str', 'narwhals._ibis.expr_struct', 'cupy', 'pyspark.sql.connect', 'pyspark.sql.connect.window', 'test_parallel', '_posixsubprocess', 'pip._vendor.tomli_w._writer', 'resolvers.criterion', 'abstract', 'resolution', 'pip._internal.req.req_dependency_group', 'pip._vendor.dependency_groups', 'pip._internal.models.pylock', 'plotly.express._core', 'cmocean', 'plotlyjs', '_plotly_utils', 'meta', 'safety.tool.decorators', 'safety.tool.auth', 'plotly.basewidget', '_deprecations', '_figure', '_frame', 'graph_objs._figurewidget', 'missing_anywidget', 'plotly.validators.layout', '_doc', '_imshow', '_chart_types', '_special_inputs', 'trendline_functions', 'imshow_utils', '_orca', 'google.colab', 'plotly.io._base_renderers', 'plotly.io.orca', '_kaleido', '_templates', '_html', '_renderers', 'kaleido', 'plotly.io._defaults', 'kaleido.scopes.plotly', 'kaleido.errors', 'choreographer.cli.defaults', '_base_renderers', 'plotly.io._orca', 'plotly.io.json', 'plotly.optional_imports', 'plotly.graph_objs.layout', 'plotly.offline', 'graph_objs', '_plotly_utils.colors', 'plotly.matplotlylib.mplexporter', 'plotly.matplotlylib', 'plotly.matplotlylib.renderer', '_frames', 'plotly.validators.heatmap', 'plotly.exceptions', 'plotly.figure_factory._2d_density', 'plotly.figure_factory._annotated_heatmap', 'plotly.figure_factory._bullet', 'plotly.figure_factory._candlestick', 'plotly.figure_factory._dendrogram', 'plotly.figure_factory._distplot', 'plotly.figure_factory._facet_grid', 'plotly.figure_factory._gantt', 'plotly.figure_factory._ohlc', 'plotly.figure_factory._quiver', 'plotly.figure_factory._scatterplot', 'plotly.figure_factory._streamline', 'plotly.figure_factory._table', 'plotly.figure_factory._trisurf', 'plotly.figure_factory._violin', 'plotly.figure_factory._county_choropleth', 'plotly.figure_factory._hexbin_mapbox', 'plotly.figure_factory._ternary_contour', 'plotly.express._doc', 'plotly.express._chart_types', 'skimage', 'offline', '_plotlyjs_version', '_sum', '_csrc', '_c', '_yaxes', '_xaxes', '_showupperhalf', '_showlowerhalf', '_diagonal', '_sortpaths', '_countssrc', '_counts', '_bundlecolors', '_arrangement', '_wsrc', '_whoverformat', '_w', '_vsrc', '_vhoverformat', '_v', '_usrc', '_uhoverformat', '_u', '_starts', '_spanmode', '_span', '_scalemode', '_quartilemethod', '_points', '_pointpos', '_meanline', '_jitter', '_bandwidth', '_valuesuffix', '_node', '_link', '_whiskerwidth', '_upperfencesrc', '_upperfence', '_showwhiskers', '_sdsrc', '_sdmultiple', '_sd', '_q3src', '_q3', '_q1src', '_q1', '_notchwidth', '_notchspansrc', '_notchspan', '_notched', '_mediansrc', '_median', '_meansrc', '_mean', '_lowerfencesrc', '_lowerfence', '_boxpoints', '_boxmean', '_ybingroup', '_xbingroup', '_number', '_gauge', '_delta', '_totals', '_measuresrc', '_measure', '_waterfallmode', '_waterfallgroupgap', '_waterfallgap', '_violinmode', '_violingroupgap', '_violingap', '_updatemenudefaults', '_updatemenus', '_uniformtext', '_treemapcolorway', '_ternary', '_template', '_sunburstcolorway', '_spikedistance', '_smith', '_sliderdefaults', '_sliders', '_shapedefaults', '_shapes', '_separators', '_selectiondefaults', '_selections', '_selectionrevision', '_selectdirection', '_scattermode', '_scattergap', '_polar', '_plot_bgcolor', '_piecolorway', '_paper_bgcolor', '_newshape', '_newselection', '_modebar', '_minreducedwidth', '_minreducedheight', '_margin', '_mapbox', '_map', '_imagedefaults', '_images', '_iciclecolorway', '_hoversubplots', '_hovermode', '_hoverdistance', '_hidesources', '_hiddenlabelssrc', '_hiddenlabels', '_grid', '_funnelmode', '_funnelgroupgap', '_funnelgap', '_funnelareacolorway', '_extendtreemapcolors', '_extendsunburstcolors', '_extendpiecolors', '_extendiciclecolors', '_extendfunnelareacolors', '_editrevision', '_dragmode', '_datarevision', '_computed', '_colorway', '_clickmode', '_boxmode', '_boxgroupgap', '_boxgap', '_barnorm', '_barmode', '_bargroupgap', '_bargap', '_barcornerradius', '_autosize', '_annotationdefaults', '_annotations', '_activeshape', '_activeselection', '_opensrc', '_open', '_lowsrc', '_low', '_highsrc', '_high', '_closesrc', '_close', '_vertexcolorsrc', '_vertexcolor', '_ksrc', '_k', '_jsrc', '_j', '_isrc', '_intensitysrc', '_intensitymode', '_intensity', '_i', '_facecolorsrc', '_facecolor', '_delaunayaxis', '_alphahull', '_rangefont', '_labelside', '_labelangle', '_ytype', '_xtype', '_hoverongaps', '_radiussrc', '_basesrc', '_surfacecolorsrc', '_surfacecolor', '_opacityscale', '_hidesurface', '_traces', '_group', '_baseframe', '_db', '_da', '_cheaterslope', '_b0', '_a0', '_label0', '_dlabel', '_baseratio', '_btype', '_atype', '_colormodel', '_pullsrc', '_pull', '_insidetextorientation', '_hole', '_locationmode', '_stackgroup', '_stackgaps', '_groupnorm', '_fillpattern', '_fillgradient', '_header', '_columnwidthsrc', '_columnwidth', '_columnordersrc', '_columnorder', '_cells', '_surfaceaxis', '_error_z', '_valuehoverformat', '_isomin', '_isomax', '_cumulative', '_realsrc', '_real', '_imagsrc', '_imag', '_currentbin', '_edgeshape', '_squarifyratio', '_packing', '_flip', '_depthfade', '_split', '_copy_zstyle', '_suffixsrc', '_prefixsrc', '_formatsrc', '_format', '_simplify', '_stop', '_startlinewidth', '_startlinecolor', '_startline', '_minorgridwidth', '_minorgriddash', '_minorgridcount', '_minorgridcolor', '_labelsuffix', '_labelprefix', '_labelpadding', '_endlinewidth', '_endlinecolor', '_endline', '_cheatertype', '_arraytick0', '_arraydtick', '_stepsrc', '_allowoverlap', '_multiselect', '_constraintrange', '_caxis', '_traceorder', '_tracegroupgap', '_itemwidth', '_itemsizing', '_itemdoubleclick', '_itemclick', '_indentation', '_groupclick', '_entrywidthmode', '_entrywidth', '_ordering', '_easing', '_duration', '_sector', '_radialaxis', '_gridshape', '_angularaxis', '_ysizemode', '_y1shift', '_y1', '_y0shift', '_xsizemode', '_x1shift', '_x1', '_x0shift', '_fillrule', '_editable', '_removesrc', '_remove', '_addsrc', '_add', '_activecolor', '_subtitle', '_realaxis', '_imaginaryaxis', '_subunitwidth', '_subunitcolor', '_showsubunits', '_showrivers', '_showocean', '_showland', '_showlakes', '_showframe', '_showcountries', '_showcoastlines', '_scope', '_riverwidth', '_rivercolor', '_resolution', '_oceancolor', '_lonaxis', '_lataxis', '_landcolor', '_lakecolor', '_framewidth', '_framecolor', '_fitbounds', '_countrywidth', '_countrycolor', '_coastlinewidth', '_coastlinecolor', '_stepdefaults', '_steps', '_minorticklen', '_currentvalue', '_activebgcolor', '_active', '_yshift', '_yclick', '_xshift', '_xclick', '_startstandoff', '_startarrowsize', '_startarrowhead', '_showarrow', '_clicktoshow', '_captureevents', '_borderpad', '_ayref', '_ay', '_axref', '_ax', '_arrowwidth', '_arrowsize', '_arrowside', '_arrowhead', '_arrowcolor', '_sizing', '_sizey', '_sizex', '_minsize', '_zoom', '_pitch', '_layerdefaults', '_layers', '_bearing', '_tickson', '_ticklabelstandoff', '_ticklabelshift', '_ticklabelmode', '_ticklabelindexsrc', '_ticklabelindex', '_spikesnap', '_spikemode', '_spikedash', '_showdividers', '_shift', '_scaleratio', '_scaleanchor', '_rangebreakdefaults', '_rangebreaks', '_overlaying', '_insiderange', '_dividerwidth', '_dividercolor', '_constraintoward', '_constrain', '_autoshift', '_accesstoken', '_rangeslider', '_rangeselector', '_sequentialminus', '_sequential', '_diverging', '_autoexpand', '_zaxis', '_camera', '_aspectmode', '_showactive', '_buttondefaults', '_buttons', '_drawdirection', '_yside', '_xside', '_rows', '_roworder', '_columns', '_padding', '_method', '_execute', '_args2', '_args', '_up', '_eye', '_dvalue', '_stepmode', '_west', '_south', '_north', '_east', '_sourcetype', '_sourcelayer', '_sourceattribution', '_minzoom', '_coordinates', '_placement', '_iconsize', '_icon', '_dashsrc', '_tilt', '_parallels', '_distance', '_roll', '_period', '_relative', '_reference', '_threshold', '_outlierwidth', '_targetsrc', '_target', '_sourcesrc', '_labelsrc', '_hovercolorsrc', '_hovercolor', '_colorscaledefaults', '_colorscales', '_arrowlen', '_groups', '_displayindex', 'IPython.core.display', 'pandas.tseries.converter', 'vincent', 'vega_renderer', 'vincent_renderer', 'fake_renderer', 'statsmodels.api', 'plotly.data', '_annotation', '_selection', '_slider', '_updatemenu', '_rangebreak', '_button', 'textual.demo.data', 'textual.suggester', 'textual.demo.game', 'textual.demo.home', 'textual.demo.projects', 'textual.demo.widgets', 'textual.renderables._blend_colors', 'textual.drivers._input_reader_windows', 'textual.drivers._input_reader_linux', 'textual.drivers', 'textual.drivers._writer_thread', 'textual._binary_encode', 'textual.drivers._byte_stream', 'textual.drivers._input_reader', 'rich.errors', 'textual.css._help_text', 'textual.layouts.factory', 'textual._duration', 'textual.css._style_properties', 'textual.css._styles_builder', 'textual._profile', 'textual._wrap', 'textual.layouts.horizontal', 'textual.widgets._content_switcher', 'textual.renderables.digits', 'textual.widgets._collapsible', 'textual.widgets._checkbox', 'textual.widgets._data_table', 'textual.widgets._digits', 'textual.widgets._footer', 'textual.widgets._header', 'textual.widgets._help_panel', 'textual.widgets._key_panel', 'textual.widgets._label', 'textual.widgets._link', 'textual.widgets._list_item', 'textual.widgets._list_view', 'textual.widgets._loading_indicator', 'textual.widgets._log', 'textual.widgets._masked_input', 'textual.widgets._pretty', 'textual.widgets._progress_bar', 'textual.widgets._radio_button', 'textual.widgets._radio_set', 'textual.widgets._rich_log', 'textual.widgets._rule', 'textual.widgets._selection_list', 'textual.widgets._sparkline', 'textual.widgets._switch', 'textual.widgets._text_area', 'textual.widgets._tooltip', 'textual.widgets._welcome', 'textual.clock', 'textual.eta', 'textual.renderables.bar', 'textual._text_area_theme', 'textual._tree_sitter', 'textual.document._document_navigator', 'textual.document._history', 'textual.document._syntax_aware_document', 'textual.validation', 'textual._slug', 'textual._two_way_dict', 'textual.coordinate', 'textual.renderables.styled', 'textual.renderables.sparkline', 'textual._line_split', 'textual._immutable_sequence_view', 'markdown_it.helpers', 'elasticsearch', 'elasticsearch.serializer', 'streamlit.web', 'streamlit.runtime.context_util', 'streamlit.proto.GapSize_pb2', 'streamlit.proto.HeightConfig_pb2', 'CoreServices.SearchKit', 'Any', 'Cc', 'Cf', 'P', 'Z', 'vibe_check.core.vcs.plugins', 'vibe_check.core.vcs.plugins.plugin_interface', 'orchestrator', 'components.sidebar', 'pages.dashboard', 'pages.issues', 'pages.files', 'pages.meta_systems', 'analyzer', 'visualizer', 'file_manager', 'PAT_tool.hypothesis_stage', 'gudhi', 'mpld3', 'pyvis.network', 'PAT_project_analysis.PAT_tool.bandit_stage', 'PAT_project_analysis.PAT_tool.black_stage', 'PAT_project_analysis.PAT_tool.call_graph_stage', 'PAT_project_analysis.PAT_tool.call_graph_visualizer', 'PAT_project_analysis.PAT_tool.chunked_prompt_generator', 'PAT_project_analysis.PAT_tool.complexity_analyzer', 'PAT_project_analysis.PAT_tool.content_extractor', 'PAT_project_analysis.PAT_tool.coverage_stage', 'PAT_project_analysis.PAT_tool.dependency_analyzer', 'PAT_project_analysis.PAT_tool.dependency_stage', 'PAT_project_analysis.PAT_tool.effect_overlay_stage', 'PAT_project_analysis.PAT_tool.flake8_stage', 'PAT_project_analysis.PAT_tool.graph_overlay_stage', 'PAT_project_analysis.PAT_tool.hypothesis_stage', 'PAT_project_analysis.PAT_tool.isort_stage', 'PAT_project_analysis.PAT_tool.meta_system_stage', 'PAT_project_analysis.PAT_tool.meta_system_visualizer', 'PAT_project_analysis.PAT_tool.models', 'PAT_project_analysis.PAT_tool.mypy_stage', 'PAT_project_analysis.PAT_tool.pipeline', 'PAT_project_analysis.PAT_tool.pre_analysis', 'PAT_project_analysis.PAT_tool.protocol_extraction_stage', 'PAT_project_analysis.PAT_tool.pycontract_stage', 'PAT_project_analysis.PAT_tool.pydocstyle_stage', 'PAT_project_analysis.PAT_tool.pylint_stage', 'PAT_project_analysis.PAT_tool.pyre_stage', 'PAT_project_analysis.PAT_tool.pyright_stage', 'PAT_project_analysis.PAT_tool.reporter', 'PAT_project_analysis.PAT_tool.ruff_stage', 'PAT_project_analysis.PAT_tool.structure_analyzer', 'PAT_project_analysis.PAT_tool.tda_overlay_stage', 'PAT_project_analysis.PAT_tool.tla_stage', 'PAT_project_analysis.PAT_tool.utils', 'PAT_project_analysis.PAT_tool.visualization', 'bandit_stage', 'black_stage', 'call_graph_stage', 'call_graph_visualizer', 'complexity_analyzer', 'content_extractor', 'coverage_stage', 'dependency_analyzer', 'dependency_stage', 'effect_overlay_stage', 'flake8_stage', 'graph_overlay_stage', 'isort_stage', 'meta_system_stage', 'mypy_stage', 'pre_analysis', 'protocol_extraction_stage', 'pycontract_stage', 'pydocstyle_stage', 'pylint_stage', 'pyre_stage', 'pyright_stage', 'ruff_stage', 'structure_analyzer', 'tla_stage', 'PAT_project_analysis.PAT_tool.dependency_audit', 'dependency_audit', 'PAT_tool.tda_overlay_stage', 'scripts.project_analyse_tool.main', 'vibe_check.core.actor_system.components', 'vibe_check.core.analyzer.project_analyzer', 'vibe_check.core.models.progress_tracker', 'pytest_asyncio', 'vibe_check.core.actor_system.actor_initializer', 'vibe_check.core.actor_system.diagnostics', 'vibe_check.core.actor_system.logging', 'vibe_check.core.actor_system.enhanced_actor', 'vibe_check.core.actor_system.actor_state', 'vibe_check.core.actor_system.integration', 'vibe_check.core.utils.file_utils', 'vibe_check.core.config.config_manager', 'vibe_check.core.actor_system.messaging.processor', 'vibe_check.core.actor_system.initialization.dependency_resolver', 'vibe_check.core.actor_system.initialization.synchronization', 'manager', 'manager_provider', 'vibe_check.core.actor_system.actors', 'generator', 'content_collector', 'chunk_builder', 'manifest', 'verification', 'schema_validator', 'pat_plugins.analyzers.caw_analyzer', 'pat_plugins.analyzers.folded_mind_analyzer', 'pat_plugins.analyzers.memory_analyzer', 'interrogate_plugin', 'pat_plugins.core.shared_context', 'plugin_manager', 'bandit_plugin', 'pyright_plugin', 'parsers', 'metrics', 'sidebar', 'forms', 'visualizations', 'pat_backend', 'home', 'analysis', 'PAT_project_analysis', 'vibe_check.compat', 'vibe_check.ai.refactoring.refactoring_engine', 'vibe_check.ai.infrastructure.model_manager', 'vibe_check.ai.visualization', 'vibe_check.ai.visualization.dashboard_engine', 'vibe_check.ai.temporal.temporal_engine', 'vibe_check.ai.explanation.explanation_engine', 'vibe_check.core.analysis.file_analyzer', 'vibe_check.core.analysis.project_analyzer', 'vibe_check.core.analysis.metrics_aggregator', 'PAT_project_analysis.cli', 'vibe_check.ui.visualization', 'vibe_check.tools.parsers.base_parser', 'vibe_check.tools.parsers.parser_registry', 'vibe_check.tools.parsers.ruff_parser', 'vibe_check.tools.parsers.mypy_parser', 'vibe_check.tools.parsers.bandit_parser', 'vibe_check.tools.parsers.complexity_parser', 'vibe_check.tools.runners.custom_rules_runner', 'vibe_check.tools.parsers.custom_rules_parser', 'vibe_check.tools.custom_rules.python_rules', 'vibe_check.tools.runners.pylint_runner', 'vibe_check.tools.runners.pyflakes_runner', 'vibe_check.tools.parsers.pylint_parser', 'vibe_check.tools.parsers.pyflakes_parser', 'vibe_check.core.progress', 'vibe_check.core.fs_utils', 'vibe_check.plugins.manager', 'vibe_check.plugins.base_plugin', 'vibe_check.core.analysis.dependency_analyzer', 'vibe_check.core.analysis.python_semantic_analyzer', 'vibe_check.core.analysis.semantic_rules', 'vibe_check.core.analysis.type_analyzer', 'vibe_check.core.analysis.standalone_analyzer', 'vibe_check.core.analysis.tool_executor', 'vibe_check.core.utils.dict_utils', 'vibe_check.core.trend_analysis.trend_visualizer', 'vibe_check.core.trend_analysis.trend_analyzer', 'vibe_check.core.vcs.rules.security_rules', 'vibe_check.core.vcs.rules.style_rules', 'vibe_check.tools.runners.ruff_runner', 'vibe_check.tools.runners.bandit_runner', 'vibe_check.ui.visualization.interactive_charts']", "Consider consolidating similar dependencies: [['unittest.mock', 'unittest.case'], ['vibe_check.core.vcs.engine', 'vibe_check.core.vcs.models', 'vibe_check.core.vcs.config', 'vibe_check.core.vcs.rules.rule_loader', 'vibe_check.core.vcs.registry', 'vibe_check.core.models', 'vibe_check.core.simple_analyzer', 'vibe_check.core.analysis', 'vibe_check.tools.runners', 'vibe_check.tools.parsers', 'vibe_check.cli.main', 'vibe_check.core.actor_system.logging.enhanced_logger', 'vibe_check.core.actor_system.actor_system', 'vibe_check.core.utils', 'vibe_check.core.analysis.import_analyzer', 'vibe_check.core.analysis.import_visualizer', 'vibe_check.core.config', 'vibe_check.core.utils.async_utils', 'vibe_check.core.logging', 'vibe_check.cli.watch_mode', 'vibe_check.cli.completion', 'vibe_check.cli.parallel_processing', 'vibe_check.cli.output_formats', 'vibe_check.core.vcs.memory_manager', 'vibe_check.core.logging.setup', 'vibe_check.cli.formatters', 'vibe_check.ai.infrastructure', 'vibe_check.core.vcs.integration.meta_analyzer', 'vibe_check.tools.runners.tool_registry', 'vibe_check.core.vcs.cache', 'vibe_check.core', 'vibe_check.ui.web.components', 'vibe_check.ui.web.state_manager', 'vibe_check.core.vcs.plugins', 'vibe_check.core.vcs.plugins.plugin_interface', 'vibe_check.core.actor_system.actor', 'vibe_check.core.actor_system.message', 'vibe_check.core.actor_system.components', 'vibe_check.core.actor_system.context_wave', 'vibe_check.core.analyzer.project_analyzer', 'vibe_check.core.models.progress_tracker', 'vibe_check.core.models.project_metrics', 'vibe_check.core.actor_system', 'vibe_check.core.actor_system.actor_initializer', 'vibe_check.core.actor_system.diagnostics', 'vibe_check.core.actor_system.logging', 'vibe_check.core.actor_system.enhanced_actor', 'vibe_check.core.actor_system.dependency_injection', 'vibe_check.core.actor_system.diagnostics.enhanced_tracker', 'vibe_check.core.actor_system.consolidated_initializer', 'vibe_check.core.actor_system.actor_state', 'vibe_check.core.actor_system.integration', 'vibe_check.cli.commands', 'vibe_check.cli.error_handler', 'vibe_check.core.utils.file_utils', 'vibe_check.core.config.config_manager', 'vibe_check.core.actor_system.messaging.processor', 'vibe_check.core.actor_system.initialization.dependency_resolver', 'vibe_check.core.actor_system.initialization.synchronization', 'vibe_check.core.models.file_metrics', 'vibe_check.core.models.directory_metrics', 'vibe_check.core.actor_system.actors', 'vibe_check.compat', 'vibe_check.ai.refactoring', 'vibe_check.ai.refactoring.refactoring_engine', 'vibe_check.ai.infrastructure.model_manager', 'vibe_check.ai.explanation', 'vibe_check.ai.temporal', 'vibe_check.ai.visualization', 'vibe_check.ai.visualization.dashboard_engine', 'vibe_check.ai.temporal.temporal_engine', 'vibe_check.ai.explanation.explanation_engine', 'vibe_check.core.analysis.file_analyzer', 'vibe_check.core.analysis.project_analyzer', 'vibe_check.core.analysis.metrics_aggregator', 'vibe_check.ui.visualization', 'vibe_check.tools.parsers.base_parser', 'vibe_check.tools.parsers.parser_registry', 'vibe_check.tools.parsers.ruff_parser', 'vibe_check.tools.parsers.mypy_parser', 'vibe_check.tools.parsers.bandit_parser', 'vibe_check.tools.parsers.complexity_parser', 'vibe_check.tools.runners.custom_rules_runner', 'vibe_check.tools.parsers.custom_rules_parser', 'vibe_check.tools.custom_rules.python_rules', 'vibe_check.tools.runners.pylint_runner', 'vibe_check.tools.runners.pyflakes_runner', 'vibe_check.tools.parsers.pylint_parser', 'vibe_check.tools.parsers.pyflakes_parser', 'vibe_check.core.progress', 'vibe_check.core.fs_utils', 'vibe_check.plugins.manager', 'vibe_check.plugins.base_plugin', 'vibe_check.core.analysis.dependency_analyzer', 'vibe_check.core.analysis.python_semantic_analyzer', 'vibe_check.core.analysis.semantic_rules', 'vibe_check.core.analysis.type_analyzer', 'vibe_check.core.analysis.standalone_analyzer', 'vibe_check.core.analysis.tool_executor', 'vibe_check.core.utils.dict_utils', 'vibe_check.core.trend_analysis.trend_visualizer', 'vibe_check.core.trend_analysis.trend_analyzer', 'vibe_check.core.trend_analysis.trend_storage', 'vibe_check.core.vcs.rules.security_rules', 'vibe_check.core.vcs.rules.style_rules', 'vibe_check.tools.runners.ruff_runner', 'vibe_check.tools.runners.bandit_runner', 'vibe_check.ui.visualization.interactive_charts'], ['PAT_tool.main', 'PAT_tool.meta_system_analyzer', 'PAT_tool.meta_system_visualizer', 'PAT_tool.models', 'PAT_tool.utils', 'PAT_tool.pipeline', 'PAT_tool.hypothesis_stage', 'PAT_tool.tool_stage', 'PAT_tool.visualization', 'PAT_tool.tda_overlay_stage'], ['pat_plugins.analyzers', 'pat_plugins.core', 'pat_plugins.performance', 'pat_plugins.analyzers.codebase_graph_analyzer', 'pat_plugins.analyzers.effect_system_analyzer', 'pat_plugins.analyzers.caw_analyzer', 'pat_plugins.analyzers.folded_mind_analyzer', 'pat_plugins.analyzers.memory_analyzer', 'pat_plugins.core.shared_context'], ['matplotlib.pyplot', 'matplotlib.patches', 'matplotlib.pylab', 'matplotlib.font_manager', 'matplotlib.transforms', 'matplotlib.path', 'matplotlib._path', 'matplotlib.collections', 'matplotlib.lines', 'matplotlib.artist', 'matplotlib.colors', 'matplotlib.scale', 'matplotlib.text', 'matplotlib.ticker', 'matplotlib.units', 'matplotlib.ft2font', 'matplotlib.mathtext', 'matplotlib.texmanager', 'matplotlib._enums', 'matplotlib._pylab_helpers', 'matplotlib.backend_managers', 'matplotlib.cbook', 'matplotlib.layout_engine', 'matplotlib.figure', 'matplotlib.dates', 'matplotlib.mlab', 'matplotlib.spines', 'matplotlib.backends', 'matplotlib._fontconfig_pattern', 'matplotlib.rcsetup', 'matplotlib._cm', 'matplotlib._cm_bivar', 'matplotlib._cm_listed', 'matplotlib._cm_multivar', 'matplotlib.colorizer', 'matplotlib.container', 'matplotlib.offsetbox', 'matplotlib.axes', 'matplotlib._api', 'matplotlib.colorbar', 'matplotlib.image', 'matplotlib.legend', 'matplotlib.backend_bases', 'matplotlib.gridspec', 'matplotlib.cm', 'matplotlib.patheffects', 'matplotlib._animation_data', 'matplotlib.bezier', 'matplotlib.projections', 'matplotlib._constrained_layout', 'matplotlib._tight_layout', 'matplotlib.axes._base', 'matplotlib.axis', 'matplotlib.contour', 'matplotlib.quiver', 'matplotlib.typing', 'matplotlib.widgets', 'matplotlib.hatch', 'matplotlib._image', 'matplotlib._layoutgrid', 'matplotlib.table', 'matplotlib.tri._triangulation', 'matplotlib.tri', 'matplotlib.tri._trifinder', 'matplotlib.tri._triinterpolate', 'matplotlib.tri._tritools', 'matplotlib.category', 'matplotlib.inset', 'matplotlib.markers', 'matplotlib.stackplot', 'matplotlib.streamplot', 'matplotlib.axes._secondary_axes', 'matplotlib.backend_tools', 'matplotlib.backends.qt_editor.figureoptions', 'matplotlib._afm', 'matplotlib.backends.backend_mixed', 'matplotlib.backends.backend_pdf', 'matplotlib._tight_bbox', 'matplotlib._mathtext_data', 'matplotlib.backends._backend_agg', 'matplotlib.testing.decorators', 'matplotlib.testing.compare', 'matplotlib.testing', 'matplotlib.testing._markers', 'matplotlib.backends.backend_tkagg', 'matplotlib.backends.backend_webagg_core', 'matplotlib.backends._backend_pdf_ps', 'matplotlib.backends.backend_agg', 'matplotlib.backends.backend_pgf', 'matplotlib.testing.exceptions', 'matplotlib.testing.conftest', 'matplotlib._type1font', 'matplotlib.animation', 'matplotlib.testing.jpl_units', 'matplotlib.backends.backend_qt5agg', 'matplotlib.backends.backend_qt5cairo', 'matplotlib.backends.backend_qt5', 'matplotlib.backends.backend_qt', 'matplotlib.textpath', 'matplotlib.sankey', 'matplotlib.dviread', 'matplotlib.testing.widgets', 'matplotlib.style.core', 'matplotlib.backends.backend_template', 'matplotlib.backends.qt_compat', 'matplotlib.backends.qt_editor', 'matplotlib.backends.backend_qtagg', 'matplotlib.projections.geo', 'matplotlib.projections.polar', 'matplotlib.legend_handler', 'matplotlib.style'], ['pat_core.report_generator', 'pat_core.plugin'], ['textual.app', 'textual.containers', 'textual.reactive', 'textual.screen', 'textual.scroll_view', 'textual.widgets', 'textual._arrange', 'textual._callback', 'textual._compositor', 'textual._context', 'textual._path', 'textual._spatial_map', 'textual._types', 'textual.actions', 'textual.await_complete', 'textual.binding', 'textual.css.match', 'textual.css.parse', 'textual.css.query', 'textual.dom', 'textual.errors', 'textual.geometry', 'textual.keys', 'textual.layout', 'textual.renderables.background_screen', 'textual.renderables.blank', 'textual.selection', 'textual.signal', 'textual.timer', 'textual.widget', 'textual.widgets._toast', 'textual.command', 'textual.message_pump', 'textual.message', 'textual._wait', 'textual.drivers.headless_driver', 'textual.events', 'textual.color', 'textual.css.types', 'textual.style', 'textual.content', 'textual.cache', 'textual.visual', 'textual._compat', 'textual._on', 'textual._time', 'textual.constants', 'textual.css.model', 'textual._color_constants', 'textual.css.scalar', 'textual.css.tokenize', 'textual.suggestions', 'textual.fuzzy', 'textual.types', 'textual.widgets.option_list', 'textual.worker', 'textual.css.styles', 'textual._animator', 'textual._compose', 'textual._debug', 'textual._dispatch_key', 'textual._easing', 'textual._extrema', 'textual._styles_cache', 'textual.await_remove', 'textual.box_model', 'textual.layouts.vertical', 'textual.messages', 'textual.notifications', 'textual.rlock', 'textual.strip', 'textual.scrollbar', 'textual.css.tokenizer', 'textual.design', 'textual.markup', 'textual._ansi_theme', 'textual._border', 'textual._opacity', 'textual._segment_tools', 'textual.filter', 'textual.renderables.text_opacity', 'textual.renderables.tint', 'textual._cells', 'textual._loop', 'textual.expand_tabs', 'textual.canvas', 'textual.layouts.grid', 'textual._log', 'textual._work_decorator', 'textual.case', 'textual._import_app', 'textual.pilot', 'textual._widget_navigation', 'textual.widgets._directory_tree', 'textual.widgets._input', 'textual.widgets._option_list', 'textual.widgets._placeholder', 'textual.widgets._select', 'textual._markup_playground', 'textual._partition', 'textual.map_geometry', 'textual.css.scalar_animation', 'textual._ansi_sequences', 'textual._keyboard_protocol', 'textual._parser', 'textual._event_broker', 'textual._files', 'textual.css.errors', 'textual.css.stylesheet', 'textual.driver', 'textual.features', 'textual.file_monitor', 'textual.theme', 'textual.worker_manager', 'textual.system_commands', 'textual.drivers.windows_driver', 'textual.drivers.linux_driver', 'textual.drivers.linux_inline_driver', 'textual.render', 'textual._win_sleep', 'textual.demo.demo_app', 'textual._box_drawing', 'textual._node_list', 'textual.css._error_tools', 'textual.css.constants', 'textual.walk', 'textual.demo.page', 'textual.demo.data', 'textual.suggester', 'textual.demo.game', 'textual.demo.home', 'textual.demo.projects', 'textual.demo.widgets', 'textual.renderables._blend_colors', 'textual.drivers._input_reader_windows', 'textual.drivers._input_reader_linux', 'textual.drivers', 'textual.drivers._writer_thread', 'textual._xterm_parser', 'textual._binary_encode', 'textual.drivers._byte_stream', 'textual.drivers._input_reader', 'textual.css._help_text', 'textual.css.transition', 'textual.layouts.factory', 'textual.css._help_renderables', 'textual._duration', 'textual.css._style_properties', 'textual.css._styles_builder', 'textual._profile', 'textual.document._document', 'textual.document._wrapped_document', 'textual._wrap', 'textual.document._edit', 'textual._resolve', 'textual.layouts.horizontal', 'textual.widgets._tree', 'textual.widgets._tabbed_content', 'textual.widgets._tabs', 'textual.widgets._content_switcher', 'textual.renderables.digits', 'textual.widgets._collapsible', 'textual.widgets._button', 'textual.widgets._checkbox', 'textual.widgets._data_table', 'textual.widgets._digits', 'textual.widgets._footer', 'textual.widgets._header', 'textual.widgets._help_panel', 'textual.widgets._key_panel', 'textual.widgets._label', 'textual.widgets._link', 'textual.widgets._list_item', 'textual.widgets._list_view', 'textual.widgets._loading_indicator', 'textual.widgets._log', 'textual.widgets._markdown', 'textual.widgets._masked_input', 'textual.widgets._pretty', 'textual.widgets._progress_bar', 'textual.widgets._radio_button', 'textual.widgets._radio_set', 'textual.widgets._rich_log', 'textual.widgets._rule', 'textual.widgets._selection_list', 'textual.widgets._sparkline', 'textual.widgets._static', 'textual.widgets._switch', 'textual.widgets._text_area', 'textual.widgets._tooltip', 'textual.widgets._welcome', 'textual.clock', 'textual.eta', 'textual.renderables.bar', 'textual._text_area_theme', 'textual._tree_sitter', 'textual.document._document_navigator', 'textual.document._history', 'textual.document._syntax_aware_document', 'textual.validation', 'textual.widgets._toggle_button', 'textual._slug', 'textual._two_way_dict', 'textual.coordinate', 'textual.renderables.styled', 'textual.renderables.sparkline', 'textual._line_split', 'textual._immutable_sequence_view'], ['sklearn.feature_extraction.text', 'sklearn.metrics.pairwise', 'sklearn.feature_extraction', 'sklearn.preprocessing', 'sklearn.linear_model', 'sklearn.naive_bayes', 'sklearn.datasets', 'sklearn.svm'], ['src.utils', 'src.calculator'], ['werkzeug.serving', 'werkzeug.exceptions', 'werkzeug.http', 'werkzeug.routing', 'werkzeug.local', 'werkzeug.datastructures', 'werkzeug.utils', 'werkzeug.wrappers', 'werkzeug.wsgi', 'werkzeug.sansio.response', 'werkzeug.test', 'werkzeug.security'], ['flask.debughelpers', 'flask.globals', 'flask.logging', 'flask.testing', 'flask.views', 'flask.sessions', 'flask.json.tag', 'flask.helpers', 'flask.json.provider', 'flask.cli', 'flask.json', 'flask.templating', 'flask.wrappers', 'flask.signals'], ['importlib.metadata', 'importlib.util', 'importlib.machinery', 'importlib._bootstrap', 'importlib.abc', 'importlib.resources', 'importlib._bootstrap_external', 'importlib.resources.abc', 'importlib.readers', 'importlib.resources.readers'], ['_pytest.monkeypatch', '_pytest.junitxml', '_pytest._py.error', '_pytest._py.path', '_pytest._code', '_pytest.assertion', '_pytest.cacheprovider', '_pytest.capture', '_pytest.config', '_pytest.config.argparsing', '_pytest.debugging', '_pytest.doctest', '_pytest.fixtures', '_pytest.freeze_support', '_pytest.legacypath', '_pytest.logging', '_pytest.main', '_pytest.mark', '_pytest.nodes', '_pytest.outcomes', '_pytest.pytester', '_pytest.python', '_pytest.python_api', '_pytest.recwarn', '_pytest.reports', '_pytest.runner', '_pytest.stash', '_pytest.terminal', '_pytest.tmpdir', '_pytest.warning_types', '_pytest.hookspec', '_pytest.mark.structures', '_pytest._io', '_pytest.deprecated', '_pytest.compat', '_pytest._code.code', '_pytest._io.saferepr', '_pytest._version', '_pytest._io.wcwidth', '_pytest.assertion.util', '_pytest.pathlib', '_pytest.warnings', '_pytest.config.exceptions', '_pytest.pytester_assertions', '_pytest.scope', '_pytest.config.compat', '_pytest._pluggy', '_pytest.helpconfig', '_pytest._argcomplete', '_pytest._code.source', '_pytest._io.pprint', '_pytest.assertion.rewrite', '_pytest.config.findpaths'], ['click.testing', 'click.core', 'click.shell_completion', 'click.parser', 'click.exceptions', 'click.termui', 'click.utils', 'click.formatting', 'click.types'], ['packaging.version', 'packaging.requirements', 'packaging.specifiers', 'packaging.utils', 'packaging.licenses', 'packaging.markers', 'packaging.tags', 'packaging.metadata', 'packaging.licenses._spdx'], ['docutils.nodes', 'docutils.parsers.rst.roles', 'docutils.parsers.rst', 'docutils.statemachine', 'docutils.parsers', 'docutils.frontend', 'docutils.utils', 'docutils.parsers.rst.directives.images'], ['sansio.app', 'sansio.scaffold', 'sansio.blueprints'], ['json.tag', 'json.provider', 'json.decoder'], ['_typeshed.wsgi', '_typeshed.importlib'], ['urllib.parse', 'urllib.request', 'urllib.error', 'urllib.response', 'urllib.robotparser'], ['blueprintapp.apps.admin', 'blueprintapp.apps.frontend'], ['hypothesis.reporting', 'hypothesis.configuration', 'hypothesis.errors', 'hypothesis.internal.escalation', 'hypothesis.internal.healthcheck', 'hypothesis.statistics', 'hypothesis.extra._patching', 'hypothesis.internal.observability', 'hypothesis.internal.conjecture', 'hypothesis.internal.conjecture.data', 'hypothesis.strategies', 'hypothesis.strategies._internal.utils', 'hypothesis._settings', 'hypothesis.internal.conjecture.choice', 'hypothesis.utils.conventions', 'hypothesis.internal.compat', 'hypothesis.internal.reflection', 'hypothesis.internal.validation', 'hypothesis.utils.dynamicvariables', 'hypothesis.vendor.pretty', 'hypothesis.control', 'hypothesis.core', 'hypothesis.entry_points', 'hypothesis.internal.detection', 'hypothesis.internal.entropy', 'hypothesis.version', 'hypothesis.database', 'hypothesis.internal.conjecture.engine', 'hypothesis.internal.conjecture.junkdrawer', 'hypothesis.internal.conjecture.providers', 'hypothesis.internal.conjecture.shrinker', 'hypothesis.internal.scrutineer', 'hypothesis.strategies._internal.misc', 'hypothesis.strategies._internal.strategies', 'hypothesis.stateful', 'hypothesis.strategies._internal.featureflags', 'hypothesis.extra.numpy', 'hypothesis.extra.pytz', 'hypothesis.extra.dateutil', 'hypothesis.strategies._internal', 'hypothesis.strategies._internal.collections', 'hypothesis.strategies._internal.core', 'hypothesis.strategies._internal.datetime', 'hypothesis.strategies._internal.ipaddress', 'hypothesis.strategies._internal.numbers', 'hypothesis.strategies._internal.types', 'hypothesis.extra.django._impl', 'hypothesis.internal.floats', 'hypothesis.internal.intervalsets', 'hypothesis.internal.coverage', 'hypothesis.extra._array_helpers', 'hypothesis.internal.conjecture.utils', 'hypothesis.strategies._internal.regex', 'hypothesis.strategies._internal.lazy', 'hypothesis.provisional', 'hypothesis.strategies._internal.deferred', 'hypothesis.strategies._internal.flatmapped', 'hypothesis.extra', 'hypothesis.utils.terminal', 'hypothesis.extra.django._fields', 'hypothesis.extra.pandas.impl', 'hypothesis.internal.conjecture.pareto', 'hypothesis.internal.conjecture.floats', 'hypothesis.internal.conjecture.shrinking', 'hypothesis.internal.conjecture.shrinking.choicetree', 'hypothesis.internal.cache', 'hypothesis.internal.constants_ast', 'hypothesis.internal.conjecture.datatree', 'hypothesis.internal.conjecture.optimiser', 'hypothesis.internal', 'hypothesis.internal.conjecture.dfa', 'hypothesis.internal.conjecture.shrinking.common', 'hypothesis.internal.conjecture.shrinking.integer', 'hypothesis.internal.conjecture.shrinking.bytes', 'hypothesis.internal.conjecture.shrinking.collection', 'hypothesis.internal.conjecture.shrinking.floats', 'hypothesis.internal.conjecture.shrinking.ordering', 'hypothesis.internal.conjecture.shrinking.string', 'hypothesis.internal.cathetus', 'hypothesis.internal.charmap', 'hypothesis.strategies._internal.functions', 'hypothesis.strategies._internal.recursive', 'hypothesis.strategies._internal.shared', 'hypothesis.strategies._internal.strings', 'hypothesis.strategies._internal.random', 'hypothesis.strategies._internal.attrs', 'hypothesis.internal.filtering'], ['asyncio.coroutines', 'asyncio.proactor_events', 'asyncio.log', 'asyncio.base_events', 'asyncio.base_subprocess'], ['astroid.typing', 'astroid.context', 'astroid.const', 'astroid.exceptions', 'astroid.nodes', 'astroid.bases', 'astroid.util', 'astroid.interpreter._import', 'astroid._backport_stdlib_names', 'astroid.__pkginfo__', 'astroid.brain.helpers', 'astroid.builder', 'astroid.inference_tip', 'astroid.objects', 'astroid.astroid_manager', 'astroid.manager', 'astroid.brain.brain_builtin_inference', 'astroid._ast', 'astroid.nodes.node_classes', 'astroid.nodes.utils', 'astroid.interpreter', 'astroid.modutils', 'astroid.transforms', 'astroid.interpreter._import.spec', 'astroid.interpreter.objectmodel', 'astroid.nodes._base_nodes', 'astroid.nodes.scoped_nodes', 'astroid.constraint', 'astroid.brain.brain_dataclasses', 'astroid.helpers', 'astroid.nodes.as_string', 'astroid.nodes.const', 'astroid.nodes.node_ng', 'astroid.protocols', 'astroid.brain.brain_numpy_utils', 'astroid.brain', 'astroid.filter_statements', 'astroid.nodes.scoped_nodes.utils', 'astroid.nodes.scoped_nodes.mixin', 'astroid.nodes.scoped_nodes.scoped_nodes', 'astroid.interpreter.dunder_lookup'], ['marshmallow.base', 'marshmallow.exceptions', 'marshmallow.utils', 'marshmallow.validate', 'marshmallow.warnings', 'marshmallow.schema', 'marshmallow.decorators', 'marshmallow.fields', 'marshmallow.error_store', 'marshmallow.orderedset', 'marshmallow.constants'], ['email.utils', 'email.feedparser', 'email.header', 'email.message', 'email.parser', 'email.policy', 'email.errors', 'email.headerregistry', 'email.generator', 'email.base64mime', 'email.mime.text', 'email.mime.multipart'], ['mdurl._decode', 'mdurl._encode', 'mdurl._format', 'mdurl._parse', 'mdurl._url'], ['_backends.base', '_backends.mock', '_backends.sync', '_backends.anyio', '_backends.trio', '_backends.auto'], ['networkx.exception', 'networkx.lazy_imports', 'networkx.utils', 'networkx.algorithms', 'networkx.classes', 'networkx.convert', 'networkx.convert_matrix', 'networkx.drawing', 'networkx.generators', 'networkx.linalg', 'networkx.readwrite', 'networkx.relabel', 'networkx.readwrite.graph6', 'networkx.readwrite.adjlist', 'networkx.readwrite.edgelist', 'networkx.readwrite.gexf', 'networkx.readwrite.gml', 'networkx.readwrite.graphml', 'networkx.readwrite.json_graph', 'networkx.readwrite.leda', 'networkx.readwrite.multiline_adjlist', 'networkx.readwrite.pajek', 'networkx.readwrite.sparse6', 'networkx.readwrite.text', 'networkx.drawing.layout', 'networkx.linalg.algebraicconnectivity', 'networkx.linalg.attrmatrix', 'networkx.linalg.bethehessianmatrix', 'networkx.linalg.graphmatrix', 'networkx.linalg.laplacianmatrix', 'networkx.linalg.modularitymatrix', 'networkx.linalg.spectrum', 'networkx.classes.coreviews', 'networkx.classes.reportviews', 'networkx.classes.graph', 'networkx.classes.digraph', 'networkx.classes.multigraph', 'networkx.classes.filters', 'networkx.algorithms.matching', 'networkx.algorithms.components', 'networkx.algorithms.assortativity', 'networkx.algorithms.asteroidal', 'networkx.algorithms.bipartite', 'networkx.algorithms.boundary', 'networkx.algorithms.bridges', 'networkx.algorithms.broadcasting', 'networkx.algorithms.centrality', 'networkx.algorithms.chains', 'networkx.algorithms.chordal', 'networkx.algorithms.clique', 'networkx.algorithms.cluster', 'networkx.algorithms.coloring', 'networkx.algorithms.communicability_alg', 'networkx.algorithms.connectivity', 'networkx.algorithms.core', 'networkx.algorithms.covering', 'networkx.algorithms.cuts', 'networkx.algorithms.cycles', 'networkx.algorithms.d_separation', 'networkx.algorithms.dag', 'networkx.algorithms.distance_measures', 'networkx.algorithms.distance_regular', 'networkx.algorithms.dominance', 'networkx.algorithms.dominating', 'networkx.algorithms.efficiency_measures', 'networkx.algorithms.euler', 'networkx.algorithms.flow', 'networkx.algorithms.graph_hashing', 'networkx.algorithms.graphical', 'networkx.algorithms.hierarchy', 'networkx.algorithms.hybrid', 'networkx.algorithms.isolate', 'networkx.algorithms.isomorphism', 'networkx.algorithms.isomorphism.vf2pp', 'networkx.algorithms.link_analysis', 'networkx.algorithms.link_prediction', 'networkx.algorithms.lowest_common_ancestors', 'networkx.algorithms.minors', 'networkx.algorithms.mis', 'networkx.algorithms.moral', 'networkx.algorithms.non_randomness', 'networkx.algorithms.operators', 'networkx.algorithms.planar_drawing', 'networkx.algorithms.planarity', 'networkx.algorithms.polynomials', 'networkx.algorithms.reciprocity', 'networkx.algorithms.regular', 'networkx.algorithms.richclub', 'networkx.algorithms.shortest_paths', 'networkx.algorithms.similarity', 'networkx.algorithms.simple_paths', 'networkx.algorithms.smallworld', 'networkx.algorithms.smetric', 'networkx.algorithms.sparsifiers', 'networkx.algorithms.structuralholes', 'networkx.algorithms.summarization', 'networkx.algorithms.swap', 'networkx.algorithms.time_dependent', 'networkx.algorithms.tournament', 'networkx.algorithms.traversal', 'networkx.algorithms.tree.branchings', 'networkx.algorithms.tree.coding', 'networkx.algorithms.tree.decomposition', 'networkx.algorithms.tree.mst', 'networkx.algorithms.tree.operations', 'networkx.algorithms.tree.recognition', 'networkx.algorithms.triads', 'networkx.algorithms.vitality', 'networkx.algorithms.voronoi', 'networkx.algorithms.walks', 'networkx.algorithms.wiener', 'networkx.algorithms.shortest_paths.weighted', 'networkx.generators.classic', 'networkx.utils.backends', 'networkx.utils.configs', 'networkx.utils.decorators', 'networkx.utils.heaps', 'networkx.utils.misc', 'networkx.utils.random_sequence', 'networkx.utils.rcm', 'networkx.utils.union_find', 'networkx.generators.atlas', 'networkx.generators.cographs', 'networkx.generators.community', 'networkx.generators.degree_seq', 'networkx.generators.directed', 'networkx.generators.duplication', 'networkx.generators.ego', 'networkx.generators.expanders', 'networkx.generators.geometric', 'networkx.generators.harary_graph', 'networkx.generators.internet_as_graphs', 'networkx.generators.intersection', 'networkx.generators.interval_graph', 'networkx.generators.joint_degree_seq', 'networkx.generators.lattice', 'networkx.generators.line', 'networkx.generators.mycielski', 'networkx.generators.nonisomorphic_trees', 'networkx.generators.random_clustered', 'networkx.generators.random_graphs', 'networkx.generators.small', 'networkx.generators.social', 'networkx.generators.spectral_graph_forge', 'networkx.generators.stochastic', 'networkx.generators.sudoku', 'networkx.generators.time_series', 'networkx.generators.trees', 'networkx.generators.triads', 'networkx.algorithms.operators.product', 'networkx.algorithms.isomorphism.isomorph', 'networkx.utils.mapped_queue', 'networkx.classes.tests.dispatch_interface', 'networkx.classes.tests', 'networkx.algorithms.isomorphism.ismags', 'networkx.algorithms.isomorphism.matchhelpers', 'networkx.algorithms.isomorphism.temporalisomorphvf2', 'networkx.algorithms.isomorphism.tree_isomorphism', 'networkx.algorithms.isomorphism.vf2userfunc', 'networkx.algorithms.link_analysis.hits_alg', 'networkx.algorithms.link_analysis.pagerank_alg', 'networkx.algorithms.operators.all', 'networkx.algorithms.operators.binary', 'networkx.algorithms.operators.unary', 'networkx.algorithms.approximation', 'networkx.algorithms.approximation.clique', 'networkx.algorithms.approximation.clustering_coefficient', 'networkx.algorithms.approximation.connectivity', 'networkx.algorithms.approximation.distance_measures', 'networkx.algorithms.approximation.dominating_set', 'networkx.algorithms.approximation.kcomponents', 'networkx.algorithms.approximation.matching', 'networkx.algorithms.approximation.maxcut', 'networkx.algorithms.approximation.ramsey', 'networkx.algorithms.approximation.steinertree', 'networkx.algorithms.approximation.traveling_salesman', 'networkx.algorithms.approximation.treewidth', 'networkx.algorithms.approximation.vertex_cover', 'networkx.algorithms.centrality.flow_matrix', 'networkx.algorithms.centrality.betweenness', 'networkx.algorithms.traversal.edgedfs', 'networkx.algorithms.threshold', 'networkx.algorithms.bipartite.basic', 'networkx.algorithms.bipartite.centrality', 'networkx.algorithms.bipartite.cluster', 'networkx.algorithms.bipartite.covering', 'networkx.algorithms.bipartite.edgelist', 'networkx.algorithms.bipartite.extendability', 'networkx.algorithms.bipartite.generators', 'networkx.algorithms.bipartite.matching', 'networkx.algorithms.bipartite.matrix', 'networkx.algorithms.bipartite.projection', 'networkx.algorithms.bipartite.redundancy', 'networkx.algorithms.bipartite.spectral', 'networkx.algorithms.minors.contraction', 'networkx.algorithms.shortest_paths.generic', 'networkx.algorithms.shortest_paths.astar', 'networkx.algorithms.shortest_paths.dense', 'networkx.algorithms.shortest_paths.unweighted', 'networkx.algorithms.assortativity.connectivity', 'networkx.algorithms.assortativity.correlation', 'networkx.algorithms.assortativity.mixing', 'networkx.algorithms.assortativity.neighbor_degree', 'networkx.algorithms.assortativity.pairs', 'networkx.algorithms.community.asyn_fluid', 'networkx.algorithms.community.centrality', 'networkx.algorithms.community.community_utils', 'networkx.algorithms.community.divisive', 'networkx.algorithms.community.kclique', 'networkx.algorithms.community.kernighan_lin', 'networkx.algorithms.community.label_propagation', 'networkx.algorithms.community.louvain', 'networkx.algorithms.community.lukes', 'networkx.algorithms.community.modularity_max', 'networkx.algorithms.community.quality', 'networkx.algorithms.community', 'networkx.algorithms.coloring.equitable_coloring', 'networkx.algorithms.coloring.greedy_coloring', 'networkx.algorithms.flow.utils', 'networkx.algorithms.centrality.subgraph_alg', 'networkx.algorithms.connectivity.kcomponents', 'networkx.algorithms.connectivity.kcutsets', 'networkx.algorithms.connectivity.edge_augmentation', 'networkx.algorithms.connectivity.edge_kcomponents', 'networkx.classes.function', 'networkx.algorithms.tree', 'networkx.readwrite.json_graph.adjacency', 'networkx.readwrite.json_graph.cytoscape', 'networkx.readwrite.json_graph.node_link', 'networkx.readwrite.json_graph.tree', 'networkx.readwrite.p2g'], ['toml.tz', 'toml.decoder'], ['vulture.core', 'vulture.version', 'vulture.config', 'vulture.reachability', 'vulture.utils'], ['h11._connection', 'h11._events', 'h11._state', 'h11._util', 'h11._version'], ['distutils.core', 'distutils.ccompiler', 'distutils.command.build_ext', 'distutils.dir_util', 'distutils.errors', 'distutils.log', 'distutils.msvc9compiler', 'distutils._modified', 'distutils.util', 'distutils.extension', 'distutils.filelist', 'distutils.cmd', 'distutils.command', 'distutils.dist', 'distutils.debug', 'distutils.fancy_getopt', 'distutils.command.install_scripts', 'distutils.command.install_egg_info', 'distutils.sysconfig', 'distutils.command.build_py', 'distutils.command.build', 'distutils.command.build_scripts', 'distutils.spawn', 'distutils.command.install_lib', 'distutils.command.bdist', 'distutils.command.sdist', 'distutils.command.bdist_rpm', 'distutils.command.build_clib', 'distutils.command.install', 'distutils.text_file', 'distutils.versionpredicate', 'distutils.tests', 'distutils.version', 'distutils.command.install_data', 'distutils.unixccompiler', 'distutils._log', 'distutils.tests.support', 'distutils.command.install_headers', 'distutils.command.clean', 'distutils.command.check', 'distutils.command.bdist_dumb', 'distutils.file_util', 'distutils.tests.test_dist', 'distutils.command.config', 'distutils.archive_util', 'distutils.compat', 'distutils.tests.compat.py39', 'distutils.cygwinccompiler'], ['mypy.build', 'mypy.errors', 'mypy.fscache', 'mypy.main', 'mypy.options', 'mypy.util', 'mypy.nodes', 'mypy.types', 'mypy.typevartuples', 'mypy.typeops', 'mypy.message_registry', 'mypy.patterns', 'mypy.reachability', 'mypy.sharedparse', 'mypy.traverser', 'mypy.visitor', 'mypy.semanal_main', 'mypy.checker', 'mypy.error_formatter', 'mypy.graph_utils', 'mypy.indirection', 'mypy.messages', 'mypy.partially_defined', 'mypy.semanal', 'mypy.semanal_pass1', 'mypy.report', 'mypy.config_parser', 'mypy.fixup', 'mypy.freetree', 'mypy.metastore', 'mypy.modulefinder', 'mypy.parse', 'mypy.plugin', 'mypy.plugins.default', 'mypy.renaming', 'mypy.stats', 'mypy.stubinfo', 'mypy.typestate', 'mypy.version', 'mypy.server.target', 'mypy.server.deps', 'mypy.refinfo', 'mypy.errorcodes', 'mypy.type_visitor', 'mypy.state', 'mypy.mixedtraverser', 'mypy.find_sources', 'mypy.moduleinspect', 'mypy.plugins.dataclasses', 'mypy.semanal_shared', 'mypy.stubdoc', 'mypy.stubgenc', 'mypy.stubutil', 'mypy.argmap', 'mypy.checkexpr', 'mypy.join', 'mypy.meet', 'mypy.server.update', 'mypy.types_utils', 'mypy.fastparse', 'mypy.exprtotype', 'mypy.erasetype', 'mypy.expandtype', 'mypy.maptype', 'mypy.typetraverser', 'mypy.subtypes', 'mypy.typevars', 'mypy.typeanal', 'mypy.bogus_type', 'mypy.tvar_scope', 'mypy.dmypy.client', 'mypy.literals', 'mypy.plugins', 'mypy.semanal_classprop', 'mypy.semanal_infer', 'mypy.semanal_typeargs', 'mypy.server.aststrip', 'mypy.ipc', 'mypy.copytype', 'mypy.infer', 'mypy.checkmember', 'mypy.dmypy_util', 'mypy.fswatcher', 'mypy.inspections', 'mypy.suggestions', 'mypy.memprofile', 'mypy.operators', 'mypy.scope', 'mypy.lookup', 'mypy.constraints', 'mypy.solve', 'mypy.strconv', 'mypy.split_namespace', 'mypy.checkstrformat', 'mypy.semanal_enum', 'mypy.server.trigger', 'mypy.defaults', 'mypy.binder', 'mypy.checkpattern', 'mypy.mro', 'mypy.treetransform', 'mypy.constant_fold', 'mypy.semanal_namedtuple', 'mypy.semanal_newtype', 'mypy.semanal_typeddict', 'mypy.applytype', 'mypy.evalexpr', 'mypy.plugins.common', 'mypy.dmypy_os', 'mypy.dmypy_server', 'mypy.test.data', 'mypy.test.helpers', 'mypy.test.typefixture', 'mypy.test.config', 'mypy.server.mergecheck', 'mypy.test.testfinegrained', 'mypy.api', 'mypy.server.astdiff', 'mypy.stubgen', 'mypy.test.visitors', 'mypy.stubtest', 'mypy.server.subexpr', 'mypy.test.update_data', 'mypy.plugins.functools', 'mypy.server.astmerge', 'mypy.server.objgraph', 'mypy.test.meta._pytest'], ['mypyc.codegen', 'mypyc.common', 'mypyc.errors', 'mypyc.ir.pprint', 'mypyc.namegen', 'mypyc.options', 'mypyc.ir.rtypes', 'mypyc.ir.func_ir', 'mypyc.subtype', 'mypyc.ir.ops', 'mypyc.ir.class_ir', 'mypyc.irbuild.util', 'mypyc.primitives.bytes_ops', 'mypyc.primitives.dict_ops', 'mypyc.primitives.exc_ops', 'mypyc.primitives.float_ops', 'mypyc.primitives.generic_ops', 'mypyc.primitives.int_ops', 'mypyc.primitives.list_ops', 'mypyc.primitives.misc_ops', 'mypyc.primitives.registry', 'mypyc.primitives.set_ops', 'mypyc.primitives.str_ops', 'mypyc.primitives.tuple_ops', 'mypyc.rt_subtype', 'mypyc.sametype', 'mypyc.irbuild.builder', 'mypyc.irbuild.context', 'mypyc.irbuild.targets', 'mypyc.irbuild.ast_helpers', 'mypyc.irbuild.for_helpers', 'mypyc.irbuild.generator', 'mypyc.irbuild.nonlocalcontrol', 'mypyc.irbuild.constant_fold', 'mypyc.crash', 'mypyc.irbuild.ll_builder', 'mypyc.irbuild.mapper', 'mypyc.irbuild.prebuildvisitor', 'mypyc.irbuild.prepare', 'mypyc.irbuild.function', 'mypyc.irbuild.env_class', 'mypyc.irbuild.classdef', 'mypyc.irbuild.expression', 'mypyc.irbuild.statement', 'mypyc.irbuild.format_str_tokenizer', 'mypyc.analysis.attrdefined', 'mypyc.ir.module_ir', 'mypyc.irbuild.visitor', 'mypyc.irbuild.vtable', 'mypyc.irbuild.specialize', 'mypyc.irbuild.callable_class', 'mypyc.analysis.dataflow', 'mypyc.analysis.selfleaks', 'mypyc.test.testutil', 'mypyc.transform.exceptions', 'mypyc.transform.flag_elimination', 'mypyc.transform.lower', 'mypyc.transform.refcount', 'mypyc.transform.uninit', 'mypyc.primitives', 'mypyc.codegen.literals', 'mypyc.transform.copy_propagation', 'mypyc.build', 'mypyc.test.config', 'mypyc.test.test_serialization', 'mypyc.analysis', 'mypyc.transform', 'mypyc.analysis.blockfreq', 'mypyc.analysis.ircheck', 'mypyc.codegen.emitclass', 'mypyc.codegen.emit', 'mypyc.codegen.emitfunc', 'mypyc.codegen.emitwrapper', 'mypyc.irbuild.main', 'mypyc.codegen.cstring', 'mypyc.lower.registry', 'mypyc.lower', 'mypyc.transform.ir_transform'], ['pylint.__pkginfo__', 'pylint.typing', 'pylint.lint', 'pylint.lint.run', 'pylint.pyreverse.main', 'pylint.checkers.symilar', 'pylint.config.callback_actions', 'pylint.pyreverse.inspector', 'pylint.reporters.ureports.nodes', 'pylint.utils', 'pylint.checkers.utils', 'pylint.config._pylint_config', 'pylint.config.config_initialization', 'pylint.config.exceptions', 'pylint.config.utils', 'pylint.constants', 'pylint.lint.base_options', 'pylint.lint.pylinter', 'pylint.reporters.base_reporter', 'pylint.checkers.base_checker', 'pylint.config.arguments_manager', 'pylint.interfaces', 'pylint.lint.caching', 'pylint.lint.expand_modules', 'pylint.lint.message_state_handler', 'pylint.lint.parallel', 'pylint.lint.report_functions', 'pylint.lint.utils', 'pylint.message', 'pylint.reporters.text', 'pylint.reporters.ureports', 'pylint.utils.pragma_parser', 'pylint.checkers', 'pylint.config.arguments_provider', 'pylint.exceptions', 'pylint.message.message_definition', 'pylint.checkers.deprecated', 'pylint.graph', 'pylint.utils.linterstats', 'pylint.config.deprecation_actions', 'pylint.config.argument', 'pylint.config.help_formatter', 'pylint.config.find_default_config_files', 'pylint.config.config_file_parser', 'pylint.message.message_id_store', 'pylint.message.message', 'pylint.message.message_definition_store', 'pylint.message._deleted_message_ids', 'pylint.utils.ast_walker', 'pylint.utils.docs', 'pylint.utils.file_state', 'pylint.utils.utils', 'pylint.extensions', 'pylint.extensions._check_docs_utils', 'pylint.checkers.exceptions', 'pylint.pyreverse.printer', 'pylint.pyreverse.utils', 'pylint.pyreverse.dot_printer', 'pylint.pyreverse.mermaidjs_printer', 'pylint.pyreverse.plantuml_printer', 'pylint.pyreverse', 'pylint.pyreverse.diagrams', 'pylint.pyreverse.printer_factory', 'pylint.pyreverse.diadefslib', 'pylint.reporters.collecting_reporter', 'pylint.reporters.json_reporter', 'pylint.reporters.multi_reporter', 'pylint.reporters.reports_handler_mix_in', 'pylint.reporters', 'pylint.reporters.ureports.text_writer', 'pylint.testutils.constants', 'pylint.testutils.checker_test_case', 'pylint.testutils.functional.test_file', 'pylint.testutils.output_line', 'pylint.testutils.reporter_for_tests', 'pylint.testutils.decorator', 'pylint.testutils.functional', 'pylint.testutils.get_test_info', 'pylint.testutils.global_test_linter', 'pylint.testutils.lint_module_test', 'pylint.testutils.tokenize_str', 'pylint.testutils.unittest_linter', 'pylint.testutils.utils', 'pylint.testutils._primer', 'pylint.testutils._primer.primer_command', 'pylint.testutils._primer.primer_compare_command', 'pylint.testutils._primer.primer_prepare_command', 'pylint.testutils._primer.primer_run_command', 'pylint.testutils._primer.package_to_lint', 'pylint.testutils.functional.find_functional_tests', 'pylint.testutils.functional.lint_module_output_update', 'pylint.reporters.ureports.base_writer', 'pylint.config._pylint_config.main', 'pylint.config._pylint_config.setup', 'pylint.config._pylint_config.help_message', 'pylint.config._pylint_config.generate_command', 'pylint.checkers.classes.class_checker', 'pylint.checkers.classes.special_methods_checker', 'pylint.checkers.refactoring.implicit_booleaness_checker', 'pylint.checkers.refactoring.not_checker', 'pylint.checkers.refactoring.recommendation_checker', 'pylint.checkers.refactoring.refactoring_checker', 'pylint.checkers.base.basic_error_checker', 'pylint.checkers.base.basic_checker', 'pylint.checkers.base.comparison_checker', 'pylint.checkers.base.docstring_checker', 'pylint.checkers.base.function_checker', 'pylint.checkers.base.name_checker', 'pylint.checkers.base.name_checker.checker', 'pylint.checkers.base.pass_checker', 'pylint.checkers.base.name_checker.naming_style'], ['streamlit.delta_generator_singletons', 'streamlit.elements.alert', 'streamlit.elements.arrow', 'streamlit.elements.balloons', 'streamlit.elements.bokeh_chart', 'streamlit.elements.code', 'streamlit.elements.deck_gl_json_chart', 'streamlit.elements.doc_string', 'streamlit.elements.empty', 'streamlit.elements.exception', 'streamlit.elements.form', 'streamlit.elements.graphviz_chart', 'streamlit.elements.heading', 'streamlit.elements.html', 'streamlit.elements.iframe', 'streamlit.elements.image', 'streamlit.elements.json', 'streamlit.elements.layouts', 'streamlit.elements.lib.form_utils', 'streamlit.elements.map', 'streamlit.elements.markdown', 'streamlit.elements.media', 'streamlit.elements.metric', 'streamlit.elements.plotly_chart', 'streamlit.elements.progress', 'streamlit.elements.pyplot', 'streamlit.elements.snow', 'streamlit.elements.text', 'streamlit.elements.toast', 'streamlit.elements.vega_charts', 'streamlit.elements.widgets.audio_input', 'streamlit.elements.widgets.button', 'streamlit.elements.widgets.button_group', 'streamlit.elements.widgets.camera_input', 'streamlit.elements.widgets.chat', 'streamlit.elements.widgets.checkbox', 'streamlit.elements.widgets.color_picker', 'streamlit.elements.widgets.data_editor', 'streamlit.elements.widgets.file_uploader', 'streamlit.elements.widgets.multiselect', 'streamlit.elements.widgets.number_input', 'streamlit.elements.widgets.radio', 'streamlit.elements.widgets.select_slider', 'streamlit.elements.widgets.selectbox', 'streamlit.elements.widgets.slider', 'streamlit.elements.widgets.text_widgets', 'streamlit.elements.widgets.time_widgets', 'streamlit.elements.write', 'streamlit.errors', 'streamlit.proto', 'streamlit.proto.RootContainer_pb2', 'streamlit.runtime', 'streamlit.runtime.scriptrunner', 'streamlit.cursor', 'streamlit.elements.lib.built_in_chart_utils', 'streamlit.config_option', 'streamlit.logger', 'streamlit.runtime.secrets', 'streamlit.string_util', 'streamlit.type_util', 'streamlit.column_config', 'streamlit.components.v1', 'streamlit.deprecation_util', 'streamlit.version', 'streamlit.delta_generator', 'streamlit.elements.lib.mutable_status_container', 'streamlit.elements.lib.dialog', 'streamlit.elements.dialog_decorator', 'streamlit.runtime.caching', 'streamlit.runtime.connection_factory', 'streamlit.runtime.fragment', 'streamlit.runtime.metrics_util', 'streamlit.runtime.context', 'streamlit.runtime.state', 'streamlit.user_info', 'streamlit.commands.experimental_query_params', 'streamlit.commands.echo', 'streamlit.commands.logo', 'streamlit.commands.navigation', 'streamlit.navigation.page', 'streamlit.elements.spinner', 'streamlit.commands.page_config', 'streamlit.commands.execution_control', 'streamlit.emojis', 'streamlit.material_icon_names', 'streamlit.elements.lib.column_types', 'streamlit.proto.ForwardMsg_pb2', 'streamlit.runtime.scriptrunner_utils.script_run_context', 'streamlit.util', 'streamlit.auth_util', 'streamlit.url_util', 'streamlit.web.cli', 'streamlit.watcher.local_sources_watcher', 'streamlit.watcher.path_watcher', 'streamlit.watcher', 'streamlit.watcher.polling_path_watcher', 'streamlit.watcher.event_based_path_watcher', 'streamlit.watcher.folder_black_list', 'streamlit.runtime.pages_manager', 'streamlit.config', 'streamlit.git_util', 'streamlit.web.server', 'streamlit.runtime.caching.storage.local_disk_cache_storage', 'streamlit.runtime.caching.storage', 'streamlit.web.bootstrap', 'streamlit.runtime.credentials', 'streamlit.web.cache_storage_manager_config', 'streamlit.hello', 'streamlit.temporary_directory', 'streamlit.proto.Delta_pb2', 'streamlit.proto.ClientState_pb2', 'streamlit.proto.Common_pb2', 'streamlit.proto.GitInfo_pb2', 'streamlit.proto.NewSession_pb2', 'streamlit.runtime.forward_msg_queue', 'streamlit.proto.BackMsg_pb2', 'streamlit.runtime.script_data', 'streamlit.runtime.scriptrunner.script_cache', 'streamlit.runtime.uploaded_file_manager', 'streamlit.source_util', 'streamlit.runtime.stats', 'streamlit.connections', 'streamlit.proto.PageProfile_pb2', 'streamlit.runtime.scriptrunner_utils.exceptions', 'streamlit.runtime.media_file_storage', 'streamlit.runtime.session_manager', 'streamlit.runtime.app_session', 'streamlit.runtime.runtime', 'streamlit.components.lib.local_component_registry', 'streamlit.runtime.forward_msg_cache', 'streamlit.runtime.media_file_manager', 'streamlit.runtime.memory_session_storage', 'streamlit.runtime.runtime_util', 'streamlit.runtime.websocket_session_manager', 'streamlit.components.types.base_component_registry', 'streamlit.error_util', 'streamlit.time_util', 'streamlit.proto.openmetrics_data_model_pb2', 'streamlit.connections.util', 'streamlit.connections.base_connection', 'streamlit.connections.snowflake_connection', 'streamlit.connections.snowpark_connection', 'streamlit.connections.sql_connection', 'streamlit.proto.Exception_pb2', 'streamlit.elements.lib.subtitle_utils', 'streamlit.elements.lib.utils', 'streamlit.proto.Audio_pb2', 'streamlit.proto.Video_pb2', 'streamlit.elements.lib.dicttools', 'streamlit.elements.lib.event_utils', 'streamlit.elements.lib.policies', 'streamlit.proto.ArrowVegaLiteChart_pb2', 'streamlit.dataframe_util', 'streamlit.elements.lib.color_util', 'streamlit.proto.GraphVizChart_pb2', 'streamlit.proto.Snow_pb2', 'streamlit.proto.Html_pb2', 'streamlit.proto.Empty_pb2', 'streamlit.proto.Skeleton_pb2', 'streamlit.elements.lib.column_config_utils', 'streamlit.elements.lib.pandas_styler_utils', 'streamlit.proto.Arrow_pb2', 'streamlit.proto.Code_pb2', 'streamlit.proto.IFrame_pb2', 'streamlit.proto.DeckGlJsonChart_pb2', 'streamlit.proto.Metric_pb2', 'streamlit.proto.Heading_pb2', 'streamlit.proto.Markdown_pb2', 'streamlit.proto.DocString_pb2', 'streamlit.runtime.scriptrunner.script_runner', 'streamlit.proto.Text_pb2', 'streamlit.proto.Toast_pb2', 'streamlit.proto.Balloons_pb2', 'streamlit.elements.lib.streamlit_plotly_theme', 'streamlit.proto.PlotlyChart_pb2', 'streamlit.elements.lib.image_utils', 'streamlit.proto.Image_pb2', 'streamlit.proto.Progress_pb2', 'streamlit.proto.Spinner_pb2', 'streamlit.proto.Block_pb2', 'streamlit.proto.Json_pb2', 'streamlit.proto.BokehChart_pb2', 'streamlit.proto.Alert_pb2', 'streamlit.proto.PageConfig_pb2', 'streamlit.file_util', 'streamlit.proto.Navigation_pb2', 'streamlit.runtime.state.query_params', 'streamlit.hello.utils', 'streamlit.external.langchain.streamlit_callback_handler', 'streamlit.proto.ChatInput_pb2', 'streamlit.proto.LabelVisibilityMessage_pb2', 'streamlit.runtime.state.common', 'streamlit.proto.DateInput_pb2', 'streamlit.proto.TimeInput_pb2', 'streamlit.elements.lib.options_selector_utils', 'streamlit.proto.Slider_pb2', 'streamlit.elements.lib.file_uploader_utils', 'streamlit.proto.CameraInput_pb2', 'streamlit.proto.MultiSelect_pb2', 'streamlit.proto.TextArea_pb2', 'streamlit.proto.TextInput_pb2', 'streamlit.elements.lib.js_number', 'streamlit.proto.ColorPicker_pb2', 'streamlit.proto.Button_pb2', 'streamlit.proto.DownloadButton_pb2', 'streamlit.proto.LinkButton_pb2', 'streamlit.proto.PageLink_pb2', 'streamlit.proto.FileUploader_pb2', 'streamlit.proto.AudioInput_pb2', 'streamlit.proto.Radio_pb2', 'streamlit.proto.ButtonGroup_pb2', 'streamlit.proto.Selectbox_pb2', 'streamlit.proto.Checkbox_pb2', 'streamlit.proto.NumberInput_pb2', 'streamlit.components.v1.custom_component', 'streamlit.elements.lib', 'streamlit.proto.Components_pb2', 'streamlit.components.v1.component_registry', 'streamlit.components.types.base_custom_component', 'streamlit.proto.Element_pb2', 'streamlit.runtime.caching.storage.dummy_cache_storage', 'streamlit.runtime.memory_media_file_storage', 'streamlit.runtime.state.safe_session_state', 'streamlit.runtime.state.session_state', 'streamlit.testing.v1.element_tree', 'streamlit.testing.v1.local_script_runner', 'streamlit.testing.v1.util', 'streamlit.proto.WidgetStates_pb2', 'streamlit.testing.v1.app_test', 'streamlit.runtime.memory_uploaded_file_manager', 'streamlit.vendor.pympler.asizeof', 'streamlit.runtime.state.query_params_proxy', 'streamlit.runtime.state.session_state_proxy', 'streamlit.runtime.state.widgets', 'streamlit.runtime.scriptrunner_utils.script_requests', 'streamlit.runtime.scriptrunner.exec_code', 'streamlit.runtime.caching.cache_type', 'streamlit.runtime.caching.cache_errors', 'streamlit.runtime.caching.cache_utils', 'streamlit.runtime.caching.cached_message_replay', 'streamlit.runtime.caching.storage.cache_storage_protocol', 'streamlit.runtime.caching.hashing', 'streamlit.runtime.caching.cache_data_api', 'streamlit.runtime.caching.cache_resource_api', 'streamlit.runtime.caching.legacy_cache_api', 'streamlit.runtime.caching.storage.in_memory_cache_storage_wrapper', 'streamlit.web.server.routes', 'streamlit.web.server.app_static_file_handler', 'streamlit.web.server.browser_websocket_handler', 'streamlit.web.server.component_request_handler', 'streamlit.web.server.media_file_handler', 'streamlit.web.server.server_util', 'streamlit.web.server.stats_request_handler', 'streamlit.web.server.upload_file_request_handler', 'streamlit.web.server.oauth_authlib_routes', 'streamlit.web.server.oidc_mixin', 'streamlit.web.server.server', 'streamlit.web.server.authlib_tornado_integration', 'streamlit.elements.lib.layout_utils', 'streamlit.elements', 'streamlit.web', 'streamlit.runtime.context_util', 'streamlit.proto.WidthConfig_pb2', 'streamlit.proto.GapSize_pb2', 'streamlit.proto.HeightConfig_pb2'], ['google.protobuf.message', 'google.auth.credentials', 'google.appengine.api', 'google.protobuf.internal', 'google.protobuf.pyext', 'google.protobuf', 'google.protobuf.descriptor_pool', 'google.protobuf.duration_pb2', 'google.protobuf.any_pb2', 'google.protobuf.timestamp_pb2', 'google._upb', 'google.protobuf.descriptor', 'google.protobuf.json_format', 'google.colab'], ['authlib.jose', 'authlib.common.encoding', 'authlib.common.urls', 'authlib.common.security', 'authlib.common.errors', 'authlib.deprecate', 'authlib.jose.errors', 'authlib.consts', 'authlib.oauth2', 'authlib.oauth2.rfc6749', 'authlib.oauth2.rfc6750', 'authlib.oidc.core', 'authlib.oauth2.auth', 'authlib.oauth2.client', 'authlib.oauth1', 'authlib.oauth2.rfc7521', 'authlib.oauth2.rfc7523', 'authlib.oauth1.client', 'authlib.oauth2.rfc7009', 'authlib.oauth1.errors', 'authlib.jose.rfc7516.models', 'authlib.jose.util', 'authlib.jose.rfc7516', 'authlib.jose.rfc7518', 'authlib.jose.rfc8037', 'authlib.oauth2.rfc8414', 'authlib.oauth2.rfc8414.models', 'authlib.oauth2.rfc6749.grants.authorization_code', 'authlib.oauth2.base', 'authlib.oauth2.rfc6750.token', 'authlib.jose.rfc7519', 'authlib.oauth2.rfc6750.errors', 'authlib.oauth2.rfc9068.token_validator', 'authlib.oauth2.rfc6750.validator', 'authlib.integrations.base_client', 'authlib.integrations.base_client.errors', 'authlib.integrations.requests_client', 'authlib.oauth2.rfc6749.util', 'authlib.oauth2.rfc6749.authorization_server', 'authlib.oauth2.rfc6749.resource_protector', 'authlib.oauth2.rfc6749.hooks', 'authlib.oidc.discovery.models', 'authlib.oauth2.rfc6749.grants'], ['pandas.core.indexing', 'pandas.io.formats.style', 'pandas.api.types', 'pandas.core.frame', 'pandas.core.series', 'pandas.core.window.rolling', 'pandas.core.window', 'pandas.core.window.expanding', 'pandas.core.groupby.generic', 'pandas.core.groupby.groupby', 'pandas.core.groupby', 'pandas.core.common', 'pandas._testing', 'pandas.util._test_decorators', 'pandas._config.config', 'pandas.core', 'pandas.core.dtypes.dtypes', 'pandas.core.indexes.api', 'pandas.util.version', 'pandas._libs', 'pandas._libs.tslibs', 'pandas.arrays', 'pandas.core.arrays.base', 'pandas.core.generic', 'pandas.core.indexes.base', 'pandas.core.internals', 'pandas.core.resample', 'pandas.io.formats.format', 'pandas.tseries.holiday', 'pandas.core.config_init', 'pandas.compat', 'pandas._config', 'pandas.core.api', 'pandas.core.computation.api', 'pandas.core.reshape.api', 'pandas.io.api', 'pandas.io.json._normalize', 'pandas.tseries', 'pandas.tseries.api', 'pandas.util._print_versions', 'pandas.util._tester', 'pandas._version_meson', 'pandas._version', 'pandas.api.internals', 'pandas.testing', 'pandas.tseries.offsets', 'pandas.api.interchange', 'pandas.util._exceptions', 'pandas.compat.compressors', 'pandas.compat._constants', 'pandas.compat.numpy', 'pandas.compat.pyarrow', 'pandas._typing', 'pandas._libs.arrays', 'pandas.core.arrays', 'pandas.util._decorators', 'pandas.compat._optional', 'pandas.core.dtypes.common', 'pandas.core.dtypes.missing', 'pandas.core.dtypes.cast', 'pandas.core.array_algos.take', 'pandas.core.construction', 'pandas.core.dtypes.concat', 'pandas.core.dtypes.generic', 'pandas.core.indexers', 'pandas.core.reshape.tile', 'pandas.core.internals.construction', 'pandas.core.sorting', 'pandas.core.algorithms', 'pandas._libs.tslibs.dtypes', 'pandas.core.apply', 'pandas.core.base', 'pandas.core.groupby.grouper', 'pandas.core.groupby.ops', 'pandas.core.indexes.datetimes', 'pandas.core.indexes.period', 'pandas.core.indexes.timedeltas', 'pandas.errors', 'pandas.tseries.frequencies', 'pandas._libs.ops_dispatch', 'pandas.core.ops.common', 'pandas.core.dtypes.base', 'pandas.core.arrays.string_', 'pandas.core.computation', 'pandas.core.util', 'pandas.io.formats.printing', 'pandas.plotting._core', 'pandas.plotting', 'pandas._libs.lib', 'pandas.core.array_algos.replace', 'pandas.core.dtypes.astype', 'pandas.core.dtypes.inference', 'pandas.core.flags', 'pandas.core.methods.describe', 'pandas.core.missing', 'pandas.core.reshape.concat', 'pandas.core.shared_docs', 'pandas.util._validators', 'pandas.core.indexers.objects', 'pandas.core.computation.parsing', 'pandas.io.formats.excel', 'pandas.io', 'pandas.io.pickle', 'pandas.core.tools.datetimes', 'pandas.core.accessor', 'pandas.core.arrays.arrow', 'pandas.core.arrays.categorical', 'pandas.core.arrays.sparse', 'pandas.core.indexes.accessors', 'pandas.core.indexes.multi', 'pandas.core.methods', 'pandas.core.strings.accessor', 'pandas.io.formats.info', 'pandas._libs.internals', 'pandas.core.reshape.reshape', 'pandas._libs.hashtable', 'pandas._libs.missing', 'pandas.core.arrays.boolean', 'pandas.core.arrays.floating', 'pandas.core.arrays.integer', 'pandas.core.indexes.interval', 'pandas.core.tools.numeric', 'pandas.core.tools.timedeltas', 'pandas.core.arraylike', 'pandas.core.reshape.melt', 'pandas.io.common', 'pandas.io.formats', 'pandas.core.interchange.dataframe_protocol', 'pandas.core.interchange.dataframe', 'pandas.core.methods.to_dict', 'pandas.io.stata', 'pandas.io.feather_format', 'pandas.io.parquet', 'pandas.io.orc', 'pandas.io.formats.xml', 'pandas.core.arrays.masked', 'pandas.core.arrays.arrow.array', 'pandas.core.computation.eval', 'pandas.core.reshape.pivot', 'pandas.core.reshape.merge', 'pandas._libs.indexing', 'pandas.core._numba.executor', 'pandas.core._numba.extensions', 'pandas.core.util.hashing', 'pandas._libs.properties', 'pandas.io._util', 'pandas.core.arrays.arrow.extension_types', 'pandas.compat.pickle_compat', 'pandas.core.computation.pytables', 'pandas.io.parsers', 'pandas.io.clipboard', 'pandas.io.clipboards', 'pandas.io.excel', 'pandas.io.gbq', 'pandas.io.html', 'pandas.io.json', 'pandas.io.pytables', 'pandas.io.sas', 'pandas.io.spss', 'pandas.io.sql', 'pandas.io.xml', 'pandas._libs.writers', 'pandas.core.indexes.range', 'pandas._libs.tslibs.parsing', 'pandas._libs.tslibs.offsets', 'pandas._libs.algos', 'pandas._libs.tslibs.ccalendar', 'pandas._libs.tslibs.fields', 'pandas.core.arrays.datetimelike', 'pandas._testing.contexts', 'pandas._config.localization', 'pandas._testing._io', 'pandas._testing._warnings', 'pandas._testing.asserters', 'pandas._testing.compat', 'pandas.core.arrays._mixins', 'pandas._libs.testing', 'pandas._libs.sparse', 'pandas._libs.tslibs.np_datetime', 'pandas._libs.pandas_parser', 'pandas._libs.pandas_datetime', 'pandas._libs.interval', 'pandas.plotting._misc', 'pandas.api', 'pandas._config.display', 'pandas.core.dtypes.api', 'pandas.core.interchange.from_dataframe', 'pandas.io.json._json', 'pandas.plotting._matplotlib.style', 'pandas.plotting._matplotlib.tools', 'pandas.plotting._matplotlib.core', 'pandas.plotting._matplotlib.groupby', 'pandas.plotting._matplotlib.misc', 'pandas.plotting._matplotlib.boxplot', 'pandas.plotting._matplotlib.converter', 'pandas.plotting._matplotlib.hist', 'pandas.plotting._matplotlib', 'pandas.plotting._matplotlib.timeseries', 'pandas._libs.tslibs.conversion', 'pandas._libs.tslibs.nattype', 'pandas._libs.tslibs.period', 'pandas._libs.tslibs.timedeltas', 'pandas._libs.tslibs.timestamps', 'pandas._libs.tslibs.timezones', 'pandas._libs.tslibs.tzconversion', 'pandas._libs.tslibs.vectorized', 'pandas.core.internals.blocks', 'pandas.core.computation.check', 'pandas.core.reshape', 'pandas.core.reshape.util', 'pandas.tests.apply.common', 'pandas.core.groupby.base', 'pandas.tests.frame.common', 'pandas.tests.strings', 'pandas.core.tools.times', 'pandas.core.tools', 'pandas.tests.indexes.datetimes.test_timezones', 'pandas.tests.extension', 'pandas.core.dtypes', 'pandas.core.arrays.numpy_', 'pandas.api.extensions', 'pandas.util', 'pandas.tests.io.generate_legacy_storage_files', 'pandas.tests.copy_view.util', 'pandas.core.interchange.utils', 'pandas.core.interchange.column', 'pandas._libs.join', 'pandas.tests.groupby', 'pandas.api.typing', 'pandas._libs.groupby', 'pandas.core.computation.engines', 'pandas.core.computation.expr', 'pandas.core.computation.expressions', 'pandas.core.computation.ops', 'pandas.core.computation.scope', 'pandas.tests.plotting.common', 'pandas._libs.window.aggregations', 'pandas.api.indexers', 'pandas.tests.extension.decimal', 'pandas.tests.extension.base', 'pandas.tests.arithmetic.common', 'pandas.core.ops.array_ops', 'pandas.tests.api.test_api', 'pandas._libs.tslibs.strptime', 'pandas._testing._hypothesis', 'pandas.tests.indexing.common', 'pandas.tests.indexing.test_floats', 'pandas.core.arrays.string_arrow', 'pandas.tests.base.common', 'pandas.core.indexes.frozen', 'pandas.core.indexes.datetimelike', 'pandas.core.arrays.datetimes', 'pandas.core.arrays.timedeltas', 'pandas._libs.index', 'pandas.core.arrays.arrow._arrow_utils', 'pandas.core.ops.mask_ops', 'pandas.tests.arrays.masked_shared', 'pandas.tests.extension.decimal.array', 'pandas.tests.tseries.offsets.common', 'pandas.io.formats.css', 'pandas.io.excel._util', 'pandas.io.excel._openpyxl', 'pandas.io.excel._base', 'pandas.io.parsers.c_parser_wrapper', 'pandas.io.parsers.base_parser', 'pandas._libs.parsers', 'pandas.io.parsers.readers', 'pandas.io.json._table_schema', 'pandas.tests.extension.date', 'pandas._libs.json', 'pandas.io.sas.sas7bdat', 'pandas._libs.byteswap', 'pandas.io.sas.sasreader', 'pandas.tests.io.pytables.common', 'pandas.io.formats.style_render', 'pandas.tests.extension.array_with_attr', 'pandas.tests.extension.array_with_attr.array', 'pandas.tests.extension.date.array', 'pandas.tests.extension.json.array', 'pandas.tests.extension.list.array', 'pandas.tests.extension.base.accumulate', 'pandas.tests.extension.base.casting', 'pandas.tests.extension.base.constructors', 'pandas.tests.extension.base.dim2', 'pandas.tests.extension.base.dtype', 'pandas.tests.extension.base.getitem', 'pandas.tests.extension.base.groupby', 'pandas.tests.extension.base.index', 'pandas.tests.extension.base.interface', 'pandas.tests.extension.base.io', 'pandas.tests.extension.base.methods', 'pandas.tests.extension.base.missing', 'pandas.tests.extension.base.ops', 'pandas.tests.extension.base.printing', 'pandas.tests.extension.base.reduce', 'pandas.tests.extension.base.reshaping', 'pandas.tests.extension.base.setitem', 'pandas.io.parsers.arrow_parser_wrapper', 'pandas.io.parsers.python_parser', 'pandas._libs.ops', 'pandas.io.formats.html', 'pandas.io.formats.string', 'pandas.io.formats.csvs', 'pandas.io.formats._color_data', 'pandas.io.formats.console', 'pandas.io.excel._calamine', 'pandas.io.excel._odfreader', 'pandas.io.excel._pyxlsb', 'pandas.io.excel._xlrd', 'pandas.io.excel._odswriter', 'pandas.io.excel._xlsxwriter', 'pandas.io.sas.sas_constants', 'pandas._libs.sas', 'pandas.io.sas.sas_xport', 'pandas.core.indexers.utils', 'pandas._libs.window.indexers', 'pandas._libs.reshape', 'pandas.core.reshape.encoding', 'pandas.core.strings.base', 'pandas._libs.hashing', 'pandas.core.nanops', 'pandas.core.interchange.buffer', 'pandas.core.arrays.sparse.array', 'pandas.core.util.numba_', 'pandas.core.groupby.categorical', 'pandas.core._numba', 'pandas.core.groupby.indexing', 'pandas.core._numba.kernels', 'pandas.core.internals.array_manager', 'pandas.core.internals.managers', 'pandas.core.internals.api', 'pandas.core.internals.base', 'pandas.core.internals.concat', 'pandas.core.array_algos.quantile', 'pandas.core.array_algos.putmask', 'pandas.core.array_algos.transforms', 'pandas.core.internals.ops', 'pandas.core.computation.common', 'pandas.core.computation.align', 'pandas.core.window.common', 'pandas.core.window.doc', 'pandas.core.window.numba_', 'pandas.core.window.online', 'pandas.core.window.ewm', 'pandas.core.arrays.numeric', 'pandas.core.strings.object_array', 'pandas.core.ops', 'pandas.core.array_algos', 'pandas.core.arrays._ranges', 'pandas.core.arrays.interval', 'pandas.core.arrays.period', 'pandas.core.arrays._arrow_string_mixins', 'pandas.core.arrays._utils', 'pandas.core.ops.invalid', 'pandas.core.ops.dispatch', 'pandas.core.ops.docstrings', 'pandas.core.roperator', 'pandas.core.indexes.extension', 'pandas.core.indexes.category', 'pandas.core.arrays.arrow.accessors', 'pandas.core.arrays.sparse.scipy_sparse', 'pandas.core.arrays.sparse.accessor', 'pandas.core._numba.kernels.shared', 'pandas.core._numba.kernels.mean_', 'pandas.core._numba.kernels.min_max_', 'pandas.core._numba.kernels.sum_', 'pandas.core._numba.kernels.var_', 'pandas.tseries.converter'], ['plotly.graph_objs', 'plotly.io', 'plotly.tools', 'plotly.basedatatypes', 'plotly.graph_objects', 'plotly.subplots', 'plotly.validators', 'plotly.files', 'plotly.figure_factory', 'plotly.version', 'plotly.offline.offline', 'plotly.io._utils', 'plotly._subplots', 'plotly.io.kaleido', 'plotly.utils', 'plotly.io._renderers', 'plotly.express._core', 'plotly.basewidget', 'plotly.validators.layout', 'plotly.colors', 'plotly.io._base_renderers', 'plotly.io.orca', 'plotly.io._defaults', 'plotly.io._orca', 'plotly.io.json', 'plotly.optional_imports', 'plotly.graph_objs.layout', 'plotly.offline', 'plotly.matplotlylib.mplexporter', 'plotly.matplotlylib', 'plotly.matplotlylib.renderer', 'plotly.validators.heatmap', 'plotly.exceptions', 'plotly.figure_factory._2d_density', 'plotly.figure_factory._annotated_heatmap', 'plotly.figure_factory._bullet', 'plotly.figure_factory._candlestick', 'plotly.figure_factory._dendrogram', 'plotly.figure_factory._distplot', 'plotly.figure_factory._facet_grid', 'plotly.figure_factory._gantt', 'plotly.figure_factory._ohlc', 'plotly.figure_factory._quiver', 'plotly.figure_factory._scatterplot', 'plotly.figure_factory._streamline', 'plotly.figure_factory._table', 'plotly.figure_factory._trisurf', 'plotly.figure_factory._violin', 'plotly.figure_factory._county_choropleth', 'plotly.figure_factory._hexbin_mapbox', 'plotly.figure_factory._ternary_contour', 'plotly.express._doc', 'plotly.express._chart_types', 'plotly.data', 'plotly.express'], ['rich.console', 'rich.align', 'rich.columns', 'rich.emoji', 'rich.highlighter', 'rich.markdown', 'rich.padding', 'rich.panel', 'rich.table', 'rich.text', 'rich.theme', 'rich.traceback', 'rich.prompt', 'rich.markup', 'rich.progress', 'rich.syntax', 'rich._null_file', 'rich.json', 'rich._win32_console', 'rich._windows_renderer', 'rich.cells', 'rich.tree', 'rich.styled', 'rich._inspect', 'rich.color', 'rich.style', 'rich.segment', 'rich.repr', 'rich.pretty', 'rich.containers', 'rich.measure', 'rich.live', 'rich.status', 'rich.box', 'rich.color_triplet', 'rich.terminal_theme', 'rich.protocol', 'rich.control', 'rich._wrap', 'rich.errors'], ['attr.setters', 'attr.validators', 'attr._next_gen', 'attr.exceptions', 'attr.converters', 'attr.filters'], ['pygments.styles', 'pygments.util', 'pygments.token', 'pygments.lexers', 'pygments.lexers._mapping', 'pygments.formatters', 'pygments.filters', 'pygments.formatters.latex', 'pygments.formatters.terminal', 'pygments.formatters.terminal256', 'pygments.lexers.special', 'pygments.lexer', 'pygments.formatter', 'pygments.filter', 'pygments.regexopt', 'pygments.cmdline', 'pygments.style', 'pygments.lexers.python', 'pygments.lexers.diff', 'pygments.plugin', 'pygments.lexers.c_cpp', 'pygments.lexers.d', 'pygments.lexers.factor', 'pygments.lexers.iolang', 'pygments.lexers.jvm', 'pygments.lexers.lisp', 'pygments.lexers.perl', 'pygments.lexers.ruby', 'pygments.lexers.scripting', 'pygments.lexers.tcl', 'pygments.lexers._asy_builtins', 'pygments.lexers._ada_builtins', 'pygments.lexers._cocoa_builtins', 'pygments.lexers._usd_builtins', 'pygments.lexers.actionscript', 'pygments.lexers.css', 'pygments.lexers.data', 'pygments.lexers.html', 'pygments.lexers.javascript', 'pygments.lexers.php', 'pygments.lexers.webmisc', 'pygments.lexers.web', 'pygments.lexers.theorem', 'pygments.lexers.shell', 'pygments.lexers._sourcemod_builtins', 'pygments.lexers._scheme_builtins', 'pygments.lexers._cl_builtins', 'pygments.lexers._lua_builtins', 'pygments.lexers._luau_builtins', 'pygments.modeline', 'pygments.lexers.automation', 'pygments.lexers.basic', 'pygments.lexers.business', 'pygments.lexers.configs', 'pygments.lexers.dsls', 'pygments.lexers.ecl', 'pygments.lexers.esoteric', 'pygments.lexers.graphics', 'pygments.lexers.installers', 'pygments.lexers.modeling', 'pygments.lexers.pawn', 'pygments.lexers.prolog', 'pygments.lexers.rebol', 'pygments.lexers.robotframework', 'pygments.lexers.smalltalk', 'pygments.lexers.smv', 'pygments.lexers.snobol', 'pygments.lexers.sql', 'pygments.lexers.testing', 'pygments.lexers.textedit', 'pygments.lexers.urbi', 'pygments.lexers.dotnet', 'pygments.lexers.objective', 'pygments.lexers.lean', 'pygments.lexers.ada', 'pygments.lexers.c_like', 'pygments.lexers.crystal', 'pygments.lexers.dylan', 'pygments.lexers.felix', 'pygments.lexers.fortran', 'pygments.lexers.go', 'pygments.lexers.ml', 'pygments.lexers.nimrod', 'pygments.lexers.ooc', 'pygments.lexers.pascal', 'pygments.lexers.rust', 'pygments.lexers._lilypond_builtins', 'pygments.lexers.lilypond', 'pygments.lexers._css_builtins', 'pygments.lexers.modula2', 'pygments.scanner', 'pygments.lexers._csound_builtins', 'pygments.lexers.erlang', 'pygments.lexers.haskell', 'pygments.lexers._vim_builtins', 'pygments.lexers._openedge_builtins', 'pygments.lexers.mime', 'pygments.lexers._qlik_builtins', 'pygments.lexers.console', 'pygments.lexers.haxe', 'pygments.lexers.make', 'pygments.lexers.markup', 'pygments.lexers.sgf', 'pygments.lexers.textfmts', 'pygments.lexers.algebra', 'pygments.lexers.idl', 'pygments.lexers.julia', 'pygments.lexers.matlab', 'pygments.lexers.r', 'pygments.lexers._php_builtins', 'pygments.lexers._mysql_builtins', 'pygments.lexers._postgres_builtins', 'pygments.unistring', 'pygments.lexers._lasso_builtins', 'pygments.lexers._julia_builtins', 'pygments.lexers._stata_builtins', 'pygments.console', 'pygments.formatters._mapping', 'pygments.styles._mapping'], ['sphinx.util.nodes', 'sphinx.ext.autodoc', 'sphinx.util', 'sphinx.errors', 'sphinx.ext.napoleon'], ['typer.core', 'typer.main', 'typer.rich_utils', 'typer._types'], ['html.entities', 'html.parser'], ['cryptography.__about__', 'cryptography.hazmat.bindings._rust', 'cryptography.exceptions', 'cryptography.hazmat.primitives', 'cryptography.hazmat.primitives.ciphers', 'cryptography.hazmat.primitives.hmac', 'cryptography.hazmat.backends', 'cryptography.hazmat.primitives.asymmetric', 'cryptography.hazmat.primitives.serialization', 'cryptography.x509', 'cryptography.hazmat.primitives.ciphers.algorithms', 'cryptography.hazmat.primitives.ciphers.modes', 'cryptography.hazmat.primitives.padding', 'cryptography.hazmat.primitives.asymmetric.ec', 'cryptography.hazmat.primitives.asymmetric.utils', 'cryptography.hazmat.primitives.kdf.concatkdf', 'cryptography.hazmat.primitives.keywrap', 'cryptography.hazmat.primitives.asymmetric.rsa', 'cryptography.hazmat.primitives.ciphers.aead', 'cryptography.hazmat.primitives.asymmetric.ed448', 'cryptography.hazmat.primitives.asymmetric.ed25519', 'cryptography.hazmat.primitives.asymmetric.x448', 'cryptography.hazmat.primitives.asymmetric.x25519', 'cryptography.x509.extensions', 'cryptography.hazmat.backends.openssl', 'cryptography.hazmat._oid', 'cryptography.x509.general_name', 'cryptography.hazmat.primitives.asymmetric.types', 'cryptography.x509.base', 'cryptography.x509.name', 'cryptography.x509.oid', 'cryptography.x509.certificate_transparency', 'cryptography.hazmat.backends.openssl.backend', 'cryptography.hazmat.primitives.hashes', 'cryptography.hazmat.primitives.constant_time', 'cryptography.hazmat.bindings.openssl._conditional', 'cryptography.hazmat.primitives.kdf', 'cryptography.hazmat.primitives.twofactor', 'cryptography.hazmat.primitives.twofactor.hotp', 'cryptography.hazmat.primitives._serialization', 'cryptography.hazmat.primitives.serialization.base', 'cryptography.hazmat.primitives.serialization.ssh', 'cryptography.utils', 'cryptography.hazmat.decrepit.ciphers.algorithms', 'cryptography.hazmat.primitives._cipheralgorithm', 'cryptography.hazmat.primitives.ciphers.base', 'cryptography.hazmat.primitives._asymmetric', 'cryptography.hazmat.bindings.openssl', 'cryptography.hazmat.primitives.asymmetric.padding'], ['nltk.corpus', 'nltk.tree', 'nltk.draw.table', 'nltk.draw.util', 'nltk.corpus.reader.util', 'nltk.data', 'nltk.collections', 'nltk.internals', 'nltk.collocations', 'nltk.decorators', 'nltk.featstruct', 'nltk.grammar', 'nltk.probability', 'nltk.text', 'nltk.util', 'nltk.jsontags', 'nltk.chunk', 'nltk.classify', 'nltk.inference', 'nltk.metrics', 'nltk.parse', 'nltk.tag', 'nltk.tokenize', 'nltk.translate', 'nltk.sem', 'nltk.stem', 'nltk.downloader', 'nltk.tree.prettyprinter', 'nltk.lm', 'nltk.lm.preprocessing', 'nltk.draw', 'nltk.tree.transforms', 'nltk.metrics.spearman', 'nltk.sem.logic', 'nltk.app.wordnet_app', 'nltk.classify.maxent', 'nltk.misc.babelfish', 'nltk.misc.chomsky', 'nltk.misc.minimalset', 'nltk.misc.wordfinder', 'nltk.tree.parented', 'nltk.tree.tree', 'nltk.draw.tree', 'nltk.tree.immutable', 'nltk.tree.parsing', 'nltk.tree.probabilistic', 'nltk.metrics.distance', 'nltk.metrics.agreement', 'nltk.metrics.aline', 'nltk.metrics.association', 'nltk.metrics.confusionmatrix', 'nltk.metrics.paice', 'nltk.metrics.scores', 'nltk.metrics.segmentation', 'nltk.twitter.util', 'nltk.twitter.twitterclient', 'nltk.twitter.common', 'nltk.twitter', 'nltk.twitter.api', 'nltk.cluster.api', 'nltk.cluster.em', 'nltk.cluster.gaac', 'nltk.cluster.kmeans', 'nltk.cluster.util', 'nltk.cluster', 'nltk.chunk.regexp', 'nltk.app.chartparser_app', 'nltk.app.chunkparser_app', 'nltk.app.collocations_app', 'nltk.app.concordance_app', 'nltk.app.nemo_app', 'nltk.app.rdparser_app', 'nltk.app.srparser_app', 'nltk.app.wordfreq_app', 'nltk.parse.chart', 'nltk.corpus.reader.wordnet', 'nltk.tag.api', 'nltk.classify.util', 'nltk.classify.api', 'nltk.classify.decisiontree', 'nltk.classify.megam', 'nltk.classify.naivebayes', 'nltk.classify.positivenaivebayes', 'nltk.classify.rte_classify', 'nltk.classify.scikitlearn', 'nltk.classify.senna', 'nltk.classify.textcat', 'nltk.classify.weka', 'nltk.classify.tadm', 'nltk.tabdata', 'nltk.tag.sequential', 'nltk.tag.mapping', 'nltk.tag.util', 'nltk.chunk.api', 'nltk.chunk.named_entity', 'nltk.chunk.util', 'nltk.parse.malt', 'nltk.corpus.reader', 'nltk.chat.util', 'nltk.chat.eliza', 'nltk.chat.iesha', 'nltk.chat.rude', 'nltk.chat.suntsu', 'nltk.chat.zen', 'nltk.stem.api', 'nltk.stem.porter', 'nltk.translate.ibm_model', 'nltk.translate.api', 'nltk.translate.ibm1', 'nltk.translate.ibm2', 'nltk.translate.ibm3', 'nltk.translate.ibm4', 'nltk.translate.ibm5', 'nltk.translate.bleu_score', 'nltk.translate.ribes_score', 'nltk.translate.meteor_score', 'nltk.translate.metrics', 'nltk.translate.stack_decoder', 'nltk.translate.nist_score', 'nltk.translate.chrf_score', 'nltk.translate.gale_church', 'nltk.translate.gdfa', 'nltk.translate.gleu_score', 'nltk.translate.phrase_based', 'nltk.lm.api', 'nltk.lm.smoothing', 'nltk.lm.counter', 'nltk.lm.models', 'nltk.lm.vocabulary', 'nltk.lm.util', 'nltk.sem.skolemize', 'nltk.sem.drt', 'nltk.sem.boxer', 'nltk.sem.evaluate', 'nltk.sem.lfg', 'nltk.sem.relextract', 'nltk.sem.util', 'nltk.sem.glue', 'nltk.parse.featurechart', 'nltk.parse.dependencygraph', 'nltk.corpus.util', 'nltk.corpus.reader.api', 'nltk.parse.api', 'nltk.parse.util', 'nltk.parse.pchart', 'nltk.tokenize.api', 'nltk.parse.bllip', 'nltk.parse.corenlp', 'nltk.parse.earleychart', 'nltk.parse.evaluate', 'nltk.parse.nonprojectivedependencyparser', 'nltk.parse.projectivedependencyparser', 'nltk.parse.recursivedescent', 'nltk.parse.shiftreduce', 'nltk.parse.transitionparser', 'nltk.parse.viterbi', 'nltk.tbl.template', 'nltk.tbl.feature', 'nltk.tbl.rule', 'nltk.tbl.erroranalysis', 'nltk.tag.brill', 'nltk.tbl', 'nltk.stem.arlstem', 'nltk.stem.arlstem2', 'nltk.stem.cistem', 'nltk.stem.isri', 'nltk.stem.lancaster', 'nltk.stem.regexp', 'nltk.stem.rslp', 'nltk.stem.snowball', 'nltk.stem.wordnet', 'nltk.stem.util', 'nltk.inference.api', 'nltk.inference.discourse', 'nltk.inference.mace', 'nltk.inference.prover9', 'nltk.inference.resolution', 'nltk.inference.tableau', 'nltk.tokenize.casual', 'nltk.sentiment', 'nltk.sentiment.sentiment_analyzer', 'nltk.sentiment.vader', 'nltk.draw.cfg', 'nltk.draw.dispersion', 'nltk.tag.brill_trainer', 'nltk.tag.tnt', 'nltk.tag.hunpos', 'nltk.tag.stanford', 'nltk.tag.hmm', 'nltk.tag.senna', 'nltk.tag.crf', 'nltk.tag.perceptron', 'nltk.tokenize.util', 'nltk.tokenize.destructive', 'nltk.tokenize.legality_principle', 'nltk.tokenize.mwe', 'nltk.tokenize.punkt', 'nltk.tokenize.regexp', 'nltk.tokenize.repp', 'nltk.tokenize.sexpr', 'nltk.tokenize.simple', 'nltk.tokenize.sonority_sequencing', 'nltk.tokenize.stanford_segmenter', 'nltk.tokenize.texttiling', 'nltk.tokenize.toktok', 'nltk.tokenize.treebank', 'nltk.ccg.api', 'nltk.ccg.chart', 'nltk.ccg.combinator', 'nltk.ccg.lexicon', 'nltk.ccg.logic', 'nltk.corpus.reader.xmldocs', 'nltk.corpus.reader.wordlist', 'nltk.corpus.reader.plaintext', 'nltk.toolbox', 'nltk.corpus.reader.bracket_parse', 'nltk.corpus.reader.tagged', 'nltk.corpus.reader.cmudict', 'nltk.corpus.reader.conll', 'nltk.corpus.reader.chunked', 'nltk.corpus.reader.ppattach', 'nltk.corpus.reader.senseval', 'nltk.corpus.reader.ieer', 'nltk.corpus.reader.sinica_treebank', 'nltk.corpus.reader.indian', 'nltk.corpus.reader.toolbox', 'nltk.corpus.reader.timit', 'nltk.corpus.reader.ycoe', 'nltk.corpus.reader.rte', 'nltk.corpus.reader.string_category', 'nltk.corpus.reader.propbank', 'nltk.corpus.reader.verbnet', 'nltk.corpus.reader.bnc', 'nltk.corpus.reader.nps_chat', 'nltk.corpus.reader.switchboard', 'nltk.corpus.reader.dependency', 'nltk.corpus.reader.nombank', 'nltk.corpus.reader.ipipan', 'nltk.corpus.reader.pl196x', 'nltk.corpus.reader.knbc', 'nltk.corpus.reader.chasen', 'nltk.corpus.reader.childes', 'nltk.corpus.reader.aligned', 'nltk.corpus.reader.lin', 'nltk.corpus.reader.semcor', 'nltk.corpus.reader.framenet', 'nltk.corpus.reader.udhr', 'nltk.corpus.reader.sentiwordnet', 'nltk.corpus.reader.twitter', 'nltk.corpus.reader.nkjp', 'nltk.corpus.reader.crubadan', 'nltk.corpus.reader.mte', 'nltk.corpus.reader.reviews', 'nltk.corpus.reader.opinion_lexicon', 'nltk.corpus.reader.pros_cons', 'nltk.corpus.reader.categorized_sents', 'nltk.corpus.reader.comparative_sents', 'nltk.corpus.reader.panlex_lite', 'nltk.corpus.reader.panlex_swadesh', 'nltk.corpus.reader.bcp47', 'nltk.help', 'nltk.corpus.europarl_raw'], ['xml.etree', 'xml.etree.ElementTree', 'xml.sax.saxutils', 'xml.dom.minidom', 'xml.parsers', 'xml.parsers.expat', 'xml.etree.cElementTree'], ['tkinter.messagebox', 'tkinter.ttk', 'tkinter.scrolledtext', 'tkinter.colorchooser', 'tkinter.commondialog', 'tkinter.dialog', 'tkinter.constants', 'tkinter.dnd', 'tkinter.filedialog', 'tkinter.font', 'tkinter.tix', 'tkinter.simpledialog'], ['referencing._core', 'referencing._attrs', 'referencing.typing', 'referencing.jsonschema', 'referencing.exceptions', 'referencing.retrieval'], ['contracts.utils', 'contracts.interface', 'contracts.main', 'contracts.library.extensions', 'contracts.library.types_misc', 'contracts.inspection', 'contracts.library.simple_values', 'contracts.syntax', 'contracts.test_registrar', 'contracts.library', 'contracts.library.array'], ['ctypes.util', 'ctypes.wintypes'], ['cffi._shimmed_dist_utils', 'cffi.api'], ['setuptools.command.build_ext', 'setuptools.command.build_py', 'setuptools.wheel', 'setuptools.errors', 'setuptools.sandbox', 'setuptools._path', 'setuptools.archive_util', 'setuptools.command.egg_info', 'setuptools.command', 'setuptools.dist', 'setuptools._importlib', 'setuptools.config', 'setuptools.discovery', 'setuptools.warnings', 'setuptools.logging', 'setuptools._core_metadata', 'setuptools.tests.textwrap', 'setuptools.command.build_clib', 'setuptools.command.build', 'setuptools.command.install_scripts', 'setuptools.extension', 'setuptools.glob', 'setuptools.command.sdist', 'setuptools.command.easy_install', 'setuptools.package_index', 'setuptools.depends', 'setuptools.tests', 'setuptools.command.bdist_wheel', 'setuptools.command.develop', 'setuptools.command.editable_wheel', 'setuptools._normalization', 'setuptools.tests.server', 'setuptools.command.setopt', 'setuptools.unicode_utils', 'setuptools.config.pyprojecttoml', 'setuptools._static', 'setuptools.config.setupcfg', 'setuptools.config._apply_pyprojecttoml', 'setuptools.compat.py310', 'setuptools._vendor.packaging'], ['pycparser.lextab', 'pycparser.yacctab'], ['markdown_it._compat', 'markdown_it.token', 'markdown_it.main', 'markdown_it.rules_inline', 'markdown_it.rules_block', 'markdown_it.common.utils', 'markdown_it.renderer', 'markdown_it.utils', 'markdown_it.rules_core', 'markdown_it.helpers'], ['common.utils', 'common.schemas', 'common.const', 'common.exceptions', 'common.html_blocks', 'common.html_re', 'common.entities'], ['fontTools.misc.cliTools', 'fontTools.misc.loggingTools', 'fontTools.misc.macCreatorType', 'fontTools.misc.textTools', 'fontTools.misc.timeTools', 'fontTools.ttLib', 'fontTools.unicode', 'fontTools.ttLib.sfnt', 'fontTools.colorLib.builder', 'fontTools.misc.sstruct', 'fontTools.cu2qu', 'fontTools.misc', 'fontTools.misc.bezierTools', 'fontTools.cu2qu.benchmark', 'fontTools.pens.qu2cuPen', 'fontTools.pens.ttGlyphPen', 'fontTools.misc.roundTools', 'fontTools.misc.vector', 'fontTools.misc.fixedTools', 'fontTools.pens.basePen', 'fontTools.encodings.StandardEncoding', 'fontTools.encodings.codecs', 'fontTools.config', 'fontTools.misc.xmlWriter', 'fontTools.pens.boundsPen', 'fontTools.ttLib.tables.DefaultTable', 'fontTools.misc.arrayTools', 'fontTools.misc.transform', 'fontTools.pens.pointPen', 'fontTools.pens.reverseContourPen', 'fontTools.subset.util', 'fontTools.otlLib.maxContextCalc', 'fontTools.subset.cff', 'fontTools.subset.svg', 'fontTools.ttLib.tables', 'fontTools.ttLib.tables._n_a_m_e', 'fontTools.ttLib.tables.otBase', 'fontTools.varLib', 'fontTools.colorLib.unbuilder', 'fontTools.ttLib.tables.S_V_G_', 'fontTools.subset', 'fontTools.voltLib.ast', 'fontTools.voltLib.error', 'fontTools.voltLib.lexer', 'fontTools.feaLib', 'fontTools.voltLib', 'fontTools.voltLib.parser', 'fontTools.merge.unicode', 'fontTools.pens.recordingPen', 'fontTools.merge.base', 'fontTools.merge.util', 'fontTools.merge.tables', 'fontTools.merge.cmap', 'fontTools.merge.layout', 'fontTools.merge.options', 'fontTools.misc.psCharStrings', 'fontTools.merge', 'fontTools.misc.configTools', 'fontTools.designspaceLib', 'fontTools.varLib.models', 'fontTools.cffLib', 'fontTools.cffLib.specializer', 'fontTools.pens.t2CharStringPen', 'fontTools.cffLib.CFFToCFF2', 'fontTools.varLib.varStore', 'fontTools.designspaceLib.split', 'fontTools.ttLib.tables._f_v_a_r', 'fontTools.ttLib.tables._g_l_y_f', 'fontTools.ttLib.tables.ttProgram', 'fontTools.ttLib.tables.TupleVariation', 'fontTools.varLib.featureVars', 'fontTools.varLib.iup', 'fontTools.varLib.merger', 'fontTools.varLib.mvar', 'fontTools.varLib.stat', 'fontTools.ttx', 'fontTools.pens.cairoPen', 'fontTools.ttLib.ttGlyphSet', 'fontTools.varLib.interpolatableHelpers', 'fontTools.otlLib.builder', 'fontTools.designspaceLib.types', 'fontTools.misc.intTools', 'fontTools.varLib.builder', 'fontTools.pens.momentsPen', 'fontTools.pens.statisticsPen', 'fontTools.pens.transformPen', 'fontTools.ufoLib', 'fontTools.pens.areaPen', 'fontTools.misc.dictTools', 'fontTools.ttLib.ttVisitor', 'fontTools.misc.treeTools', 'fontTools.otlLib.optimize.gpos', 'fontTools.ttLib.tables.otConverters', 'fontTools.ttLib.tables.otTraverse', 'fontTools.misc.iterTools', 'fontTools.feaLib.lookupDebugInfo', 'fontTools.otlLib', 'fontTools.ttLib.tables._c_m_a_p', 'fontTools.misc.testTools', 'fontTools.mtiLib', 'fontTools.pens.filterPen', 'fontTools.qu2cu', 'fontTools.misc.symfont', 'fontTools.feaLib.ast', 'fontTools.otlLib.error', 'fontTools.ufoLib.utils', 'fontTools.ufoLib.converters', 'fontTools.ufoLib.errors', 'fontTools.ufoLib.filenames', 'fontTools.ufoLib.validators', 'fontTools.ufoLib.glifLib', 'fontTools.misc.plistlib', 'fontTools.misc.etree', 'fontTools.designspaceLib.statNames', 'fontTools.feaLib.error', 'fontTools.feaLib.parser', 'fontTools.feaLib.variableScalar', 'fontTools.feaLib.lexer', 'fontTools.misc.encodingTools', 'fontTools.feaLib.location', 'fontTools.feaLib.builder', 'fontTools.ttLib.woff2', 'fontTools.misc.macRes', 'fontTools.ttLib.ttFont', 'fontTools.ttLib.tables.otTables', 'fontTools.varLib.multiVarStore', 'fontTools.ttLib.ttCollection', 'fontTools.misc.visitor', 'fontTools.misc.psOperators', 'fontTools.misc.lazyTools', 'fontTools.ttLib.standardGlyphOrder', 'fontTools.misc.filenames', 'fontTools.ttLib.tables.C_F_F_', 'fontTools.otlLib.optimize', 'fontTools.varLib.instancer', 'fontTools.cffLib.CFF2ToCFF', 'fontTools.ttLib.removeOverlaps'], ['ttLib.tables._c_m_a_p', 'ttLib.tables._g_l_y_f', 'ttLib.tables.O_S_2f_2', 'ttLib.tables._f_v_a_r'], ['altair.vegalite.v5.schema._config', 'altair.vegalite.v5.theme', 'altair.utils.plugin_registry', 'altair.vegalite', 'altair.expr', 'altair.jupyter', 'altair.utils', 'altair.vegalite.v5.schema.core', 'altair._magics', 'altair.utils.deprecation', 'altair.expr.core', 'altair.vegalite.v5.schema._typing', 'altair.utils._vegafusion_data', 'altair.vegalite.v5.data', 'altair.typing', 'altair.utils._importers', 'altair.utils.schemapi', 'altair.utils._dfi_types', 'altair.utils.data', 'altair.vegalite.v5.schema', 'altair.utils.core', 'altair.vegalite.data', 'altair.vegalite.v5.display', 'altair.utils.display', 'altair.vegalite.v5.api', 'altair.vegalite.v5.schema.channels', 'altair.utils.selection', 'altair.utils.compiler', 'altair.vegalite.v5', 'altair.vegalite.v5.compiler', 'altair.utils.mimebundle', 'altair.vegalite.display', 'altair.utils._show', 'altair.utils.save', 'altair.utils.server', 'altair.utils._transformed_data'], ['IPython.core', 'IPython.core.getipython', 'IPython.display', 'IPython.lib.pretty', 'IPython.html.widgets', 'IPython.core.formatters', 'IPython.core.pylabtools', 'IPython.core.inputtransformer2', 'IPython.core.interactiveshell', 'IPython.core.completer', 'IPython.core.display'], ['narwhals.stable.v1.dependencies', 'narwhals._expression_parsing', 'narwhals.dependencies', 'narwhals.exceptions', 'narwhals.expr', 'narwhals.series', 'narwhals.translate', 'narwhals.utils', 'narwhals._compliant', 'narwhals._translate', 'narwhals.dataframe', 'narwhals.dtypes', 'narwhals.schema', 'narwhals.typing', 'narwhals._namespace', 'narwhals._polars.namespace', 'narwhals._pandas_like.dataframe', 'narwhals._pandas_like.series', 'narwhals._dask.namespace', 'narwhals._ibis.dataframe', 'narwhals._interchange.dataframe', 'narwhals._compliant.typing', 'narwhals.group_by', 'narwhals.functions', 'narwhals.series_cat', 'narwhals.series_dt', 'narwhals.series_list', 'narwhals.series_str', 'narwhals.series_struct', 'narwhals._arrow.typing', 'narwhals._arrow.utils', 'narwhals._arrow.namespace', 'narwhals._duckdb.namespace', 'narwhals._pandas_like.namespace', 'narwhals._spark_like.dataframe', 'narwhals._spark_like.namespace', 'narwhals.stable.v1', 'narwhals.stable.v1._namespace', 'narwhals._pandas_like.utils', 'narwhals._interchange.series', 'narwhals._polars.utils', 'narwhals.expr_cat', 'narwhals.expr_dt', 'narwhals.expr_list', 'narwhals.expr_name', 'narwhals.expr_str', 'narwhals.expr_struct', 'narwhals.stable', 'narwhals._polars.series', 'narwhals._polars.group_by', 'narwhals._duckdb.dataframe', 'narwhals._dask.dataframe', 'narwhals._arrow.dataframe', 'narwhals._polars.dataframe', 'narwhals._polars.expr', 'narwhals._polars.typing', 'narwhals._dask.expr', 'narwhals._dask.utils', 'narwhals._dask.group_by', 'narwhals._compliant.group_by', 'narwhals._compliant.namespace', 'narwhals._dask.selectors', 'narwhals._compliant.expr', 'narwhals._dask.expr_dt', 'narwhals._dask.expr_str', 'narwhals._pandas_like.group_by', 'narwhals._arrow.series', 'narwhals._arrow.expr', 'narwhals._arrow.group_by', 'narwhals._arrow.series_cat', 'narwhals._arrow.series_dt', 'narwhals._arrow.series_list', 'narwhals._arrow.series_str', 'narwhals._arrow.series_struct', 'narwhals._compliant.series', 'narwhals._arrow.selectors', 'narwhals._compliant.any_namespace', 'narwhals._compliant.dataframe', 'narwhals._compliant.selectors', 'narwhals._compliant.when_then', 'narwhals._duckdb.utils', 'narwhals._duckdb.expr', 'narwhals._duckdb.group_by', 'narwhals._duckdb.series', 'narwhals._duckdb.selectors', 'narwhals._duckdb.expr_dt', 'narwhals._duckdb.expr_list', 'narwhals._duckdb.expr_str', 'narwhals._duckdb.expr_struct', 'narwhals._duckdb.typing', 'narwhals._ibis.series', 'narwhals._pandas_like.expr', 'narwhals._pandas_like.series_cat', 'narwhals._pandas_like.series_dt', 'narwhals._pandas_like.series_list', 'narwhals._pandas_like.series_str', 'narwhals._pandas_like.series_struct', 'narwhals._pandas_like.selectors', 'narwhals._pandas_like.typing', 'narwhals._spark_like.expr', 'narwhals._spark_like.utils', 'narwhals._spark_like.group_by', 'narwhals._spark_like.selectors', 'narwhals._spark_like.expr_dt', 'narwhals._spark_like.expr_list', 'narwhals._spark_like.expr_str', 'narwhals._spark_like.expr_struct', 'narwhals._spark_like.typing', 'narwhals.stable.v1.dtypes', 'narwhals.selectors', 'narwhals.stable.v1._dtypes', 'narwhals.stable.v1.typing', 'narwhals._typing_compat', 'narwhals._utils', 'narwhals._ibis.namespace', 'narwhals._enum', 'narwhals._duration', 'narwhals._compliant.window', 'narwhals._ibis.utils', 'narwhals._ibis.expr', 'narwhals._ibis.group_by', 'narwhals._ibis.selectors', 'narwhals._ibis.expr_dt', 'narwhals._ibis.expr_list', 'narwhals._ibis.expr_str', 'narwhals._ibis.expr_struct'], ['tenacity.asyncio', 'tenacity.tornadoweb', 'tenacity.stop', 'tenacity.wait'], ['tornado.concurrent', 'tornado.escape', 'tornado.log', 'tornado.util', 'tornado.httputil', 'tornado.web', 'tornado.ioloop', 'tornado.httpserver', 'tornado.routing', 'tornado.locks', 'tornado.simple_httpclient', 'tornado.options', 'tornado.netutil', 'tornado.speedups', 'tornado.http1connection', 'tornado.tcpserver', 'tornado.platform.asyncio', 'tornado.gen', 'tornado.process', 'tornado.iostream', 'tornado._locale_data', 'tornado.httpclient', 'tornado.autoreload', 'tornado.tcpclient', 'tornado.queues', 'tornado.websocket', 'tornado.template', 'tornado.test.util', 'tornado.testing', 'tornado.auth', 'tornado.platform.twisted', 'tornado.wsgi', 'tornado.test', 'tornado.curl_httpclient', 'tornado.test.httpclient_test', 'tornado.platform.caresresolver', 'tornado.locale', 'tornado.test.runtests'], ['libcst._nodes.base', 'libcst.metadata.base_provider', 'libcst.metadata.wrapper', 'libcst._flatten_sentinel', 'libcst._maybe_sentinel', 'libcst._removal_sentinel', 'libcst._typed_visitor_base', 'libcst._nodes.expression', 'libcst._nodes.module', 'libcst._nodes.op', 'libcst._nodes.statement', 'libcst._nodes.whitespace', 'libcst._metadata_dependent', 'libcst._typed_visitor', 'libcst._visitors', 'libcst._batched_visitor', 'libcst._exceptions', 'libcst._parser.entrypoints', 'libcst._parser.types.config', 'libcst._version', 'libcst.helpers', 'libcst._types', 'libcst._add_slots', 'libcst._parser.parso.pgen2.generator', 'libcst._parser.parso.python.token', 'libcst._parser.types.token', 'libcst._tabs', 'libcst._parser.parso.utils', 'libcst.codemod', 'libcst.display', 'libcst.display.text', 'libcst.metadata', 'libcst.matchers', 'libcst._nodes.internal', 'libcst._type_enforce', 'libcst._nodes.deep_equals', 'libcst.metadata.type_inference_provider', 'libcst.testing.utils', 'libcst.codemod.visitors', 'libcst.codegen.gather', 'libcst.codegen.gen_visitor_functions', 'libcst.codegen.gen_matcher_classes', 'libcst.codegen.gen_type_mapping', 'libcst.codegen.transforms', 'libcst._parser.wrapped_tokenize', 'libcst._parser.parso.python.tokenize', 'libcst._parser.types.whitespace_state', 'libcst._parser.conversions.expression', 'libcst._parser.conversions.module', 'libcst._parser.conversions.params', 'libcst._parser.conversions.statement', 'libcst._parser.conversions.terminals', 'libcst._parser.production_decorator', 'libcst._parser.types.conversions', 'libcst._parser.types.production', 'libcst._parser.base_parser', 'libcst._parser.grammar', 'libcst._parser', 'libcst._parser.detect_config', 'libcst._parser.python_parser', 'libcst.native', 'libcst.display.graphviz', 'libcst.matchers._decorators', 'libcst.matchers._matcher_base', 'libcst.matchers._visitors', 'libcst.matchers._return_types', 'libcst._position', 'libcst.metadata.position_provider', 'libcst.metadata.accessor_provider', 'libcst.metadata.expression_context_provider', 'libcst.metadata.file_path_provider', 'libcst.metadata.full_repo_manager', 'libcst.metadata.name_provider', 'libcst.metadata.parent_node_provider', 'libcst.metadata.reentrant_codegen', 'libcst.metadata.scope_provider', 'libcst.metadata.span_provider', 'libcst.helpers.module', 'libcst.helpers._template', 'libcst.helpers.common', 'libcst.helpers.expression', 'libcst.helpers.node_fields', 'libcst.codemod._context', 'libcst.codemod._codemod', 'libcst.codemod._visitor', 'libcst.codemod.visitors._add_imports', 'libcst.codemod.visitors._remove_imports', 'libcst.codemod._dummy_pool', 'libcst.codemod._runner', 'libcst.codemod._cli', 'libcst.codemod._command', 'libcst.codemod._testing', 'libcst.codemod.visitors._gather_exports', 'libcst.codemod.visitors._gather_string_annotation_names', 'libcst.codemod.visitors._apply_type_annotations', 'libcst.codemod.visitors._gather_comments', 'libcst.codemod.visitors._gather_global_names', 'libcst.codemod.visitors._gather_imports', 'libcst.codemod.visitors._gather_unused_imports', 'libcst.codemod.visitors._imports', 'libcst.codemod.commands.rename', 'libcst.codemod.commands.unnecessary_format_string', 'libcst.codemod.commands.fix_pyre_directives', 'libcst.codemod.commands.remove_unused_imports', 'libcst.codemod.commands.convert_type_comments', 'libcst.codemod.commands.convert_format_to_fstring', 'libcst.codemod.commands.convert_union_to_or', 'libcst.codemod.commands.rename_typing_generic_aliases', 'libcst.codemod.commands.convert_percent_format_to_fstring', 'libcst.codemod.commands.ensure_import_present', 'libcst.codemod.commands.noop', 'libcst.codemod.commands.convert_namedtuple_to_dataclass', 'libcst.codemod.commands.fix_variadic_callable', 'libcst.codemod.commands.add_trailing_commas', 'libcst.codemod.commands.strip_strings_from_types', 'libcst.codemod.commands.remove_pyre_directive', 'libcst.codemod.commands.add_pyre_directive', 'libcst.helpers.paths', 'libcst.metadata.tests.test_type_inference_provider', 'libcst.tests.test_pyre_integration', 'libcst.tool', 'libcst._parser.types', 'libcst._parser.types.py_token', 'libcst._parser.custom_itertools', 'libcst._parser.types.partials', 'libcst._parser.whitespace_parser', 'libcst._parser.parso.python.py_token', 'libcst._parser.parso.pgen2.grammar_parser', 'libcst.codegen.generate', 'libcst._nodes.tests.base'], ['pytz.tzinfo', 'pytz.exceptions', 'pytz.lazy', 'pytz.tzfile'], ['isort.settings', 'isort.utils', 'isort.exceptions', 'isort.literal', 'isort.comments', 'isort.parse', 'isort.main', 'isort.format', 'isort.api'], ['deprecated.finders', 'deprecated.class_validators', 'deprecated.config', 'deprecated.tools', 'deprecated.parse', 'deprecated.json'], ['regex._regex', 'regex._regex_core'], ['bandit.core', 'bandit.core.constants', 'bandit.core.issue', 'bandit.core.test_properties', 'bandit.cli', 'bandit.core.utils', 'bandit.plugins', 'bandit.formatters', 'bandit.blacklists'], ['numpy._core', 'numpy._core._multiarray_tests', 'numpy._core.tests._natype', 'numpy.testing._private.utils', 'numpy.linalg', 'numpy.fft', 'numpy.dtypes', 'numpy.random', 'numpy.polynomial', 'numpy.ma', 'numpy.ctypeslib', 'numpy.exceptions', 'numpy.testing', 'numpy.matlib', 'numpy.f2py', 'numpy.typing', 'numpy.rec', 'numpy.char', 'numpy.core', 'numpy.strings', 'numpy.distutils', 'numpy.__config__', 'numpy._pytesttester', 'numpy._core._multiarray_umath', 'numpy._core._internal', 'numpy._core.multiarray', 'numpy.matrixlib.defmatrix', 'numpy.lib.stride_tricks', 'numpy.lib.array_utils', 'numpy.ma.testutils', 'numpy._core.strings', 'numpy._core.defchararray', 'numpy.version', 'numpy._globals', 'numpy._typing', 'numpy._utils', 'numpy.lib._twodim_base_impl', 'numpy.lib._function_base_impl', 'numpy.lib._index_tricks_impl', 'numpy._core.umath', 'numpy._core.numerictypes', 'numpy._core.numeric', 'numpy._utils._inspect', 'numpy._core.fromnumeric', 'numpy._core.function_base', 'numpy._core.overrides', 'numpy.lib._stride_tricks_impl', 'numpy.lib', 'numpy._core.records', 'numpy._typing._add_docstring', 'numpy.f2py._backends', 'numpy.distutils.system_info', 'numpy.distutils.fcompiler', 'numpy.distutils.cpuinfo', 'numpy.f2py.f2py2e', 'numpy.lib._type_check_impl', 'numpy.ma.mrecords', 'numpy.lib._iotools', 'numpy.lib._utils_impl', 'numpy._core.shape_base', 'numpy.matrixlib', 'numpy.lib._histograms_impl', 'numpy.polynomial.chebyshev', 'numpy.polynomial.polynomial', 'numpy.polynomial.hermite_e', 'numpy.polynomial.polyutils', 'numpy.polynomial.laguerre', 'numpy.polynomial.legendre', 'numpy.polynomial.hermite', 'numpy.linalg.tests.test_linalg', 'numpy.ma.core', 'numpy.ma.extras', 'numpy.random._common', 'numpy.random.bit_generator', 'numpy.random._examples.numba', 'numpy.random._examples.cffi', 'numpy.lib._npyio_impl', 'numpy.lib._arraypad_impl', 'numpy.lib._datasource', 'numpy._core._rational_tests', 'numpy.lib.recfunctions', 'numpy.lib._nanfunctions_impl', 'numpy.linalg._umath_linalg', 'numpy.distutils.misc_util', 'numpy.distutils.core', 'numpy.f2py.tests', 'numpy.f2py._src_pyf', 'numpy.f2py.crackfortran', 'numpy.f2py.auxfuncs', 'numpy._core._type_aliases', 'numpy.f2py.symbolic', 'numpy.f2py._backends._meson', 'numpy.typing.mypy_plugin', 'numpy.lib.user_array', 'numpy._core._machar', 'numpy._core.arrayprint', 'numpy._core.tests._locales', 'numpy._core._struct_ufunc_tests', 'numpy.lib._shape_base_impl', 'numpy.lib.tests.test_io', 'numpy._core._simd', 'numpy._core._operand_flag_tests', 'numpy._core._umath_tests', 'numpy._core.getlimits', 'numpy.testing.overrides', 'numpy.lib.mixins', 'numpy.linalg.lapack_lite', 'numpy.linalg._linalg', 'numpy.core.multiarray'], ['lib._utils_impl', 'lib._arraypad_impl', 'lib._arraysetops_impl', 'lib._function_base_impl', 'lib._histograms_impl', 'lib._index_tricks_impl', 'lib._nanfunctions_impl', 'lib._npyio_impl', 'lib._polynomial_impl', 'lib._shape_base_impl', 'lib._stride_tricks_impl', 'lib._twodim_base_impl', 'lib._type_check_impl', 'lib._ufunclike_impl'], ['encodings.aliases', 'encodings.idna', 'encodings.raw_unicode_escape', 'encodings.unicode_escape'], ['charset_normalizer.cd', 'charset_normalizer.md', 'charset_normalizer.models', 'charset_normalizer.version'], ['PyQt6.QtCore', 'PyQt6.QtGui'], ['PySide6.QtCore', 'PySide6.QtGui'], ['http.cookies', 'http.client', 'http.cookiejar', 'http.server'], ['urllib3.exceptions', 'urllib3.fields', 'urllib3.filepost', 'urllib3.util', 'urllib3.contrib', 'urllib3.poolmanager', 'urllib3.util.retry', 'urllib3.util.ssl_', 'urllib3.contrib.socks', 'urllib3.connection'], ['safety.formatters.bare', 'safety.formatters.html', 'safety.formatters.json', 'safety.formatters.screen', 'safety.formatters.text', 'safety.constants', 'safety.errors', 'safety.events.event_bus', 'safety.models', 'safety.auth.models', 'safety.auth.utils', 'safety.cli_util', 'safety.output_utils', 'safety.events.utils', 'safety.meta', 'safety.util', 'safety.asyncio_patch', 'safety.alerts', 'safety.auth', 'safety.auth.cli', 'safety.console', 'safety.decorators', 'safety.error_handlers', 'safety.firewall.command', 'safety.formatter', 'safety.init.command', 'safety.scan.command', 'safety.scan.constants', 'safety.scan.finder', 'safety.scan.main', 'safety.tool', 'safety.scan.models', 'safety.auth.constants', 'safety.auth.cli_utils', 'safety.cli', 'safety.events.utils.emission', 'safety.init.constants', 'safety.init.main', 'safety.init.models', 'safety.scan.init_scan', 'safety.init.render', 'safety.scan.render', 'safety.scan.util', 'safety.auth.main', 'safety.auth.server', 'safety.safety', 'safety.formatters.schemas.zero_five', 'safety.events.handlers', 'safety.scan.decorators', 'safety.scan.finder.file_finder', 'safety.scan.finder.handlers', 'safety.scan.fun_mode.easter_eggs', 'safety.scan.validators', 'safety.tool.pip', 'safety.tool.poetry', 'safety.tool.uv.main', 'safety.tool.utils', 'safety.tool.constants', 'safety.tool.typosquatting', 'safety.tool.resolver', 'safety.tool.intents', 'safety.tool.pip.parser', 'safety.tool.poetry.parser', 'safety.init.types', 'safety.events.utils.context', 'safety.events.types', 'safety.firewall.events.utils', 'safety.scan.fun_mode.celebration_effects', 'safety.tool.decorators', 'safety.tool.auth'], ['ruamel.yaml', 'ruamel.yaml.error', 'ruamel.yaml.anchor', 'ruamel.yaml.compat', 'ruamel.yaml.docinfo', 'ruamel.yaml.tokens', 'ruamel.yaml.comments', 'ruamel.yaml.nodes', 'ruamel.yaml.scalarbool', 'ruamel.yaml.scalarfloat', 'ruamel.yaml.scalarint', 'ruamel.yaml.scalarstring', 'ruamel.yaml.tag', 'ruamel.yaml.timestamp', 'ruamel.yaml.util', 'ruamel.yaml.serializer', 'ruamel.yaml.events', 'ruamel.yaml.main', 'ruamel.yaml.emitter', 'ruamel.yaml.representer', 'ruamel.yaml.resolver', 'ruamel.yaml.constructor', 'ruamel.yaml.scanner', 'ruamel.yaml.composer', 'ruamel.yaml.parser', 'ruamel.yaml.reader', 'ruamel.yaml.dumper', 'ruamel.yaml.loader'], ['safety_schemas.models', 'safety_schemas.config.schemas.v3_0', 'safety_schemas.models.events.types', 'safety_schemas.models.events.constants', 'safety_schemas.models.events.payloads', 'safety_schemas.models.events', 'safety_schemas.models.events.context', 'safety_schemas.models.package'], ['scan.main', 'scan.util'], ['pydantic.json', 'pydantic._internal', 'pydantic.version', 'pydantic.warnings', 'pydantic.errors', 'pydantic.main', 'pydantic.color', 'pydantic.types', 'pydantic.v1.utils', 'pydantic.v1.errors', 'pydantic.v1.typing', 'pydantic.v1.version', 'pydantic.v1.fields', 'pydantic.v1.main', 'pydantic.v1', 'pydantic.v1.class_validators', 'pydantic.v1.error_wrappers', 'pydantic.v1.types', 'pydantic.v1.validators', 'pydantic.v1.config', 'pydantic.v1.schema', 'pydantic.v1.json', 'pydantic.v1.datetime_parse', 'pydantic.v1.annotated_types', 'pydantic.v1.dataclasses', 'pydantic.v1.parse', 'pydantic.v1.decorator', 'pydantic.v1.env_settings', 'pydantic.v1.networks', 'pydantic.v1.tools', 'pydantic.v1.color', 'pydantic.fields', 'pydantic._internal._serializers', 'pydantic._internal._internal_dataclass', 'pydantic.dataclasses', 'pydantic.validators'], ['dparse.parser', 'dparse.updater', 'dparse.dependencies'], ['mando.core', 'mando.napoleon', 'mando.utils', 'mando.napoleon.docstring', 'mando.napoleon.iterators', 'mando.napoleon.pycompat'], ['_core._eventloop', '_core._exceptions', '_core._subprocesses', '_core._synchronization', '_core._tasks', '_core._fileio', '_core._resources', '_core._signals', '_core._sockets', '_core._streams', '_core._tempfile', '_core._testing', '_core._typedattr'], ['streams.buffered', 'streams.memory', 'streams.stapled', 'streams.tls'], ['concurrent.futures', 'concurrent.futures.thread', 'concurrent.futures._base', 'concurrent.futures.process'], ['abc._tasks', 'abc._eventloop'], ['pip._internal.utils.entrypoints', 'pip._internal.cli.main', 'pip._internal.models.direct_url', 'pip._internal.utils.egg_link', 'pip._vendor.packaging.version', 'pip._internal.operations.freeze', 'pip._internal.utils.urls', 'pip._internal.exceptions', 'pip._internal.utils', 'pip._internal.utils.compat', 'pip._internal.utils.logging', 'pip._internal.utils.misc', 'pip._vendor', 'pip._internal.utils.packaging', 'pip._vendor.packaging.requirements', 'pip._internal.models.link', 'pip._internal.models.wheel', 'pip._internal.utils.temp_dir', 'pip._vendor.packaging.tags', 'pip._vendor.packaging.utils', 'pip._vendor.rich.console', 'pip._vendor.rich.markup', 'pip._vendor.rich.text', 'pip._internal.metadata', 'pip._internal.req.req_install', 'pip._vendor.requests.models', 'pip._internal.utils.hashes', 'pip._internal.utils._log', 'pip._internal.cache', 'pip._internal.operations.build.wheel', 'pip._internal.operations.build.wheel_editable', 'pip._internal.operations.build.wheel_legacy', 'pip._internal.utils.setuptools_build', 'pip._internal.utils.subprocess', 'pip._internal.vcs', 'pip._internal.index.collector', 'pip._internal.index.package_finder', 'pip._internal.models.selection_prefs', 'pip._internal.network.session', 'pip._internal.utils.filesystem', 'pip._internal.cli.spinners', 'pip._internal.locations', 'pip._vendor.urllib3.util.ssl_', 'pip._vendor.typing_extensions', 'pip._vendor.pygments.styles', 'pip._vendor.pygments.util', 'pip._vendor.pygments.token', 'pip._vendor.pygments.lexers', 'pip._vendor.pygments.lexers._mapping', 'pip._vendor.pygments.formatters', 'pip._vendor.pygments.filters', 'pip._vendor.pygments', 'pip._vendor.pygments.formatters.latex', 'pip._vendor.pygments.formatters.terminal', 'pip._vendor.pygments.formatters.terminal256', 'pip._vendor.pygments.lexers.special', 'pip._vendor.pygments.lexer', 'pip._vendor.pygments.formatter', 'pip._vendor.pygments.filter', 'pip._vendor.pygments.regexopt', 'pip._vendor.pygments.cmdline', 'pip._vendor.requests.structures', 'pip._vendor.urllib3', 'pip._vendor.requests', 'pip._vendor.cachecontrol.adapter', 'pip._vendor.cachecontrol.cache', 'pip._vendor.cachecontrol.controller', 'pip._vendor.cachecontrol.heuristics', 'pip._vendor.cachecontrol.serialize', 'pip._vendor.cachecontrol.filewrapper', 'pip._vendor.requests.adapters', 'pip._vendor.cachecontrol.wrapper', 'pip._vendor.urllib3.exceptions', 'pip._vendor.urllib3.fields', 'pip._vendor.urllib3.filepost', 'pip._vendor.urllib3.util', 'pip._vendor.certifi', 'pip._vendor.urllib3.contrib', 'pip._vendor.urllib3.poolmanager', 'pip._vendor.urllib3.util.retry', 'pip._vendor.urllib3.contrib.socks', 'pip._vendor.rich._null_file', 'pip._vendor.rich.markdown', 'pip._vendor.rich.panel', 'pip._vendor.rich.syntax', 'pip._vendor.rich.table', 'pip._vendor.rich.json', 'pip._vendor.rich._win32_console', 'pip._vendor.rich._windows_renderer', 'pip._vendor.rich.cells', 'pip._vendor.rich.columns', 'pip._vendor.rich.highlighter', 'pip._vendor.rich.tree', 'pip._vendor.rich.styled', 'pip._vendor.rich', 'pip._vendor.rich._inspect', 'pip._vendor.rich.color', 'pip._vendor.rich.style', 'pip._vendor.rich.segment', 'pip._vendor.rich.repr', 'pip._vendor.rich.pretty', 'pip._vendor.pygments.style', 'pip._vendor.rich.containers', 'pip._vendor.rich.padding', 'pip._vendor.rich.measure', 'pip._vendor.rich.traceback', 'pip._internal.utils._jaraco_text', 'pip._vendor.packaging', 'pip._vendor.platformdirs', 'pip._vendor.platformdirs.windows', 'pip._vendor.platformdirs.macos', 'pip._vendor.platformdirs.unix', 'pip._vendor.platformdirs.android', 'pip._vendor.cachecontrol.caches.file_cache', 'pip._vendor.cachecontrol.caches.redis_cache', 'pip._vendor.pygments.plugin', 'pip._vendor.pygments.modeline', 'pip._vendor.pygments.console', 'pip._vendor.pygments.formatters._mapping', 'pip._vendor.pygments.styles._mapping', 'pip._vendor.packaging.licenses._spdx', 'pip._internal.vcs.versioncontrol', 'pip._vendor.requests.auth', 'pip._vendor.requests.utils', 'pip._internal.network.utils', 'pip._internal.cli.progress_bars', 'pip._internal.models.index', 'pip._internal.network.cache', 'pip._internal.network.auth', 'pip._internal.utils.glibc', 'pip._vendor.cachecontrol', 'pip._vendor.urllib3.connectionpool', 'pip._vendor.cachecontrol.caches', 'pip._internal.utils.deprecation', 'pip._vendor.rich.logging', 'pip._internal.utils.retry', 'pip._internal.utils.virtualenv', 'pip._vendor.pyproject_hooks', 'pip._internal.utils.filetypes', 'pip._internal.models.format_control', 'pip._internal.utils.compatibility_tags', 'pip._vendor.packaging.markers', 'pip._internal.cli.parser', 'pip._internal.models.target_python', 'pip._internal.cli.base_command', 'pip._internal.cli.command_context', 'pip._internal.self_outdated_check', 'pip._internal.cli.status_codes', 'pip._internal.configuration', 'pip._internal.cli.main_parser', 'pip._internal.commands', 'pip._internal.cli', 'pip._internal.build_env', 'pip._vendor.rich.progress', 'pip._internal.cli.autocompletion', 'pip._internal.resolution.resolvelib.resolver', 'pip._internal.resolution.legacy.resolver', 'pip._internal.cli.index_command', 'pip._internal.operations.build.build_tracker', 'pip._internal.operations.prepare', 'pip._internal.req.constructors', 'pip._internal.req.req_file', 'pip._internal.resolution.base', 'pip._internal.distributions', 'pip._internal.metadata.base', 'pip._internal.utils.direct_url_helpers', 'pip._internal.distributions.installed', 'pip._internal.network.download', 'pip._internal.network.lazy_wheel', 'pip._internal.utils.unpacking', 'pip._internal.operations.build.metadata', 'pip._internal.operations.build.metadata_editable', 'pip._internal.operations.build.metadata_legacy', 'pip._internal.operations.install.editable_legacy', 'pip._internal.operations.install.wheel', 'pip._internal.pyproject', 'pip._internal.req.req_uninstall', 'pip._vendor.packaging.specifiers', 'pip._internal.models.search_scope', 'pip._internal.req.req_set', 'pip._internal.vcs.bazaar', 'pip._internal.vcs.git', 'pip._internal.vcs.mercurial', 'pip._internal.vcs.subversion', 'pip._internal.models.scheme', 'pip._vendor.requests.exceptions', 'pip._internal.models.candidate', 'pip._internal.req', 'pip._internal.operations.check', 'pip._internal.cli.req_command', 'pip._internal.commands.search', 'pip._internal.cli.cmdoptions', 'pip._internal.network.xmlrpc', 'pip._internal.models.installation_report', 'pip._internal.wheel_builder', 'pip._internal.utils.wheel', 'pip._internal.distributions.base', 'pip._internal.distributions.sdist', 'pip._internal.distributions.wheel', 'pip._vendor.pkg_resources', 'pip._vendor.resolvelib.providers', 'pip._vendor.resolvelib.resolvers', 'pip._vendor.resolvelib.reporters', 'pip._vendor.resolvelib', 'pip._internal.resolution.resolvelib.provider', 'pip._internal.resolution.resolvelib.reporter', 'pip._vendor.resolvelib.structs', 'pip._vendor.distlib.scripts', 'pip._vendor.distlib.util', 'pip.command', 'pip._vendor.tomli_w._writer', 'pip._internal.req.req_dependency_group', 'pip._vendor.dependency_groups', 'pip._internal.models.pylock'], ['logging.handlers', 'logging.config'], ['dataclasses_json.stringcase', 'dataclasses_json.undefined', 'dataclasses_json.api', 'dataclasses_json.cfg', 'dataclasses_json.utils', 'dataclasses_json.core', 'dataclasses_json.mm'], ['contourpy._contourpy', 'contourpy.array', 'contourpy.enum_util', 'contourpy.typecheck', 'contourpy.types', 'contourpy._version', 'contourpy.chunk', 'contourpy.convert', 'contourpy.dechunk', 'contourpy.util.mpl_util', 'contourpy.util.renderer', 'contourpy.util._build_config', 'contourpy.util.bokeh_util'], ['_transports.base', '_transports.default'], ['externals.loky', 'externals.loky.backend', 'externals.loky.backend.resource_tracker', 'externals.loky.process_executor', 'externals.loky.reusable_executor'], ['dask.distributed', 'dask.sizeof', 'dask.utils', 'dask.dataframe', 'dask.callbacks', 'dask.array', 'dask.dataframe.dask_expr', 'dask.dataframe.api'], ['distributed.utils', 'distributed.metrics', 'distributed.utils_test'], ['multiprocessing.context', 'multiprocessing.pool', 'multiprocessing.process', 'multiprocessing.reduction', 'multiprocessing.queues', 'multiprocessing.connection', 'multiprocessing.popen_spawn_win32', 'multiprocessing.resource_tracker'], ['joblib.backports', 'joblib.test.common', 'joblib.testing', 'joblib._utils', 'joblib.disk', 'joblib._memmapping_reducer', 'joblib.executor', 'joblib.parallel', 'joblib.pool', 'joblib.compressor', 'joblib.numpy_pickle_utils', 'joblib.test', 'joblib.memory', 'joblib._multiprocessing_helpers', 'joblib.externals.loky', 'joblib._parallel_backends', 'joblib.externals.loky.process_executor', 'joblib.func_inspect', 'joblib.test.test_func_inspect_special_encoding', 'joblib._store_backends', 'joblib._dask', 'joblib.test.test_parallel', 'joblib.hashing', 'joblib.logger', 'joblib.externals.cloudpickle', 'joblib.externals'], ['lz4.frame', 'lz4.block'], ['pipdeptree._warning', 'pipdeptree._models.package', 'pipdeptree._cli', 'pipdeptree._detect_env', 'pipdeptree._discovery', 'pipdeptree._models', 'pipdeptree._render', 'pipdeptree._validate', 'pipdeptree._models.dag', 'pipdeptree._freeze'], ['jsonschema.exceptions', 'jsonschema.protocols', 'jsonschema.validators', 'jsonschema._format', 'jsonschema._types', 'jsonschema._utils', 'jsonschema.cli', 'jsonschema.tests._suite'], ['pyarrow.parquet', 'pyarrow._orc', 'pyarrow.fs', 'pyarrow.lib', 'pyarrow.gandiva', 'pyarrow.acero', 'pyarrow.dataset', 'pyarrow.orc', 'pyarrow.parquet.encryption', 'pyarrow.flight', 'pyarrow.substrait', 'pyarrow.cuda', 'pyarrow.tests.util', 'pyarrow._flight', 'pyarrow._substrait', 'pyarrow.ipc', 'pyarrow.types', 'pyarrow.util', 'pyarrow._dataset', 'pyarrow.compute', 'pyarrow._dataset_orc', 'pyarrow._dataset_parquet', 'pyarrow._dataset_parquet_encryption', 'pyarrow._cuda', 'pyarrow._feather', 'pyarrow.pandas_compat', 'pyarrow._fs', 'pyarrow._azurefs', 'pyarrow._hdfs', 'pyarrow._gcsfs', 'pyarrow._s3fs', 'pyarrow._acero', 'pyarrow._csv', 'pyarrow._json', 'pyarrow._compute', 'pyarrow.vendored', 'pyarrow.tests.strategies', 'pyarrow.tests', 'pyarrow.interchange', 'pyarrow.vendored.version', 'pyarrow._pyarrow_cpp_tests', 'pyarrow.cffi', 'pyarrow.jvm', 'pyarrow.tests.test_io', 'pyarrow.csv', 'pyarrow.feather', 'pyarrow.json', 'pyarrow.tests.parquet.encryption', 'pyarrow.interchange.column', 'pyarrow.interchange.buffer', 'pyarrow._parquet_encryption', 'pyarrow._parquet', 'pyarrow.interchange.from_dataframe', 'pyarrow.tests.parquet.common', 'pyarrow.tests.pandas_examples', 'pyarrow._stubs_typing', 'pyarrow.__lib_pxi.table'], ['pyspark.sql', 'pyspark.pandas.spark.functions', 'pyspark.sql.connect.dataframe', 'pyspark.sql.connect', 'pyspark.sql.connect.window'], ['modin.pandas', 'modin.pandas.utils'], ['sqlframe._version', 'sqlframe.base.dataframe', 'sqlframe.base.column', 'sqlframe.base.window', 'sqlframe.base', 'sqlframe.base.functions', 'sqlframe.base.types', 'sqlframe.base.session'], ['radon.raw', 'radon.visitors', 'radon.cli', 'radon.cli.colors', 'radon.cli.harvest', 'radon.cli.tools', 'radon.tests.test_cli_harvest', 'radon.complexity', 'radon.contrib.flake8', 'radon.metrics'], ['backend.execute', 'backend.context', 'backend.reduction', 'backend.queues', 'backend.utils'], ['pgen2.grammar', 'pgen2.token'], ['blib2to3.pgen2.grammar', 'blib2to3.pgen2', 'blib2to3.pgen2.parse', 'blib2to3.pgen2.tokenize', 'blib2to3.pytree', 'blib2to3.pgen2.token', 'blib2to3.pgen2.driver'], ['testslide.lib', 'testslide.mock_callable', 'testslide.mock_constructor', 'testslide.strict_mock', 'testslide.matchers', 'testslide.patch_attribute', 'testslide.runner', 'testslide.dsl', 'testslide.import_profiler'], ['dateutil.tz', 'dateutil.parser', 'dateutil.relativedelta', 'dateutil.rrule', 'dateutil.zoneinfo', 'dateutil.tz.tz'], ['PIL.Image', 'PIL.PngImagePlugin', 'PIL.TiffTags'], ['watchdog.observers.api', 'watchdog.events', 'watchdog.observers'], ['black.concurrency', 'black.handle_ipynb_magics', 'black.mode', 'black.output', 'black.report', 'black.nodes', 'black.cache', 'black.comments', 'black.const', 'black.files', 'black.linegen', 'black.lines', 'black.parsing', 'black.ranges', 'black.brackets', 'black.strings', 'black.rusty', 'black.numerics', 'black.trans', 'black._width_table'], ['aiohttp.typedefs', 'aiohttp.web_middlewares', 'aiohttp.web_request', 'aiohttp.web_response'], ['util.connection', 'util.timeout', 'util.url', 'util.request', 'util.retry', 'util.proxy', 'util.response', 'util.ssl_', 'util.ssltransport', 'util.util', 'util.wait', 'util.ssl_match_hostname', 'util.queue'], ['jaraco.text', 'jaraco.functools', 'jaraco.path', 'jaraco.envs', 'jaraco.test.cpython', 'jaraco.context'], ['_internal._schema_generation_shared', '_internal._generate_schema', '_internal._utils', '_internal._repr', '_internal._core_utils', '_internal._dataclasses', '_internal._validators', '_internal._import_utils'], ['pydantic_core._pydantic_core', 'pydantic_core.core_schema'], ['psutil._common', 'psutil._compat', 'psutil.tests', 'psutil._psutil_posix', 'psutil._psutil_linux', 'psutil._pslinux', 'psutil._psposix', 'psutil.tests.test_process_all', 'psutil._pswindows'], ['future.utils', 'future.builtins', 'future.backports.misc', 'future.standard_library', 'future.types.newbytes', 'future.types.newobject', 'future.types', 'future.types.newstr', 'future.utils.surrogateescape', 'future.moves.subprocess', 'future.types.newdict', 'future.types.newint', 'future.builtins.new_min_max', 'future.builtins.newnext', 'future.builtins.newround', 'future.builtins.newsuper', 'future.builtins.iterators', 'future.builtins.misc', 'future.backports.urllib', 'future.moves.test', 'future.moves.dbm', 'future.backports.http.server', 'future.backports.test', 'future.backports.http.cookiejar', 'future.backports.email.utils', 'future.backports', 'future.backports.http', 'future.backports.html.entities', 'future.backports.email', 'future.backports.urllib.parse', 'future.backports.http.client', 'future.backports.xmlrpc.client', 'future.backports.email.charset', 'future.backports.email.errors', 'future.backports.email.quoprimime', 'future.standard_library.email._policybase', 'future.standard_library.email.headerregistry', 'future.standard_library.email.utils', 'future.backports.email.parser', 'future.backports.email._encoded_words', 'future.backports.email._policybase', 'future.backports.email.generator', 'future.backports.email.iterators', 'future.backports.email.feedparser', 'future.backports.email.message', 'future.backports.email.header', 'future.backports.email._parseaddr', 'future.backports.email.encoders', 'future.backports.email.mime.base', 'future.backports.email.mime.nonmultipart'], ['lib2to3.main', 'lib2to3.fixer_util', 'lib2to3.pygram', 'lib2to3.pytree', 'lib2to3.pgen2', 'lib2to3.fixes.fix_input', 'lib2to3.fixes.fix_xrange', 'lib2to3.fixes.fix_imports', 'lib2to3.fixes.fix_urllib', 'lib2to3.fixes.fix_import', 'lib2to3.pgen2.parse', 'lib2to3.refactor'], ['libfuturize.fixes', 'libfuturize.fixer_util', 'libfuturize.fixes.fix_print'], ['coverage.exceptions', 'coverage.types', 'coverage.misc', 'coverage.plugin', 'coverage.report_core', 'coverage.results', 'coverage.tomlconfig', 'coverage.bytecode', 'coverage.debug', 'coverage.data', 'coverage.annotate', 'coverage.collector', 'coverage.config', 'coverage.context', 'coverage.core', 'coverage.disposition', 'coverage.files', 'coverage.html', 'coverage.inorout', 'coverage.jsonreport', 'coverage.lcovreport', 'coverage.multiproc', 'coverage.plugin_support', 'coverage.python', 'coverage.report', 'coverage.xmlreport', 'coverage.templite', 'coverage.version', 'coverage.plugins', 'coverage.control', 'coverage.execfile', 'coverage.pytracer', 'coverage.sysmon', 'coverage.tracer', 'coverage.numbits', 'coverage.sqlitedb', 'coverage.phystokens', 'coverage.parser', 'coverage.regions', 'coverage.cmdline', 'coverage.sqldata'], ['pbr.hooks', 'pbr.version', 'pbr.pbr_json', 'pbr.tests'], ['gitdb.exc', 'gitdb.fun', 'gitdb.util', 'gitdb.base', 'gitdb.const', 'gitdb.stream', 'gitdb.typ', 'gitdb.utils.encoding', 'gitdb.db', 'gitdb.db.loose', 'gitdb.pack', 'gitdb.test.lib', 'gitdb.db.base', 'gitdb.db.pack', 'gitdb.db.ref', 'gitdb.db.git', 'gitdb.db.mem'], ['dbm.ndbm', 'dbm.gnu', 'dbm.dumb'], ['dill._dill', 'dill.tests.__main__', 'dill.source', 'dill.detect', 'dill._objects', 'dill.temp', 'dill.logger'], ['libpasteurize.fixes', 'libpasteurize.fixes.fix_division'], ['typeguard.importhook', 'typeguard._config', 'typeguard._exceptions', 'typeguard._importhook', 'typeguard._utils'], ['git.exc', 'git.types', 'git.util', 'git.cmd', 'git.compat', 'git.diff', 'git.repo.base', 'git.config', 'git.refs', 'git.objects.commit', 'git.objects.submodule.base', 'git.remote', 'git.db', 'git.index', 'git.objects', 'git.repo', 'git.objects.blob', 'git.objects.util', 'git.objects.base', 'git.objects.tree', 'git.refs.reference', 'git.refs.log', 'git.objects.fun', 'git.refs.tag', 'git.refs.symbolic'], ['tomlkit.api', 'tomlkit.toml_document', 'tomlkit.container', 'tomlkit._compat', 'tomlkit._types', 'tomlkit._utils', 'tomlkit.exceptions', 'tomlkit.items', 'tomlkit.source', 'tomlkit.toml_char', 'tomlkit.parser'], ['twisted.trial.unittest', 'twisted.trial.itrial', 'twisted.internet.defer', 'twisted.names', 'twisted.internet.abstract', 'twisted.internet.asyncioreactor', 'twisted.names.cache', 'twisted.names.client', 'twisted.names.hosts', 'twisted.names.resolve', 'twisted.python'], ['platformdirs.windows', 'platformdirs.macos', 'platformdirs.unix', 'platformdirs.android'], ['numba.cuda.cudadrv.devicearray', 'numba.core', 'numba.core.datamodel', 'numba.core.extending', 'numba.core.imputils', 'numba.typed', 'numba.extending'], ['scipy.sparse', 'scipy.stats', 'scipy.optimize', 'scipy.sparse.csgraph', 'scipy.stats.stats', 'scipy.special', 'scipy.sparse.linalg'], ['pydeck.types', 'pydeck.types.base', 'pydeck.exceptions'], ['stevedore.example', 'stevedore.tests', 'stevedore.dispatch', 'stevedore.extension', 'stevedore.example2'], ['lxml.etree', 'lxml.html'], ['botocore.exceptions', 'botocore.response'], ['sqlalchemy.sql.expression', 'sqlalchemy.schema', 'sqlalchemy.types', 'sqlalchemy.engine', 'sqlalchemy.dialects.postgresql', 'sqlalchemy.sql', 'sqlalchemy.dialects.mysql', 'sqlalchemy.orm', 'sqlalchemy.engine.base', 'sqlalchemy.exc'], ['fsspec.implementations.memory', 'fsspec.registry'], ['mpl_toolkits.axes_grid1', 'mpl_toolkits.axes_grid1.inset_locator', 'mpl_toolkits.mplot3d', 'mpl_toolkits.axisartist', 'mpl_toolkits.axes_grid1.parasite_axes', 'mpl_toolkits.axisartist.grid_finder', 'mpl_toolkits.axes_grid1.axes_divider', 'mpl_toolkits.axisartist.axis_artist', 'mpl_toolkits.axisartist.angle_helper', 'mpl_toolkits.axisartist.axislines', 'mpl_toolkits.axisartist.grid_helper_curvelinear', 'mpl_toolkits.axisartist.floating_axes', 'mpl_toolkits.mplot3d.art3d', 'mpl_toolkits.mplot3d.axes3d', 'mpl_toolkits.axes_grid1.mpl_axes', 'mpl_toolkits.axes_grid1.anchored_artists', 'mpl_toolkits.axes_grid1.axes_rgb'], ['odf.namespaces', 'odf.table', 'odf.opendocument', 'odf.text', 'odf.style', 'odf.config', 'odf.element', 'odf.office'], ['openpyxl.descriptors.serialisable', 'openpyxl.workbook', 'openpyxl.styles', 'openpyxl.cell.cell'], ['PyQt5.QtWidgets', 'PyQt5.QtGui'], ['test.support', 'test.support.os_helper', 'test.support.warnings_helper', 'test.test_support', 'test.support.import_helper'], ['xmlrpc.server', 'xmlrpc.client'], ['rfc6749.parameters', 'rfc6749.errors', 'rfc6749.hooks', 'rfc6749.authenticate_client', 'rfc6749.requests'], ['requests.auth', 'requests.utils', 'requests.adapters', 'requests.exceptions'], ['django.dispatch', 'django.http', 'django.conf', 'django.utils.module_loading', 'django.utils.functional', 'django.core.cache', 'django.contrib.staticfiles', 'django.core.exceptions', 'django.db', 'django.contrib.auth.forms', 'django.core.validators'], ['starlette.datastructures', 'starlette.responses'], ['base_client.async_app', 'base_client.async_openid'], ['compat.py310', 'compat.py39', 'compat.numpy', 'compat.py38'], ['config.downloads', 'config.schemas.v3_0'], ['wheel.macosx_libfile', 'wheel.wheelfile', 'wheel.cli', 'wheel.util'], ['Cython.Distutils.build_ext', 'Cython.Compiler.Version', 'Cython.Build'], ['compilers.C', 'compilers.C.base', 'compilers.C.errors', 'compilers.C.cygwin'], ['vendored.packaging.requirements', 'vendored.packaging', 'vendored.packaging.tags'], ['OpenSSL.SSL', 'OpenSSL.crypto'], ['h2.config', 'h2.connection', 'h2.events', 'h2.exceptions', 'h2.settings'], ['lark.grammar', 'lark.lark', 'lark.lexer'], ['re._constants', 're._parser'], ['wx.lib.wxcairo', 'wx.svg'], ['polars.dataframe.group_by', 'polars.lazyframe.group_by'], ['ibis.selectors', 'ibis.expr.types', 'ibis.expr.operations', 'ibis.expr.datatypes'], ['bokeh.io', 'bokeh.io.export', 'bokeh.layouts', 'bokeh.models.annotations.labels', 'bokeh.palettes', 'bokeh.plotting', 'bokeh.core.enums', 'bokeh.models', 'bokeh.plotting.figure', 'bokeh.embed'], ['smmap.mman', 'smmap.util', 'smmap.buf', 'smmap.test.lib'], ['__pypy__.builders', '__pypy__.time'], ['packages.six', 'packages.six.moves.urllib.parse', 'packages.six.moves.http_client', 'packages.six.moves', 'packages.backports.weakref_finalize', 'packages.backports.makefile'], ['_securetransport.bindings', '_securetransport.low_level'], ['trio.from_thread', 'trio.lowlevel', 'trio.socket', 'trio.to_thread', 'trio.testing'], ['tool.main', 'tool.interceptors'], ['spdx_tools.spdx.model', 'spdx_tools.spdx.model.spdx_no_assertion', 'spdx_tools.spdx.writer.write_utils', 'spdx_tools.spdx'], ['ecosystems.base', 'ecosystems.target'], ['python.main', 'python.dependencies'], ['code_generators.genapi', 'code_generators.numpy_api'], ['PyInstaller.compat', 'PyInstaller.utils.hooks'], ['numpy_distutils.command.build_flib', 'numpy_distutils.fcompiler', 'numpy_distutils.command.cpuinfo', 'numpy_distutils.cpuinfo'], ['v5.api', 'v5.schema'], ['schema._typing', 'schema._config', 'schema.channels', 'schema.core'], ['reportlab.graphics.shapes', 'reportlab.lib', 'reportlab.graphics'], ['freetype.ft_enums', 'freetype.ft_errors', 'freetype.ft_structs', 'freetype.ft_types', 'freetype.raw'], ['fs.base', 'fs.osfs', 'fs.copy', 'fs.errors', 'fs.subfs', 'fs.tempfs', 'fs.tools', 'fs.zipfs', 'fs.path'], ['past.builtins', 'past.utils', 'past.types', 'past.builtins.noniterators', 'past.builtins.misc'], ['commands.codemods', 'commands.configurationless', 'commands.consolidate_nested_configurations', 'commands.expand_target_coverage', 'commands.fix_configuration', 'commands.fixme', 'commands.fixme_all', 'commands.fixme_single', 'commands.global_strictness', 'commands.global_version_update', 'commands.pysa_version_update', 'commands.strict_default', 'commands.support_sqlalchemy', 'commands.targets_to_configuration', 'commands.command'], ['mdit_py_plugins.front_matter', 'mdit_py_plugins.utils'], ['snowflake.snowpark._internal.utils', 'snowflake.connector', 'snowflake.connector.cursor', 'snowflake.snowpark.session', 'snowflake.snowpark.context', 'snowflake.connector.pandas_tools', 'snowflake.snowpark.exceptions'], ['langchain.callbacks.base', 'langchain.schema'], ['utils.misc', 'utils.config_utils', 'utils.file_utils', 'utils.fs_utils'], ['models.progress_tracker', 'models.project_metrics'], ['core.dependency_manager', 'core.simple_analyzer', 'core.config', 'core.version', 'core.knowledge.framework_knowledge_base', 'core.logging', 'core.utils.dict_utils', 'core.vcs.engine', 'core.vcs.models', 'core.vcs.config', 'core.analysis.standalone_analyzer', 'core.models.project_metrics', 'core.fs_utils', 'core.analysis.import_analyzer'], ['ui.gui', 'ui.tui', 'ui.web', 'ui.visualization.interactive_charts'], ['pygal.graph.box', 'pygal.style'], ['pytest_benchmark.csv', 'pytest_benchmark.cli'], ['storage.file', 'storage.elasticsearch'], ['uc_micro.categories', 'uc_micro.properties'], ['Foundation._context', 'Foundation._functiondefines', 'Foundation._nsindexset', 'Foundation._nsobject', 'Foundation._nsurl'], ['textual_dev.client', 'textual_dev.redirect_output'], ['_plotly_utils.files', '_plotly_utils.importers', '_plotly_utils.basevalidators', '_plotly_utils.utils', '_plotly_utils.exceptions', '_plotly_utils.data_utils', '_plotly_utils.optional_imports', '_plotly_utils.colors'], ['objc._pythonify', 'objc._objc', 'objc._new', 'objc._transform', 'objc._convenience', 'objc._convenience_mapping', 'objc._framework'], ['kaleido.scopes.plotly', 'kaleido.errors'], ['components.sidebar', 'components.visualizations'], ['pages.dashboard', 'pages.issues', 'pages.files', 'pages.meta_systems'], ['PAT_project_analysis.PAT_tool.bandit_stage', 'PAT_project_analysis.PAT_tool.black_stage', 'PAT_project_analysis.PAT_tool.call_graph_stage', 'PAT_project_analysis.PAT_tool.call_graph_visualizer', 'PAT_project_analysis.PAT_tool.chunked_prompt_generator', 'PAT_project_analysis.PAT_tool.complexity_analyzer', 'PAT_project_analysis.PAT_tool.content_extractor', 'PAT_project_analysis.PAT_tool.coverage_stage', 'PAT_project_analysis.PAT_tool.dependency_analyzer', 'PAT_project_analysis.PAT_tool.dependency_stage', 'PAT_project_analysis.PAT_tool.effect_overlay_stage', 'PAT_project_analysis.PAT_tool.flake8_stage', 'PAT_project_analysis.PAT_tool.graph_overlay_stage', 'PAT_project_analysis.PAT_tool.hypothesis_stage', 'PAT_project_analysis.PAT_tool.isort_stage', 'PAT_project_analysis.PAT_tool.meta_system_stage', 'PAT_project_analysis.PAT_tool.meta_system_visualizer', 'PAT_project_analysis.PAT_tool.models', 'PAT_project_analysis.PAT_tool.mypy_stage', 'PAT_project_analysis.PAT_tool.pipeline', 'PAT_project_analysis.PAT_tool.pre_analysis', 'PAT_project_analysis.PAT_tool.protocol_extraction_stage', 'PAT_project_analysis.PAT_tool.pycontract_stage', 'PAT_project_analysis.PAT_tool.pydocstyle_stage', 'PAT_project_analysis.PAT_tool.pylint_stage', 'PAT_project_analysis.PAT_tool.pyre_stage', 'PAT_project_analysis.PAT_tool.pyright_stage', 'PAT_project_analysis.PAT_tool.reporter', 'PAT_project_analysis.PAT_tool.ruff_stage', 'PAT_project_analysis.PAT_tool.structure_analyzer', 'PAT_project_analysis.PAT_tool.tda_overlay_stage', 'PAT_project_analysis.PAT_tool.tla_stage', 'PAT_project_analysis.PAT_tool.utils', 'PAT_project_analysis.PAT_tool.visualization', 'PAT_project_analysis.PAT_tool.dependency_audit', 'PAT_project_analysis.cli']]"]}