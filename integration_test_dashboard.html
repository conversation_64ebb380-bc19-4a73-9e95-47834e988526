
        <!DOCTYPE html>
        <html>
        <head>
            <title>Integration Test Dashboard</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .dashboard-header { text-align: center; margin-bottom: 20px; }
                .panel { 
                    display: inline-block; 
                    margin: 10px; 
                    padding: 15px; 
                    border: 1px solid #ddd; 
                    border-radius: 8px;
                    background: white;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .panel-title { font-weight: bold; margin-bottom: 10px; }
                .metric-value { font-size: 2em; text-align: center; color: #007bff; }
            </style>
        </head>
        <body>
            <div class="dashboard-header">
                <h1>Integration Test Dashboard</h1>
                <p>Testing analysis + visualization integration</p>
            </div>
        
            <div class="panel" style="width: 200px; height: 150px;">
                <div class="panel-title">Total Files</div>
                
        <div class="metric-value">117</div>
        <div style="text-align: center; margin-top: 10px;">total_files</div>
        
            </div>
            
            <div class="panel" style="width: 200px; height: 150px;">
                <div class="panel-title">Average Complexity</div>
                
        <div class="metric-value">42.7</div>
        <div style="text-align: center; margin-top: 10px;">avg_complexity</div>
        
            </div>
            
            <div class="panel" style="width: 400px; height: 300px;">
                <div class="panel-title">Complexity Distribution</div>
                
        <div style="width: 370px; height: 240px;">
            <canvas id="complexity_chart"></canvas>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            const ctx = document.getElementById('complexity_chart').getContext('2d');
            const chart = new Chart(ctx, {"type": "bar", "data": {"labels": ["Low", "Medium", "High", "Very High"], "datasets": [{"label": "", "data": [22, 12, 26, 57], "backgroundColor": "rgba(54, 162, 235, 0.2)", "borderColor": "rgba(54, 162, 235, 1)", "borderWidth": 1}]}, "options": {"responsive": true, "plugins": {"title": {"display": false, "text": ""}}}});
        </script>
        
            </div>
            
        </body>
        </html>
        