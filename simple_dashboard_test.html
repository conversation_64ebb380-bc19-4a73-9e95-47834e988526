
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test Monitoring Dashboard</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .dashboard-header { text-align: center; margin-bottom: 20px; }
                .panel { 
                    display: inline-block; 
                    margin: 10px; 
                    padding: 15px; 
                    border: 1px solid #ddd; 
                    border-radius: 8px;
                    background: white;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .panel-title { font-weight: bold; margin-bottom: 10px; }
                .metric-value { font-size: 2em; text-align: center; color: #007bff; }
            </style>
        </head>
        <body>
            <div class="dashboard-header">
                <h1>Test Monitoring Dashboard</h1>
                <p>A test dashboard for the unified visualization system</p>
            </div>
        
            <div class="panel" style="width: 200px; height: 150px;">
                <div class="panel-title">CPU Usage</div>
                
        <div class="metric-value">45.2</div>
        <div style="text-align: center; margin-top: 10px;">cpu_usage</div>
        
            </div>
            
            <div class="panel" style="width: 200px; height: 150px;">
                <div class="panel-title">Memory Usage</div>
                
        <div class="metric-value">67.8</div>
        <div style="text-align: center; margin-top: 10px;">memory_usage</div>
        
            </div>
            
            <div class="panel" style="width: 200px; height: 150px;">
                <div class="panel-title">Disk Usage</div>
                
        <div class="metric-value">23.1</div>
        <div style="text-align: center; margin-top: 10px;">disk_usage</div>
        
            </div>
            
            <div class="panel" style="width: 400px; height: 300px;">
                <div class="panel-title">CPU Over Time</div>
                
        <div style="width: 370px; height: 240px;">
            <canvas id="cpu_chart"></canvas>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            const ctx = document.getElementById('cpu_chart').getContext('2d');
            const chart = new Chart(ctx, {"type": "line", "data": {"labels": ["1h", "2h", "3h", "4h", "5h"], "datasets": [{"label": "", "data": [40, 45, 42, 48, 44], "backgroundColor": "rgba(54, 162, 235, 0.2)", "borderColor": "rgba(54, 162, 235, 1)", "borderWidth": 1}]}, "options": {"responsive": true, "plugins": {"title": {"display": false, "text": ""}}}});
        </script>
        
            </div>
            
            <div class="panel" style="width: 400px; height: 300px;">
                <div class="panel-title">Memory Usage</div>
                
        <div style="width: 370px; height: 240px;">
            <canvas id="memory_chart"></canvas>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            const ctx = document.getElementById('memory_chart').getContext('2d');
            const chart = new Chart(ctx, {"type": "bar", "data": {"labels": ["App1", "App2", "App3", "System"], "datasets": [{"label": "", "data": [25, 35, 20, 20], "backgroundColor": "rgba(54, 162, 235, 0.2)", "borderColor": "rgba(54, 162, 235, 1)", "borderWidth": 1}]}, "options": {"responsive": true, "plugins": {"title": {"display": false, "text": ""}}}});
        </script>
        
            </div>
            
            <div class="panel" style="width: 400px; height: 300px;">
                <div class="panel-title">Usage Breakdown</div>
                
        <div style="width: 370px; height: 240px;">
            <canvas id="usage_pie"></canvas>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            const ctx = document.getElementById('usage_pie').getContext('2d');
            const chart = new Chart(ctx, {"type": "pie", "data": {"labels": ["CPU", "Memory", "Disk", "Network"], "datasets": [{"label": "", "data": [30, 40, 20, 10], "backgroundColor": "rgba(54, 162, 235, 0.2)", "borderColor": "rgba(54, 162, 235, 1)", "borderWidth": 1}]}, "options": {"responsive": true, "plugins": {"title": {"display": false, "text": ""}}}});
        </script>
        
            </div>
            
        </body>
        </html>
        