#!/usr/bin/env python3
"""
Simple Integration Test
=======================

A simplified integration test that validates Week 2 consolidation
without complex import dependencies.
"""

import sys
import time
import json
from pathlib import Path


def test_file_structure_consolidation():
    """Test that file consolidation actually happened"""
    print("🧪 Testing File Structure Consolidation")
    print("=" * 40)
    
    results = {}
    
    # Test 1: CLI Consolidation
    cli_main_files = list(Path("vibe_check/cli").glob("*.py")) if Path("vibe_check/cli").exists() else []
    cli_ui_files = list(Path("vibe_check/ui/cli").glob("*.py")) if Path("vibe_check/ui/cli").exists() else []
    
    print(f"  📁 CLI main files: {len(cli_main_files)}")
    print(f"  📁 CLI UI files: {len(cli_ui_files)}")
    
    # Check for unified formatters
    unified_formatters = Path("vibe_check/cli/unified_formatters.py")
    if unified_formatters.exists():
        print("  ✅ Unified formatters created")
        results['cli_consolidation'] = True
    else:
        print("  ❌ Unified formatters missing")
        results['cli_consolidation'] = False
    
    # Test 2: Analysis Engine Consolidation
    unified_analyzer = Path("vibe_check/core/unified_analyzer.py")
    if unified_analyzer.exists():
        print("  ✅ Unified analyzer created")
        results['analysis_consolidation'] = True
    else:
        print("  ❌ Unified analyzer missing")
        results['analysis_consolidation'] = False
    
    # Test 3: Visualization Consolidation
    core_viz_dir = Path("vibe_check/core/visualization")
    if core_viz_dir.exists():
        viz_files = list(core_viz_dir.glob("*.py"))
        print(f"  📁 Core visualization files: {len(viz_files)}")
        if len(viz_files) >= 2:  # Should have at least unified_charts.py and dashboard_engine.py
            print("  ✅ Visualization consolidation completed")
            results['visualization_consolidation'] = True
        else:
            print("  ❌ Visualization consolidation incomplete")
            results['visualization_consolidation'] = False
    else:
        print("  ❌ Core visualization directory missing")
        results['visualization_consolidation'] = False
    
    return results


def test_generated_files_quality():
    """Test quality of generated files"""
    print("\n🧪 Testing Generated Files Quality")
    print("=" * 35)
    
    results = {}
    
    # Test unified analyzer file
    unified_analyzer = Path("vibe_check/core/unified_analyzer.py")
    if unified_analyzer.exists():
        with open(unified_analyzer, 'r') as f:
            content = f.read()
        
        # Check for key components
        has_async = 'async def' in content
        has_config = 'AnalysisConfig' in content
        has_result = 'AnalysisResult' in content
        has_engine = 'UnifiedAnalysisEngine' in content
        
        quality_score = sum([has_async, has_config, has_result, has_engine])
        print(f"  📄 Unified analyzer quality: {quality_score}/4")
        
        if quality_score >= 3:
            print("  ✅ Unified analyzer has good quality")
            results['analyzer_quality'] = True
        else:
            print("  ❌ Unified analyzer quality issues")
            results['analyzer_quality'] = False
    else:
        results['analyzer_quality'] = False
    
    # Test visualization files
    viz_charts = Path("vibe_check/core/visualization/unified_charts.py")
    viz_dashboard = Path("vibe_check/core/visualization/dashboard_engine.py")
    
    viz_quality = 0
    if viz_charts.exists():
        with open(viz_charts, 'r') as f:
            content = f.read()
        if 'ChartType' in content and 'UnifiedChartEngine' in content:
            viz_quality += 1
            print("  ✅ Unified charts file has good structure")
    
    if viz_dashboard.exists():
        with open(viz_dashboard, 'r') as f:
            content = f.read()
        if 'Dashboard' in content and 'UnifiedDashboardEngine' in content:
            viz_quality += 1
            print("  ✅ Dashboard engine file has good structure")
    
    if viz_quality >= 2:
        print("  ✅ Visualization files have good quality")
        results['visualization_quality'] = True
    else:
        print("  ❌ Visualization files quality issues")
        results['visualization_quality'] = False
    
    return results


def test_performance_claims():
    """Test performance claims with simple tests"""
    print("\n🧪 Testing Performance Claims")
    print("=" * 30)
    
    results = {}
    
    try:
        # Test analysis performance
        from test_unified_analyzer_simple import SimpleUnifiedAnalyzer
        
        analyzer = SimpleUnifiedAnalyzer(max_workers=1, use_async=False)
        
        # Create test files
        test_dir = Path("temp_test_files")
        test_dir.mkdir(exist_ok=True)
        
        # Create 50 small test files
        for i in range(50):
            test_file = test_dir / f"test_{i}.py"
            test_file.write_text(f'''
def test_function_{i}():
    """Test function {i}"""
    result = []
    for j in range(10):
        if j % 2 == 0:
            result.append(j * {i})
    return result

class TestClass_{i}:
    def __init__(self):
        self.data = list(range(10))
    
    def process(self):
        return sum(self.data)
''')
        
        # Test analysis speed
        start_time = time.time()
        
        import asyncio
        result = asyncio.run(analyzer.analyze_project(test_dir))
        
        analysis_time = time.time() - start_time
        files_per_second = result.files_analyzed / analysis_time if analysis_time > 0 else 0
        
        print(f"  📊 Analysis Performance:")
        print(f"    • Files: {result.files_analyzed}")
        print(f"    • Time: {analysis_time:.3f}s")
        print(f"    • Speed: {files_per_second:.1f} files/sec")
        
        # Cleanup
        import shutil
        shutil.rmtree(test_dir, ignore_errors=True)
        
        # Performance targets
        if files_per_second >= 100:  # Conservative target
            print("  ✅ Analysis performance target met")
            results['analysis_performance'] = True
        else:
            print("  ⚠️  Analysis performance below target")
            results['analysis_performance'] = False
        
    except Exception as e:
        print(f"  ❌ Analysis performance test failed: {e}")
        results['analysis_performance'] = False
    
    try:
        # Test visualization performance
        from test_visualization_simple import SimpleDashboardEngine, ChartType
        
        dashboard_engine = SimpleDashboardEngine()
        
        # Create multiple dashboards
        start_time = time.time()
        
        for i in range(20):
            dashboard = dashboard_engine.create_dashboard(f"perf_test_{i}", f"Dashboard {i}")
            dashboard_engine.add_metric_panel(f"perf_test_{i}", "metric1", "Metric 1", {'width': 200, 'height': 150}, "metric1")
            dashboard_engine.add_chart_panel(f"perf_test_{i}", "chart1", "Chart 1", ChartType.LINE, {'width': 400, 'height': 300})
        
        creation_time = time.time() - start_time
        
        # Test rendering
        start_time = time.time()
        
        for i in range(20):
            html = dashboard_engine.render_dashboard_html(f"perf_test_{i}")
        
        render_time = time.time() - start_time
        dashboards_per_second = 20 / render_time if render_time > 0 else 0
        
        print(f"  📊 Visualization Performance:")
        print(f"    • Creation time: {creation_time:.3f}s")
        print(f"    • Render time: {render_time:.3f}s")
        print(f"    • Render speed: {dashboards_per_second:.1f} dashboards/sec")
        
        if dashboards_per_second >= 50:  # Conservative target
            print("  ✅ Visualization performance target met")
            results['visualization_performance'] = True
        else:
            print("  ⚠️  Visualization performance below target")
            results['visualization_performance'] = False
        
    except Exception as e:
        print(f"  ❌ Visualization performance test failed: {e}")
        results['visualization_performance'] = False
    
    return results


def test_functionality_preservation():
    """Test that core functionality is preserved"""
    print("\n🧪 Testing Functionality Preservation")
    print("=" * 38)
    
    results = {}
    
    # Test 1: Analysis functionality
    try:
        from test_unified_analyzer_simple import SimpleUnifiedAnalyzer, SimpleFileMetrics
        
        analyzer = SimpleUnifiedAnalyzer()
        
        # Test on a single file
        test_file = Path("test_single_file.py")
        test_file.write_text('''
def example_function():
    """Example function"""
    if True:
        for i in range(10):
            print(i)
    return "done"

class ExampleClass:
    def __init__(self):
        self.value = 42
''')
        
        # Create temporary directory
        temp_dir = Path("temp_single_test")
        temp_dir.mkdir(exist_ok=True)
        (temp_dir / "test.py").write_text(test_file.read_text())
        
        import asyncio
        result = asyncio.run(analyzer.analyze_project(temp_dir))
        
        # Cleanup
        test_file.unlink(missing_ok=True)
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        if result.files_analyzed > 0 and len(result.file_metrics) > 0:
            print("  ✅ Analysis functionality preserved")
            results['analysis_functionality'] = True
        else:
            print("  ❌ Analysis functionality broken")
            results['analysis_functionality'] = False
        
    except Exception as e:
        print(f"  ❌ Analysis functionality test failed: {e}")
        results['analysis_functionality'] = False
    
    # Test 2: Visualization functionality
    try:
        from test_visualization_simple import SimpleDashboardEngine, ChartType
        
        engine = SimpleDashboardEngine()
        dashboard = engine.create_dashboard("test", "Test Dashboard")
        
        # Add panels
        engine.add_metric_panel("test", "metric1", "Test Metric", {'width': 200, 'height': 150}, "test_metric")
        engine.add_chart_panel("test", "chart1", "Test Chart", ChartType.LINE, {'width': 400, 'height': 300})
        
        # Render
        html = engine.render_dashboard_html("test")
        
        if len(html) > 1000 and 'Test Dashboard' in html:
            print("  ✅ Visualization functionality preserved")
            results['visualization_functionality'] = True
        else:
            print("  ❌ Visualization functionality broken")
            results['visualization_functionality'] = False
        
    except Exception as e:
        print(f"  ❌ Visualization functionality test failed: {e}")
        results['visualization_functionality'] = False
    
    return results


def main():
    """Main validation function"""
    print("🚀 Week 2 Consolidation Validation")
    print("=" * 40)
    
    all_results = {}
    
    # Run all tests
    all_results.update(test_file_structure_consolidation())
    all_results.update(test_generated_files_quality())
    all_results.update(test_performance_claims())
    all_results.update(test_functionality_preservation())
    
    # Calculate scores
    total_tests = len(all_results)
    passed_tests = sum(all_results.values())
    
    print("\n" + "=" * 40)
    print("📊 VALIDATION SUMMARY")
    print("=" * 40)
    
    # Group results by category
    categories = {
        'Consolidation': ['cli_consolidation', 'analysis_consolidation', 'visualization_consolidation'],
        'Quality': ['analyzer_quality', 'visualization_quality'],
        'Performance': ['analysis_performance', 'visualization_performance'],
        'Functionality': ['analysis_functionality', 'visualization_functionality']
    }
    
    for category, tests in categories.items():
        category_passed = sum(all_results.get(test, False) for test in tests)
        category_total = len(tests)
        print(f"  {category}: {category_passed}/{category_total} ({'✅' if category_passed >= category_total * 0.7 else '❌'})")
    
    print(f"\n🎯 Overall Score: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    # Determine if Week 2 is ready
    critical_tests = ['cli_consolidation', 'analysis_consolidation', 'visualization_consolidation', 
                     'analysis_functionality', 'visualization_functionality']
    critical_passed = sum(all_results.get(test, False) for test in critical_tests)
    critical_total = len(critical_tests)
    
    if critical_passed >= critical_total * 0.8:  # 80% of critical tests must pass
        print("✅ Week 2 consolidation VALIDATION PASSED")
        print("🚀 Ready to proceed with Week 3: Async Implementation & Caching")
        return 0
    else:
        print("❌ Week 2 consolidation VALIDATION FAILED")
        print(f"⚠️  Critical tests: {critical_passed}/{critical_total} passed")
        print("🔧 Issues need to be addressed before proceeding")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
