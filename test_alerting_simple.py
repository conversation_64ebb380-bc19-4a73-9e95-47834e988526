#!/usr/bin/env python3
"""
Simple Alerting Test
====================

Simple focused test to verify alerting system works correctly.
"""

import asyncio
import time
import tempfile
import shutil
import logging
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Enable info logging
logging.basicConfig(level=logging.INFO)

async def main():
    """Simple alerting test"""
    from vibe_check.monitoring.storage.time_series_engine import (
        TimeSeriesStorageEngine, TSDBConfig
    )
    from vibe_check.monitoring.query.promql_engine import PromQLEngine
    from vibe_check.monitoring.alerting import (
        AlertingEngine, AlertRule, AlertSeverity, AlertState
    )
    
    print("🚀 Simple Alerting Test")
    print("=" * 50)
    
    # Create temporary storage
    temp_dir = Path(tempfile.mkdtemp())
    tsdb_config = TSDBConfig(
        data_dir=temp_dir / "tsdb",
        flush_interval_seconds=0.1
    )
    
    # Initialize TSDB and PromQL engine
    tsdb = TimeSeriesStorageEngine(tsdb_config)
    promql_engine = PromQLEngine(tsdb)
    
    # Add test data
    base_time = time.time()
    print(f"📊 Adding test data at {base_time}")
    
    # Add high CPU data that should trigger alert
    await tsdb.ingest_sample(
        metric_name="cpu_usage_percent",
        value=85.0,  # High CPU that should trigger alert
        labels={"instance": "server1", "job": "node"},
        timestamp=base_time
    )
    
    # Add normal disk data that should NOT trigger alert
    await tsdb.ingest_sample(
        metric_name="disk_usage_percent",
        value=50.0,  # Normal disk that should not trigger alert
        labels={"instance": "server1", "job": "node"},
        timestamp=base_time
    )
    
    await asyncio.sleep(1.0)  # Wait for data to be flushed
    print("✅ Test data added and flushed")
    
    # Verify data is queryable
    print("\n🔍 Verifying data is queryable")
    cpu_results = await promql_engine.execute_query("cpu_usage_percent")
    disk_results = await promql_engine.execute_query("disk_usage_percent")
    
    print(f"  CPU query results: {len(cpu_results)}")
    if cpu_results:
        print(f"    CPU value: {cpu_results[0].values[-1][1]}")
    
    print(f"  Disk query results: {len(disk_results)}")
    if disk_results:
        print(f"    Disk value: {disk_results[0].values[-1][1]}")
    
    # Create alerting engine
    print("\n⚙️ Creating alerting engine")
    alerting_engine = AlertingEngine(promql_engine)
    
    # Create alert rules
    cpu_rule = AlertRule(
        name="high_cpu_alert",
        query="cpu_usage_percent",
        condition="> 80",  # Should trigger with 85.0
        severity=AlertSeverity.CRITICAL,
        description="High CPU usage detected",
        evaluation_interval=1.0,
        for_duration=1.0  # Short duration for testing
    )
    
    disk_rule = AlertRule(
        name="high_disk_alert",
        query="disk_usage_percent",
        condition="> 90",  # Should NOT trigger with 50.0
        severity=AlertSeverity.WARNING,
        description="High disk usage detected",
        evaluation_interval=1.0,
        for_duration=1.0
    )
    
    alerting_engine.add_alert_rule(cpu_rule)
    alerting_engine.add_alert_rule(disk_rule)
    print(f"✅ Added {len(alerting_engine.alert_rules)} alert rules")
    
    # Test manual rule evaluation
    print("\n🧪 Testing manual rule evaluation")
    current_time = time.time()
    
    print("  Evaluating CPU rule...")
    await alerting_engine._evaluate_rule(cpu_rule, current_time)
    
    print("  Evaluating disk rule...")
    await alerting_engine._evaluate_rule(disk_rule, current_time)
    
    # Check results
    active_alerts = alerting_engine.get_active_alerts()
    print(f"\n📋 Results after manual evaluation:")
    print(f"  Active alerts: {len(active_alerts)}")
    
    for alert in active_alerts:
        print(f"    - {alert.rule_name}: {alert.state.value} (value: {alert.value})")
    
    # Test with alerting engine loop
    print("\n🔄 Testing with alerting engine loop")
    await alerting_engine.start()
    
    # Wait for evaluation
    await asyncio.sleep(3.0)
    
    await alerting_engine.stop()
    
    # Check final results
    final_alerts = alerting_engine.get_active_alerts()
    print(f"\n📊 Final results:")
    print(f"  Active alerts: {len(final_alerts)}")
    print(f"  Evaluation count: {alerting_engine.evaluation_count}")
    print(f"  Alert count: {alerting_engine.alert_count}")
    
    for alert in final_alerts:
        print(f"    - {alert.rule_name}: {alert.state.value} (value: {alert.value})")
    
    # Determine success
    cpu_alert_found = any(alert.rule_name == "high_cpu_alert" for alert in final_alerts)
    disk_alert_found = any(alert.rule_name == "high_disk_alert" for alert in final_alerts)
    
    success = cpu_alert_found and not disk_alert_found
    
    print(f"\n🎯 Test Results:")
    print(f"  CPU alert found: {'✅' if cpu_alert_found else '❌'}")
    print(f"  Disk alert found: {'❌' if not disk_alert_found else '✅'}")
    print(f"  Overall success: {'✅' if success else '❌'}")
    
    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
