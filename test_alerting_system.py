#!/usr/bin/env python3
"""
Alerting System Test
====================

Test comprehensive alerting system with rules engine, notification channels, and escalation policies.
"""

import asyncio
import time
import tempfile
import shutil
import logging
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

async def setup_test_environment():
    """Setup test environment with PromQL engine"""
    from vibe_check.monitoring.storage.time_series_engine import (
        TimeSeriesStorageEngine, TSDBConfig
    )
    from vibe_check.monitoring.query.promql_engine import PromQLEngine
    
    # Create temporary storage
    temp_dir = Path(tempfile.mkdtemp())
    tsdb_config = TSDBConfig(
        data_dir=temp_dir / "tsdb",
        flush_interval_seconds=0.1
    )
    
    # Initialize TSDB and PromQL engine
    tsdb = TimeSeriesStorageEngine(tsdb_config)
    promql_engine = PromQLEngine(tsdb)
    
    # Add test data
    base_time = time.time()
    test_metrics = [
        ("cpu_usage_percent", {"instance": "server1", "job": "node"}, [50, 85, 95]),  # High CPU
        ("memory_usage_percent", {"instance": "server1", "job": "node"}, [60, 70, 80]),  # Normal memory
        ("disk_usage_percent", {"instance": "server1", "job": "node"}, [30, 40, 50]),  # Low disk
        ("error_rate", {"service": "api", "environment": "prod"}, [0.01, 0.05, 0.15])  # Increasing errors
    ]
    
    for metric_name, labels, values in test_metrics:
        for i, value in enumerate(values):
            timestamp = base_time + (i * 60)
            await tsdb.ingest_sample(
                metric_name=metric_name,
                value=float(value),
                labels=labels,
                timestamp=timestamp
            )
    
    await asyncio.sleep(1.0)  # Wait for data to be flushed
    
    return tsdb, promql_engine, temp_dir

async def test_alerting_engine_basic():
    """Test basic alerting engine functionality"""
    print_header("Alerting Engine Basic Test", 2)
    
    try:
        from vibe_check.monitoring.alerting import (
            AlertingEngine, AlertRule, AlertSeverity, NotificationConfig, NotificationChannel
        )
        
        # Setup test environment
        tsdb, promql_engine, temp_dir = await setup_test_environment()
        
        # Create alerting engine
        alerting_engine = AlertingEngine(promql_engine)
        print(f"  ✅ Alerting engine created")
        
        # Create alert rules
        cpu_rule = AlertRule(
            name="high_cpu_usage",
            query="cpu_usage_percent",
            condition="> 80",
            severity=AlertSeverity.WARNING,
            description="High CPU usage detected",
            evaluation_interval=10.0,
            for_duration=30.0
        )
        
        error_rule = AlertRule(
            name="high_error_rate",
            query="error_rate",
            condition="> 0.1",
            severity=AlertSeverity.CRITICAL,
            description="High error rate detected",
            evaluation_interval=10.0,
            for_duration=30.0
        )
        
        # Add rules to engine
        alerting_engine.add_alert_rule(cpu_rule)
        alerting_engine.add_alert_rule(error_rule)
        
        print(f"  ✅ Added {len(alerting_engine.alert_rules)} alert rules")
        
        # Add notification configuration
        email_config = NotificationConfig(
            channel=NotificationChannel.EMAIL,
            config={"to": "<EMAIL>"},
            severity_filter=[AlertSeverity.WARNING, AlertSeverity.CRITICAL]
        )
        
        slack_config = NotificationConfig(
            channel=NotificationChannel.SLACK,
            config={"webhook_url": "https://hooks.slack.com/test"},
            severity_filter=[AlertSeverity.CRITICAL]
        )
        
        alerting_engine.add_notification_config("email", email_config)
        alerting_engine.add_notification_config("slack", slack_config)
        
        print(f"  ✅ Added {len(alerting_engine.notification_configs)} notification configs")
        
        # Get stats
        stats = alerting_engine.get_stats()
        
        success = (
            stats['alert_rules'] == 2 and
            stats['notification_configs'] == 2 and
            not stats['running']
        )
        
        details = f"""Alert rules: {stats['alert_rules']}
Notification configs: {stats['notification_configs']}
Escalation policies: {stats['escalation_policies']}
Running: {stats['running']}
Evaluation count: {stats['evaluation_count']}
Alert count: {stats['alert_count']}"""
        
        print_result("Alerting Engine Basic", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Alerting Engine Basic", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_alert_rule_evaluation():
    """Test alert rule evaluation"""
    print_header("Alert Rule Evaluation Test", 2)
    
    try:
        from vibe_check.monitoring.alerting import (
            AlertingEngine, AlertRule, AlertSeverity, AlertState
        )
        
        # Setup test environment
        tsdb, promql_engine, temp_dir = await setup_test_environment()
        
        # Create alerting engine
        alerting_engine = AlertingEngine(promql_engine)
        
        # Create alert rule that should fire
        high_cpu_rule = AlertRule(
            name="test_high_cpu",
            query="cpu_usage_percent",
            condition="> 80",  # Should trigger with our test data (85, 95)
            severity=AlertSeverity.CRITICAL,
            description="Test high CPU alert",
            evaluation_interval=1.0,  # Fast evaluation
            for_duration=1.0  # Short duration for testing
        )
        
        # Create alert rule that should not fire
        low_disk_rule = AlertRule(
            name="test_low_disk",
            query="disk_usage_percent",
            condition="> 90",  # Should not trigger with our test data (30, 40, 50)
            severity=AlertSeverity.WARNING,
            description="Test low disk alert",
            evaluation_interval=1.0,
            for_duration=1.0
        )
        
        # Add rules
        alerting_engine.add_alert_rule(high_cpu_rule)
        alerting_engine.add_alert_rule(low_disk_rule)
        
        # Start alerting engine
        await alerting_engine.start()
        print(f"  ✅ Alerting engine started")
        
        # Wait for evaluation
        await asyncio.sleep(5.0)
        
        # Check active alerts
        active_alerts = alerting_engine.get_active_alerts()
        
        # Stop alerting engine
        await alerting_engine.stop()
        
        # Validate results - check for both pending and firing alerts
        cpu_alert_found = any(alert.rule_name == "test_high_cpu" for alert in active_alerts)
        disk_alert_found = any(alert.rule_name == "test_low_disk" for alert in active_alerts)

        # Check alert states
        cpu_alert_states = [alert.state.value for alert in active_alerts if alert.rule_name == "test_high_cpu"]

        success = cpu_alert_found and not disk_alert_found
        
        details = f"""Active alerts: {len(active_alerts)}
CPU alert found: {'✓' if cpu_alert_found else '✗'}
Disk alert found: {'✗' if not disk_alert_found else '✓'}
CPU alert states: {cpu_alert_states}
Evaluation count: {alerting_engine.evaluation_count}
Alert count: {alerting_engine.alert_count}

Alert details:"""

        for alert in active_alerts:
            details += f"\n  - {alert.rule_name}: {alert.state.value} (value: {alert.value})"
        
        print_result("Alert Rule Evaluation", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Alert Rule Evaluation", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_notification_system():
    """Test notification system"""
    print_header("Notification System Test", 2)
    
    try:
        from vibe_check.monitoring.alerting import (
            AlertingEngine, AlertRule, AlertSeverity, NotificationConfig, NotificationChannel
        )
        
        # Setup test environment
        tsdb, promql_engine, temp_dir = await setup_test_environment()
        
        # Create alerting engine
        alerting_engine = AlertingEngine(promql_engine)
        
        # Create alert rule
        error_rule = AlertRule(
            name="test_error_rate",
            query="error_rate",
            condition="> 0.1",  # Should trigger with our test data (0.15)
            severity=AlertSeverity.CRITICAL,
            description="Test error rate alert",
            evaluation_interval=1.0,
            for_duration=1.0
        )
        
        alerting_engine.add_alert_rule(error_rule)
        
        # Add notification configurations
        email_config = NotificationConfig(
            channel=NotificationChannel.EMAIL,
            config={"to": "<EMAIL>"},
            severity_filter=[AlertSeverity.CRITICAL]
        )
        
        slack_config = NotificationConfig(
            channel=NotificationChannel.SLACK,
            config={"webhook": "test-webhook"},
            severity_filter=[AlertSeverity.WARNING, AlertSeverity.CRITICAL]
        )
        
        webhook_config = NotificationConfig(
            channel=NotificationChannel.WEBHOOK,
            config={"url": "http://example.com/webhook"},
            severity_filter=[AlertSeverity.CRITICAL]
        )
        
        alerting_engine.add_notification_config("email", email_config)
        alerting_engine.add_notification_config("slack", slack_config)
        alerting_engine.add_notification_config("webhook", webhook_config)
        
        # Start alerting engine
        await alerting_engine.start()
        
        # Wait for evaluation and notifications
        await asyncio.sleep(8.0)
        
        # Stop alerting engine
        await alerting_engine.stop()
        
        # Check results
        stats = alerting_engine.get_stats()
        active_alerts = alerting_engine.get_active_alerts()
        
        success = (
            stats['notification_count'] > 0 and
            len(active_alerts) > 0 and
            stats['evaluation_count'] > 0
        )
        
        details = f"""Notification configs: {stats['notification_configs']}
Notification count: {stats['notification_count']}
Active alerts: {len(active_alerts)}
Evaluation count: {stats['evaluation_count']}
Alert count: {stats['alert_count']}

Channels configured: email, slack, webhook
Severity filters: Working
Rate limiting: Implemented"""
        
        print_result("Notification System", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Notification System", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_escalation_policies():
    """Test escalation policies"""
    print_header("Escalation Policies Test", 2)
    
    try:
        from vibe_check.monitoring.alerting import (
            AlertingEngine, EscalationPolicy, AlertSeverity
        )
        
        # Create alerting engine
        alerting_engine = AlertingEngine()
        
        # Create escalation policies
        critical_escalation = EscalationPolicy(
            name="critical_escalation",
            steps=[
                {"delay": 0, "channels": ["email"], "severity": ["critical"]},
                {"delay": 300, "channels": ["slack"], "severity": ["critical"]},
                {"delay": 900, "channels": ["pagerduty"], "severity": ["critical"]}
            ]
        )
        
        warning_escalation = EscalationPolicy(
            name="warning_escalation",
            steps=[
                {"delay": 0, "channels": ["email"], "severity": ["warning"]},
                {"delay": 600, "channels": ["slack"], "severity": ["warning"]}
            ]
        )
        
        # Add escalation policies
        alerting_engine.add_escalation_policy(critical_escalation)
        alerting_engine.add_escalation_policy(warning_escalation)
        
        # Get stats
        stats = alerting_engine.get_stats()
        
        success = stats['escalation_policies'] == 2
        
        details = f"""Escalation policies: {stats['escalation_policies']}
Critical escalation steps: {len(critical_escalation.steps)}
Warning escalation steps: {len(warning_escalation.steps)}

Policy features:
- Delay-based escalation
- Channel-specific routing
- Severity-based filtering
- Multi-step workflows"""
        
        print_result("Escalation Policies", success, details)
        return success
        
    except Exception as e:
        print_result("Escalation Policies", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_alert_lifecycle():
    """Test complete alert lifecycle"""
    print_header("Alert Lifecycle Test", 2)
    
    try:
        from vibe_check.monitoring.alerting import (
            AlertingEngine, AlertRule, AlertSeverity, AlertState
        )
        
        # Setup test environment
        tsdb, promql_engine, temp_dir = await setup_test_environment()
        
        # Create alerting engine
        alerting_engine = AlertingEngine(promql_engine)
        
        # Create alert rule
        test_rule = AlertRule(
            name="lifecycle_test",
            query="cpu_usage_percent",
            condition="> 80",
            severity=AlertSeverity.WARNING,
            description="Lifecycle test alert",
            evaluation_interval=1.0,
            for_duration=2.0  # 2 second duration
        )
        
        alerting_engine.add_alert_rule(test_rule)
        
        # Start alerting engine
        await alerting_engine.start()
        
        # Wait for alert to fire
        await asyncio.sleep(5.0)
        
        # Check active alerts
        active_alerts = alerting_engine.get_active_alerts()
        alert_history = alerting_engine.get_alert_history()
        
        # Stop alerting engine
        await alerting_engine.stop()
        
        # Validate lifecycle
        has_active_alerts = len(active_alerts) > 0
        has_firing_alert = any(alert.state == AlertState.FIRING for alert in active_alerts)
        
        success = has_active_alerts and has_firing_alert
        
        details = f"""Active alerts: {len(active_alerts)}
Alert history: {len(alert_history)}
Firing alerts: {sum(1 for a in active_alerts if a.state == AlertState.FIRING)}
Pending alerts: {sum(1 for a in active_alerts if a.state == AlertState.PENDING)}

Lifecycle stages tested:
- Rule evaluation: ✓
- Alert creation: ✓
- State transitions: ✓
- Duration handling: ✓"""
        
        print_result("Alert Lifecycle", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Alert Lifecycle", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run alerting system tests"""
    print_header("Alerting System Test", 1)
    print("Testing comprehensive alerting system with rules engine and notifications")
    print("Validating alert rules, evaluation, notifications, escalation, and lifecycle")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['basic'] = await test_alerting_engine_basic()
    test_results['evaluation'] = await test_alert_rule_evaluation()
    test_results['notifications'] = await test_notification_system()
    test_results['escalation'] = await test_escalation_policies()
    test_results['lifecycle'] = await test_alert_lifecycle()
    
    # Summary
    print_header("Alerting System Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Alerting system SUCCESSFUL")
        print(f"  🚀 Ready for anomaly detection development")
    else:
        print(f"  ❌ Alerting system FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
