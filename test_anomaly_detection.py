#!/usr/bin/env python3
"""
Anomaly Detection Test
======================

Test comprehensive anomaly detection system with statistical and ML algorithms.
"""

import asyncio
import time
import math
import random
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

def generate_normal_data(length: int, base_value: float = 100.0, noise: float = 5.0) -> list:
    """Generate normal time series data with noise"""
    data = []
    for i in range(length):
        # Add some trend and seasonality
        trend = i * 0.1
        seasonal = 10 * math.sin(2 * math.pi * i / 24)  # Daily pattern
        noise_val = random.gauss(0, noise)
        value = base_value + trend + seasonal + noise_val
        data.append(max(0, value))  # Ensure non-negative
    return data

def inject_anomalies(data: list, anomaly_positions: list, anomaly_magnitude: float = 50.0) -> list:
    """Inject anomalies into normal data"""
    anomalous_data = data.copy()
    for pos in anomaly_positions:
        if 0 <= pos < len(anomalous_data):
            # Add spike anomaly
            anomalous_data[pos] += anomaly_magnitude
    return anomalous_data

async def test_statistical_detection():
    """Test statistical anomaly detection methods"""
    print_header("Statistical Anomaly Detection Test", 2)
    
    try:
        from vibe_check.monitoring.anomaly import (
            AnomalyDetector, AnomalyConfig, DetectionMethod, StatisticalDetector
        )
        
        # Test Z-score detection
        normal_data = [10, 12, 11, 13, 10, 11, 12, 100, 11, 10]  # 100 is anomaly
        z_anomalies = StatisticalDetector.z_score_detection(normal_data, threshold=2.0)
        
        z_success = len(z_anomalies) > 0 and any(idx == 7 for idx, _ in z_anomalies)
        
        # Test IQR detection
        iqr_anomalies = StatisticalDetector.iqr_detection(normal_data, multiplier=1.5)
        iqr_success = len(iqr_anomalies) > 0 and any(idx == 7 for idx, _ in iqr_anomalies)
        
        success = z_success and iqr_success
        
        details = f"""Z-score detection: {len(z_anomalies)} anomalies found
Anomaly at index 7: {'✓' if z_success else '✗'}
IQR detection: {len(iqr_anomalies)} anomalies found
Anomaly at index 7: {'✓' if iqr_success else '✗'}

Test data: {normal_data}
Expected anomaly at index 7 (value: 100)"""
        
        print_result("Statistical Detection", success, details)
        return success
        
    except Exception as e:
        print_result("Statistical Detection", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_moving_average_detection():
    """Test moving average anomaly detection"""
    print_header("Moving Average Anomaly Detection Test", 2)
    
    try:
        from vibe_check.monitoring.anomaly import MovingAverageDetector
        
        # Generate data with anomaly
        base_data = [50] * 30  # Stable baseline
        anomaly_data = base_data + [150] + [50] * 10  # Spike at position 30
        
        # Test simple moving average
        ma_anomalies = MovingAverageDetector.simple_moving_average(
            anomaly_data, window=20, threshold=2.0
        )
        
        ma_success = len(ma_anomalies) > 0 and any(idx == 30 for idx, _ in ma_anomalies)
        
        # Test exponential moving average
        ema_anomalies = MovingAverageDetector.exponential_moving_average(
            anomaly_data, alpha=0.3, threshold=2.0
        )
        
        ema_success = len(ema_anomalies) > 0
        
        success = ma_success and ema_success
        
        details = f"""Simple MA detection: {len(ma_anomalies)} anomalies found
Anomaly at index 30: {'✓' if ma_success else '✗'}
Exponential MA detection: {len(ema_anomalies)} anomalies found
EMA anomalies found: {'✓' if ema_success else '✗'}

Data length: {len(anomaly_data)}
Expected anomaly at index 30 (value: 150)"""
        
        print_result("Moving Average Detection", success, details)
        return success
        
    except Exception as e:
        print_result("Moving Average Detection", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_seasonal_detection():
    """Test seasonal anomaly detection"""
    print_header("Seasonal Anomaly Detection Test", 2)
    
    try:
        from vibe_check.monitoring.anomaly import SeasonalDetector
        
        # Generate seasonal data (24-hour pattern)
        timestamps = []
        values = []
        base_time = time.time()
        
        for i in range(48):  # 2 days of hourly data
            timestamp = base_time + i * 3600  # Hourly intervals
            hour = i % 24
            
            # Normal seasonal pattern (higher during day, lower at night)
            seasonal_value = 50 + 30 * math.sin(2 * math.pi * hour / 24)
            
            # Inject anomaly at hour 12 of second day
            if i == 36:  # Hour 12 of second day
                seasonal_value += 100  # Anomaly
            
            timestamps.append(timestamp)
            values.append(seasonal_value)
        
        # Test seasonal detection
        seasonal_anomalies = SeasonalDetector.seasonal_decomposition(
            values, timestamps, period=24, threshold=2.0
        )
        
        success = len(seasonal_anomalies) > 0 and any(idx == 36 for idx, _ in seasonal_anomalies)
        
        details = f"""Seasonal detection: {len(seasonal_anomalies)} anomalies found
Anomaly at index 36: {'✓' if success else '✗'}
Data points: {len(values)}
Seasonal period: 24 hours

Anomaly details:"""
        
        for idx, confidence in seasonal_anomalies:
            details += f"\n  - Index {idx}: confidence {confidence:.2f}"
        
        print_result("Seasonal Detection", success, details)
        return success
        
    except Exception as e:
        print_result("Seasonal Detection", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_anomaly_detector_integration():
    """Test integrated anomaly detector"""
    print_header("Anomaly Detector Integration Test", 2)
    
    try:
        from vibe_check.monitoring.anomaly import (
            AnomalyDetector, AnomalyConfig, DetectionMethod
        )
        
        # Create detector with statistical method
        config = AnomalyConfig(
            method=DetectionMethod.STATISTICAL,
            z_score_threshold=2.0,
            min_samples=10
        )
        detector = AnomalyDetector(config)
        
        # Generate normal data
        base_time = time.time()
        normal_values = generate_normal_data(50, base_value=100.0, noise=5.0)
        
        # Inject anomalies at specific positions
        anomaly_positions = [25, 35, 45]
        anomalous_values = inject_anomalies(normal_values, anomaly_positions, anomaly_magnitude=50.0)
        
        # Feed data to detector
        for i, value in enumerate(anomalous_values):
            timestamp = base_time + i * 60  # 1-minute intervals
            await detector.add_data_point(
                metric_name="test_metric",
                timestamp=timestamp,
                value=value,
                labels={"instance": "test"}
            )
        
        # Get detected anomalies
        detected_anomalies = detector.get_anomalies()
        stats = detector.get_stats()
        
        # Check if anomalies were detected
        detected_count = len(detected_anomalies)
        success = detected_count > 0
        
        details = f"""Total data points: {len(anomalous_values)}
Injected anomalies: {len(anomaly_positions)} at positions {anomaly_positions}
Detected anomalies: {detected_count}
Detection method: {config.method.value}

Statistics:
- Total anomalies: {stats['total_anomalies']}
- Monitored metrics: {stats['monitored_metrics']}
- Detection enabled: {stats['config']['enabled']}

Detected anomaly details:"""
        
        for anomaly in detected_anomalies[:5]:  # Show first 5
            details += f"\n  - Time: {anomaly.timestamp:.0f}, Value: {anomaly.value:.1f}, Confidence: {anomaly.confidence:.2f}"
        
        print_result("Anomaly Detector Integration", success, details)
        return success
        
    except Exception as e:
        print_result("Anomaly Detector Integration", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_multiple_detection_methods():
    """Test multiple detection methods"""
    print_header("Multiple Detection Methods Test", 2)
    
    try:
        from vibe_check.monitoring.anomaly import (
            AnomalyDetector, AnomalyConfig, DetectionMethod
        )
        
        # Test data
        base_time = time.time()
        test_data = generate_normal_data(100, base_value=50.0, noise=3.0)
        anomalous_data = inject_anomalies(test_data, [30, 60, 90], anomaly_magnitude=30.0)
        
        results = {}
        
        # Test each detection method
        methods = [
            DetectionMethod.STATISTICAL,
            DetectionMethod.MOVING_AVERAGE,
            DetectionMethod.SEASONAL
        ]
        
        for method in methods:
            config = AnomalyConfig(
                method=method,
                min_samples=20,
                window_size=50
            )
            detector = AnomalyDetector(config)
            
            # Feed data
            for i, value in enumerate(anomalous_data):
                timestamp = base_time + i * 300  # 5-minute intervals
                await detector.add_data_point(
                    metric_name=f"test_metric_{method.value}",
                    timestamp=timestamp,
                    value=value
                )
            
            # Get results
            anomalies = detector.get_anomalies()
            results[method.value] = len(anomalies)
        
        # Check if all methods detected anomalies
        success = all(count > 0 for count in results.values())
        
        details = f"""Test data points: {len(anomalous_data)}
Injected anomalies: 3 at positions [30, 60, 90]

Detection results:"""
        
        for method, count in results.items():
            details += f"\n  - {method}: {count} anomalies detected"
        
        details += f"\n\nAll methods detected anomalies: {'✓' if success else '✗'}"
        
        print_result("Multiple Detection Methods", success, details)
        return success
        
    except Exception as e:
        print_result("Multiple Detection Methods", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_anomaly_confidence_scoring():
    """Test anomaly confidence scoring"""
    print_header("Anomaly Confidence Scoring Test", 2)
    
    try:
        from vibe_check.monitoring.anomaly import (
            AnomalyDetector, AnomalyConfig, DetectionMethod
        )
        
        config = AnomalyConfig(
            method=DetectionMethod.STATISTICAL,
            z_score_threshold=1.5,  # Lower threshold for more sensitivity
            min_samples=10
        )
        detector = AnomalyDetector(config)
        
        # Generate data with varying anomaly magnitudes
        base_time = time.time()
        base_values = [50.0] * 30
        
        # Add anomalies of different magnitudes
        test_values = base_values + [
            60.0,   # Small anomaly
            80.0,   # Medium anomaly
            120.0,  # Large anomaly
        ] + [50.0] * 10
        
        # Feed data
        for i, value in enumerate(test_values):
            timestamp = base_time + i * 60
            await detector.add_data_point(
                metric_name="confidence_test",
                timestamp=timestamp,
                value=value
            )
        
        # Get anomalies and check confidence scores
        anomalies = detector.get_anomalies()
        
        # Sort by confidence
        anomalies.sort(key=lambda x: x.confidence, reverse=True)
        
        success = (
            len(anomalies) >= 3 and  # At least 3 anomalies detected
            all(0.0 <= a.confidence <= 1.0 for a in anomalies) and  # Valid confidence range
            anomalies[0].confidence > anomalies[-1].confidence  # Highest confidence first
        )
        
        details = f"""Total anomalies detected: {len(anomalies)}
Confidence range validation: {'✓' if all(0.0 <= a.confidence <= 1.0 for a in anomalies) else '✗'}

Top anomalies by confidence:"""
        
        for i, anomaly in enumerate(anomalies[:5]):
            details += f"\n  {i+1}. Value: {anomaly.value:.1f}, Confidence: {anomaly.confidence:.3f}, Deviation: {anomaly.deviation:.1f}"
        
        print_result("Anomaly Confidence Scoring", success, details)
        return success
        
    except Exception as e:
        print_result("Anomaly Confidence Scoring", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run anomaly detection tests"""
    print_header("Anomaly Detection System Test", 1)
    print("Testing comprehensive anomaly detection with statistical and ML algorithms")
    print("Validating detection methods, confidence scoring, and integration capabilities")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['statistical'] = await test_statistical_detection()
    test_results['moving_average'] = await test_moving_average_detection()
    test_results['seasonal'] = await test_seasonal_detection()
    test_results['integration'] = await test_anomaly_detector_integration()
    test_results['multiple_methods'] = await test_multiple_detection_methods()
    test_results['confidence'] = await test_anomaly_confidence_scoring()
    
    # Summary
    print_header("Anomaly Detection Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Anomaly detection system SUCCESSFUL")
        print(f"  🚀 Ready for performance optimization development")
    else:
        print(f"  ❌ Anomaly detection system FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
