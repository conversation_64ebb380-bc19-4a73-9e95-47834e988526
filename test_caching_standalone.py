#!/usr/bin/env python3
"""
Standalone Caching Test
=======================

Standalone test for caching implementation that directly imports only the caching modules.
"""

import asyncio
import time
import sys
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))


async def test_memory_cache_standalone():
    """Test memory cache functionality standalone"""
    print("🧪 Testing Memory Cache (Standalone)")
    print("=" * 36)
    
    try:
        # Direct import of cache engine
        from vibe_check.core.caching.cache_engine import LRUMemoryCache, CacheConfig
        
        config = CacheConfig(memory_cache_size=5, memory_cache_ttl=10.0)
        cache = LRUMemoryCache(config)
        
        # Test basic operations
        await cache.set("key1", "value1")
        await cache.set("key2", {"complex": "data", "number": 42})
        
        value1 = await cache.get("key1")
        value2 = await cache.get("key2")
        
        print(f"  ✅ Basic operations:")
        print(f"    • String value: {value1 == 'value1'}")
        print(f"    • Complex value: {value2 == {'complex': 'data', 'number': 42}}")
        
        # Test LRU eviction
        for i in range(3, 10):
            await cache.set(f"key{i}", f"value{i}")
        
        # key1 and key2 should be evicted
        evicted1 = await cache.get("key1")
        evicted2 = await cache.get("key2")
        recent = await cache.get("key9")
        
        print(f"  ✅ LRU eviction:")
        print(f"    • Old keys evicted: {evicted1 is None and evicted2 is None}")
        print(f"    • Recent key preserved: {recent == 'value9'}")
        
        # Test TTL
        await cache.set("ttl_test", "ttl_value", ttl=0.1)
        immediate = await cache.get("ttl_test")
        await asyncio.sleep(0.2)
        expired = await cache.get("ttl_test")
        
        print(f"  ✅ TTL expiration:")
        print(f"    • Immediate: {immediate == 'ttl_value'}")
        print(f"    • Expired: {expired is None}")
        
        # Test statistics
        stats = cache.get_stats()
        print(f"  📊 Statistics:")
        print(f"    • Hits: {stats.hits}")
        print(f"    • Misses: {stats.misses}")
        print(f"    • Hit ratio: {stats.hit_ratio:.2f}")
        
        return {
            'basic_ops': value1 == 'value1' and value2 == {'complex': 'data', 'number': 42},
            'lru_eviction': evicted1 is None and evicted2 is None and recent == 'value9',
            'ttl_expiration': immediate == 'ttl_value' and expired is None,
            'hit_ratio': stats.hit_ratio
        }
        
    except Exception as e:
        print(f"❌ Memory cache test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_disk_cache_standalone():
    """Test disk cache functionality standalone"""
    print("\n🧪 Testing Disk Cache (Standalone)")
    print("=" * 34)
    
    try:
        from vibe_check.core.caching.cache_engine import DiskCache, CacheConfig
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config = CacheConfig(
                disk_cache_enabled=True,
                disk_cache_dir=Path(temp_dir),
                disk_cache_ttl=10.0,
                compression_enabled=True
            )
            cache = DiskCache(config)
            
            # Test complex data storage
            test_data = {
                "string": "test_value",
                "number": 12345,
                "list": [1, 2, 3, "four"],
                "nested": {"inner": {"deep": "value"}}
            }
            
            await cache.set("complex_data", test_data)
            await cache.set("simple_string", "hello_world")
            
            # Retrieve data
            retrieved_complex = await cache.get("complex_data")
            retrieved_simple = await cache.get("simple_string")
            
            print(f"  ✅ Data storage:")
            print(f"    • Complex data: {retrieved_complex == test_data}")
            print(f"    • Simple string: {retrieved_simple == 'hello_world'}")
            
            # Test persistence (simulate restart)
            cache.cleanup()
            cache2 = DiskCache(config)
            
            persistent_complex = await cache2.get("complex_data")
            persistent_simple = await cache2.get("simple_string")
            
            print(f"  ✅ Persistence:")
            print(f"    • Complex data persisted: {persistent_complex == test_data}")
            print(f"    • Simple string persisted: {persistent_simple == 'hello_world'}")
            
            # Test TTL expiration
            await cache2.set("ttl_test", "will_expire", ttl=0.1)
            immediate = await cache2.get("ttl_test")
            await asyncio.sleep(0.2)
            expired = await cache2.get("ttl_test")
            
            print(f"  ✅ TTL expiration:")
            print(f"    • Immediate: {immediate == 'will_expire'}")
            print(f"    • Expired: {expired is None}")
            
            cache2.cleanup()
            
            return {
                'data_storage': retrieved_complex == test_data and retrieved_simple == 'hello_world',
                'persistence': persistent_complex == test_data and persistent_simple == 'hello_world',
                'ttl_expiration': immediate == 'will_expire' and expired is None
            }
    
    except Exception as e:
        print(f"❌ Disk cache test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_multi_level_cache_standalone():
    """Test multi-level cache standalone"""
    print("\n🧪 Testing Multi-Level Cache (Standalone)")
    print("=" * 42)
    
    try:
        from vibe_check.core.caching.cache_engine import MultiLevelCache, CacheConfig
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config = CacheConfig(
                memory_cache_size=3,
                memory_cache_ttl=10.0,
                disk_cache_enabled=True,
                disk_cache_dir=Path(temp_dir),
                disk_cache_ttl=20.0
            )
            cache = MultiLevelCache(config)
            
            # Fill memory cache
            await cache.set("mem1", "memory_value_1")
            await cache.set("mem2", "memory_value_2")
            await cache.set("mem3", "memory_value_3")
            
            # Verify all in memory
            mem1 = await cache.get("mem1")  # Memory hit
            mem2 = await cache.get("mem2")  # Memory hit
            
            print(f"  ✅ Memory cache population:")
            print(f"    • mem1: {mem1 == 'memory_value_1'}")
            print(f"    • mem2: {mem2 == 'memory_value_2'}")
            
            # Add more items to trigger eviction
            await cache.set("mem4", "memory_value_4")
            await cache.set("mem5", "memory_value_5")
            
            # mem1 should be evicted from memory but available from disk
            mem1_from_disk = await cache.get("mem1")  # Should hit disk, then promote to memory
            mem5_from_memory = await cache.get("mem5")  # Should hit memory
            
            print(f"  ✅ Multi-level retrieval:")
            print(f"    • mem1 from disk: {mem1_from_disk == 'memory_value_1'}")
            print(f"    • mem5 from memory: {mem5_from_memory == 'memory_value_5'}")
            
            # Test cache statistics
            stats = cache.get_combined_stats()
            
            print(f"  📊 Combined statistics:")
            print(f"    • Memory hits: {stats['memory']['hits']}")
            print(f"    • Disk hits: {stats['disk']['hits']}")
            print(f"    • Total hit ratio: {stats['combined']['total_hit_ratio']:.2f}")
            
            cache.cleanup()
            
            return {
                'memory_population': mem1 == 'memory_value_1' and mem2 == 'memory_value_2',
                'multi_level_retrieval': mem1_from_disk == 'memory_value_1' and mem5_from_memory == 'memory_value_5',
                'hit_ratio': stats['combined']['total_hit_ratio']
            }
    
    except Exception as e:
        print(f"❌ Multi-level cache test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_cache_performance_standalone():
    """Test cache performance standalone"""
    print("\n🧪 Testing Cache Performance (Standalone)")
    print("=" * 41)
    
    try:
        from vibe_check.core.caching.cache_engine import MultiLevelCache, CacheConfig
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config = CacheConfig(
                memory_cache_size=200,
                disk_cache_enabled=True,
                disk_cache_dir=Path(temp_dir),
                compression_enabled=False,  # Disable for speed
                async_write_enabled=True
            )
            cache = MultiLevelCache(config)
            
            # Test write performance
            print("  🔄 Write performance test...")
            write_start = time.time()
            
            write_tasks = []
            for i in range(200):
                task = cache.set(f"perf_key_{i}", f"performance_value_{i}_{'x' * 50}")
                write_tasks.append(task)
            
            await asyncio.gather(*write_tasks)
            write_time = time.time() - write_start
            write_ops_per_sec = 200 / write_time
            
            print(f"    • Operations: 200")
            print(f"    • Time: {write_time:.3f}s")
            print(f"    • Speed: {write_ops_per_sec:.1f} ops/sec")
            
            # Test read performance
            print("  ⚡ Read performance test...")
            read_start = time.time()
            
            read_tasks = []
            for i in range(200):
                task = cache.get(f"perf_key_{i}")
                read_tasks.append(task)
            
            read_results = await asyncio.gather(*read_tasks)
            read_time = time.time() - read_start
            read_ops_per_sec = 200 / read_time
            
            successful_reads = sum(1 for r in read_results if r is not None)
            
            print(f"    • Operations: 200")
            print(f"    • Successful: {successful_reads}")
            print(f"    • Time: {read_time:.3f}s")
            print(f"    • Speed: {read_ops_per_sec:.1f} ops/sec")
            
            # Test mixed workload
            print("  🔀 Mixed workload test...")
            mixed_start = time.time()
            
            mixed_tasks = []
            for i in range(100):
                if i % 3 == 0:  # 33% writes
                    task = cache.set(f"mixed_key_{i}", f"mixed_value_{i}")
                else:  # 67% reads
                    task = cache.get(f"perf_key_{i % 200}")
                mixed_tasks.append(task)
            
            await asyncio.gather(*mixed_tasks)
            mixed_time = time.time() - mixed_start
            mixed_ops_per_sec = 100 / mixed_time
            
            print(f"    • Operations: 100")
            print(f"    • Time: {mixed_time:.3f}s")
            print(f"    • Speed: {mixed_ops_per_sec:.1f} ops/sec")
            
            cache.cleanup()
            
            return {
                'write_ops_per_sec': write_ops_per_sec,
                'read_ops_per_sec': read_ops_per_sec,
                'mixed_ops_per_sec': mixed_ops_per_sec,
                'read_success_rate': successful_reads / 200
            }
    
    except Exception as e:
        print(f"❌ Cache performance test failed: {e}")
        return {}


async def test_cache_invalidation_standalone():
    """Test cache invalidation standalone"""
    print("\n🧪 Testing Cache Invalidation (Standalone)")
    print("=" * 42)
    
    try:
        from vibe_check.core.caching.cache_engine import MultiLevelCache, CacheConfig
        from vibe_check.core.caching.cache_invalidation import SmartCacheManager
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config = CacheConfig(
                memory_cache_size=20,
                disk_cache_enabled=True,
                disk_cache_dir=Path(temp_dir),
                dependency_tracking=True,
                file_watch_enabled=False  # Disable file watching for simplicity
            )
            
            cache = MultiLevelCache(config)
            manager = SmartCacheManager(cache, config)
            
            # Test basic invalidation
            await manager.set("basic1", "value1")
            await manager.set("basic2", "value2")
            await manager.set("basic3", "value3")
            
            # Verify initial state
            val1 = await manager.get("basic1")
            val2 = await manager.get("basic2")
            val3 = await manager.get("basic3")
            
            print(f"  ✅ Initial state:")
            print(f"    • All values set: {all([val1 == 'value1', val2 == 'value2', val3 == 'value3'])}")
            
            # Test single invalidation
            invalidated = await manager.invalidate("basic2")
            
            val1_after = await manager.get("basic1")
            val2_after = await manager.get("basic2")
            val3_after = await manager.get("basic3")
            
            print(f"  ✅ Single invalidation:")
            print(f"    • Invalidated count: {invalidated}")
            print(f"    • basic1 preserved: {val1_after == 'value1'}")
            print(f"    • basic2 invalidated: {val2_after is None}")
            print(f"    • basic3 preserved: {val3_after == 'value3'}")
            
            # Test dependency invalidation
            await manager.set("parent", "parent_val", dependencies=["file1.py", "file2.py"])
            await manager.set("child1", "child1_val", dependencies=["file1.py"])
            await manager.set("child2", "child2_val", dependencies=["file2.py"])
            await manager.set("independent", "independent_val")
            
            # Invalidate by dependency
            dep_invalidated = await manager.invalidate_dependency("file1.py")
            
            parent_after = await manager.get("parent")
            child1_after = await manager.get("child1")
            child2_after = await manager.get("child2")
            independent_after = await manager.get("independent")
            
            print(f"  ✅ Dependency invalidation:")
            print(f"    • Dependency invalidated: {dep_invalidated}")
            print(f"    • Parent invalidated: {parent_after is None}")
            print(f"    • Child1 invalidated: {child1_after is None}")
            print(f"    • Child2 preserved: {child2_after == 'child2_val'}")
            print(f"    • Independent preserved: {independent_after == 'independent_val'}")
            
            # Test statistics
            stats = manager.get_stats()
            print(f"  📊 Invalidation stats:")
            print(f"    • Total invalidations: {stats['invalidation']['invalidations_count']}")
            print(f"    • Dependencies tracked: {stats['invalidation']['dependency_count']}")
            
            manager.cleanup()
            
            return {
                'basic_invalidation': val2_after is None and val1_after == 'value1' and val3_after == 'value3',
                'dependency_invalidation': all([
                    dep_invalidated >= 2,
                    parent_after is None,
                    child1_after is None,
                    child2_after == 'child2_val',
                    independent_after == 'independent_val'
                ]),
                'invalidation_count': stats['invalidation']['invalidations_count']
            }
    
    except Exception as e:
        print(f"❌ Cache invalidation test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def main():
    """Main test function"""
    print("🚀 Standalone Caching Implementation Test - Task 3.2")
    print("=" * 60)
    
    # Run all tests
    memory_results = await test_memory_cache_standalone()
    disk_results = await test_disk_cache_standalone()
    multi_level_results = await test_multi_level_cache_standalone()
    performance_results = await test_cache_performance_standalone()
    invalidation_results = await test_cache_invalidation_standalone()
    
    print("\n" + "=" * 60)
    print("📊 STANDALONE CACHING IMPLEMENTATION SUMMARY")
    print("=" * 60)
    
    # Evaluate results
    targets_met = 0
    total_targets = 6
    
    # Target 1: Memory cache functionality
    if (memory_results.get('basic_ops') and 
        memory_results.get('lru_eviction') and 
        memory_results.get('ttl_expiration')):
        print("  ✅ Memory cache functionality complete")
        targets_met += 1
    else:
        print("  ❌ Memory cache functionality issues")
    
    # Target 2: Disk cache functionality
    if (disk_results.get('data_storage') and 
        disk_results.get('persistence') and 
        disk_results.get('ttl_expiration')):
        print("  ✅ Disk cache functionality complete")
        targets_met += 1
    else:
        print("  ❌ Disk cache functionality issues")
    
    # Target 3: Multi-level cache integration
    if (multi_level_results.get('memory_population') and 
        multi_level_results.get('multi_level_retrieval')):
        print("  ✅ Multi-level cache integration working")
        targets_met += 1
    else:
        print("  ❌ Multi-level cache integration issues")
    
    # Target 4: Cache performance
    if performance_results.get('read_ops_per_sec', 0) >= 1000:
        print(f"  ✅ Cache performance: {performance_results.get('read_ops_per_sec', 0):.1f} ops/sec")
        targets_met += 1
    else:
        print(f"  ⚠️  Cache performance: {performance_results.get('read_ops_per_sec', 0):.1f} ops/sec (target: 1000)")
    
    # Target 5: Cache invalidation
    if (invalidation_results.get('basic_invalidation') and 
        invalidation_results.get('dependency_invalidation')):
        print("  ✅ Cache invalidation working")
        targets_met += 1
    else:
        print("  ❌ Cache invalidation issues")
    
    # Target 6: Overall system integration
    if all([memory_results, disk_results, multi_level_results, performance_results, invalidation_results]):
        print("  ✅ Overall system integration working")
        targets_met += 1
    else:
        print("  ❌ Overall system integration issues")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 5:
        print("✅ Task 3.2: Multi-Level Caching Implementation SUCCESSFUL")
        print("🚀 Ready to proceed with Week 4: Monitoring Infrastructure Foundation")
        
        # Show key achievements
        print(f"\n🏆 Key Achievements:")
        if performance_results:
            print(f"  • Read performance: {performance_results.get('read_ops_per_sec', 0):.1f} ops/sec")
            print(f"  • Write performance: {performance_results.get('write_ops_per_sec', 0):.1f} ops/sec")
            print(f"  • Mixed workload: {performance_results.get('mixed_ops_per_sec', 0):.1f} ops/sec")
        if multi_level_results:
            print(f"  • Multi-level hit ratio: {multi_level_results.get('hit_ratio', 0):.2f}")
        if invalidation_results:
            print(f"  • Invalidations processed: {invalidation_results.get('invalidation_count', 0)}")
        print(f"  • LRU memory cache with TTL support")
        print(f"  • Compressed disk cache with persistence")
        print(f"  • Intelligent dependency tracking")
        print(f"  • Async cache operations throughout")
        print(f"  • Multi-level cache hierarchy (memory → disk)")
        
        return 0
    else:
        print("⚠️  Task 3.2: Multi-Level Caching Implementation needs improvement")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
