#!/usr/bin/env python3
"""
Data Compression and Retention Test
===================================

Test data compression and automated retention policies for historical data.
"""

import asyncio
import json
import tempfile
import shutil
import time
import os
from pathlib import Path
from typing import List
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

def create_test_files(data_dir: Path, count: int = 10) -> List[Path]:
    """Create test files for compression/retention testing"""
    files = []
    
    for i in range(count):
        file_path = data_dir / f"test_data_{i}.json"
        
        # Create test data
        test_data = {
            "timestamp": time.time() - (i * 3600),  # Files from different hours
            "metrics": [
                {"name": f"metric_{j}", "value": j * i, "labels": {"test": "compression"}}
                for j in range(100)  # Make files reasonably large
            ]
        }
        
        with open(file_path, 'w') as f:
            json.dump(test_data, f, indent=2)
        
        # Set file modification time to simulate age
        old_time = time.time() - (i * 3600)
        os.utime(file_path, (old_time, old_time))
        
        files.append(file_path)
    
    return files

async def test_compression_basic():
    """Test basic compression functionality"""
    print_header("Basic Compression Test", 2)
    
    try:
        from vibe_check.monitoring.storage.compression_manager import (
            CompressionManager, CompressionConfig, CompressionType
        )
        
        # Create temporary directory
        temp_dir = Path(tempfile.mkdtemp())
        
        # Create compression manager
        compression_config = CompressionConfig(
            enabled=True,
            compression_type=CompressionType.GZIP,
            compress_after_hours=0.0,  # Compress immediately
            min_file_size_bytes=100
        )
        
        manager = CompressionManager(temp_dir, compression_config)
        
        # Create test files
        test_files = create_test_files(temp_dir, 5)
        print(f"  ✅ Created {len(test_files)} test files")
        
        # Get initial storage summary
        initial_summary = manager.get_storage_summary()
        
        # Force compression
        compression_result = await manager.force_compression()
        
        # Get final storage summary
        final_summary = manager.get_storage_summary()
        stats = manager.get_stats()
        
        success = (
            stats['files_compressed'] > 0 and
            final_summary['compressed_files'] > 0 and
            stats['bytes_saved'] > 0
        )
        
        details = f"""Initial files: {initial_summary['total_files']}
Files compressed: {stats['files_compressed']}
Compressed files: {final_summary['compressed_files']}
Bytes saved: {stats['bytes_saved']}
Compression ratio: {stats['compression_ratio']:.2%}
Duration: {compression_result['duration_seconds']:.2f}s"""
        
        print_result("Basic Compression", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Basic Compression", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_time_based_retention():
    """Test time-based retention policy"""
    print_header("Time-Based Retention Test", 2)
    
    try:
        from vibe_check.monitoring.storage.compression_manager import (
            CompressionManager, RetentionConfig, RetentionPolicy
        )
        
        # Create temporary directory
        temp_dir = Path(tempfile.mkdtemp())
        
        # Create retention manager
        retention_config = RetentionConfig(
            policy=RetentionPolicy.TIME_BASED,
            max_age_days=0.001,  # Very short retention (about 1.4 minutes)
            cleanup_interval_hours=0.1
        )
        
        manager = CompressionManager(temp_dir, retention_config=retention_config)
        
        # Create test files with different ages
        test_files = create_test_files(temp_dir, 8)
        print(f"  ✅ Created {len(test_files)} test files with different ages")
        
        # Get initial summary
        initial_summary = manager.get_storage_summary()
        
        # Force cleanup
        cleanup_result = await manager.force_cleanup()
        
        # Get final summary
        final_summary = manager.get_storage_summary()
        stats = manager.get_stats()
        
        success = (
            stats['files_deleted'] > 0 and
            final_summary['total_files'] < initial_summary['total_files']
        )
        
        details = f"""Initial files: {initial_summary['total_files']}
Final files: {final_summary['total_files']}
Files deleted: {stats['files_deleted']}
Bytes deleted: {stats['bytes_deleted']}
Duration: {cleanup_result['duration_seconds']:.2f}s"""
        
        print_result("Time-Based Retention", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Time-Based Retention", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_size_based_retention():
    """Test size-based retention policy"""
    print_header("Size-Based Retention Test", 2)
    
    try:
        from vibe_check.monitoring.storage.compression_manager import (
            CompressionManager, RetentionConfig, RetentionPolicy
        )
        
        # Create temporary directory
        temp_dir = Path(tempfile.mkdtemp())
        
        # Create retention manager with small size limit
        retention_config = RetentionConfig(
            policy=RetentionPolicy.SIZE_BASED,
            max_size_gb=0.001,  # Very small limit (1MB)
            cleanup_interval_hours=0.1
        )
        
        manager = CompressionManager(temp_dir, retention_config=retention_config)
        
        # Create many test files to exceed size limit
        test_files = create_test_files(temp_dir, 15)
        print(f"  ✅ Created {len(test_files)} test files")
        
        # Get initial summary
        initial_summary = manager.get_storage_summary()
        
        # Force cleanup
        cleanup_result = await manager.force_cleanup()
        
        # Get final summary
        final_summary = manager.get_storage_summary()
        stats = manager.get_stats()
        
        success = (
            stats['files_deleted'] > 0 and
            final_summary['total_size_mb'] < initial_summary['total_size_mb']
        )
        
        details = f"""Initial files: {initial_summary['total_files']}
Final files: {final_summary['total_files']}
Initial size: {initial_summary['total_size_mb']:.2f}MB
Final size: {final_summary['total_size_mb']:.2f}MB
Files deleted: {stats['files_deleted']}
Size limit: {(retention_config.max_size_gb or 0) * 1024:.1f}MB"""
        
        print_result("Size-Based Retention", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Size-Based Retention", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_background_operations():
    """Test background compression and retention operations"""
    print_header("Background Operations Test", 2)
    
    try:
        from vibe_check.monitoring.storage.compression_manager import (
            CompressionManager, CompressionConfig, RetentionConfig,
            CompressionType, RetentionPolicy
        )
        
        # Create temporary directory
        temp_dir = Path(tempfile.mkdtemp())
        
        # Create manager with fast background operations
        compression_config = CompressionConfig(
            enabled=True,
            compress_after_hours=0.001,  # Very fast compression
            min_file_size_bytes=100
        )
        
        retention_config = RetentionConfig(
            policy=RetentionPolicy.COUNT_BASED,
            max_files=5,  # Keep only 5 files
            cleanup_interval_hours=0.001  # Very fast cleanup
        )
        
        manager = CompressionManager(
            temp_dir, 
            compression_config, 
            retention_config
        )
        
        # Create test files
        test_files = create_test_files(temp_dir, 10)
        print(f"  ✅ Created {len(test_files)} test files")
        
        # Start background operations
        await manager.start()
        
        # Wait for background operations
        await asyncio.sleep(2.0)
        
        # Stop manager
        await manager.stop()
        
        # Check results
        final_summary = manager.get_storage_summary()
        stats = manager.get_stats()
        
        success = (
            final_summary['total_files'] <= retention_config.max_files and
            (stats['files_compressed'] > 0 or stats['files_deleted'] > 0)
        )
        
        details = f"""Final files: {final_summary['total_files']}
Max files allowed: {retention_config.max_files}
Files compressed: {stats['files_compressed']}
Files deleted: {stats['files_deleted']}
Compressed percentage: {final_summary['compression_percentage']:.1f}%
Background operations: {'Running' if manager.running else 'Stopped'}"""
        
        print_result("Background Operations", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Background Operations", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run compression and retention tests"""
    print_header("Data Compression and Retention Test", 1)
    print("Testing data compression and automated retention policies")
    print("Validating compression algorithms, retention policies, and background operations")
    
    # Import os for file operations
    import os
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['compression'] = await test_compression_basic()
    test_results['time_retention'] = await test_time_based_retention()
    test_results['size_retention'] = await test_size_based_retention()
    test_results['background'] = await test_background_operations()
    
    # Summary
    print_header("Compression & Retention Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Data compression & retention SUCCESSFUL")
        print(f"  🚀 Ready for interactive dashboards")
    else:
        print(f"  ❌ Data compression & retention FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
