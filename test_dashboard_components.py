#!/usr/bin/env python3
"""
Dashboard Components Test
=========================

Test real-time dashboard components with interactive charts and visualization.
"""

import asyncio
import time
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

async def test_chart_component_basic():
    """Test basic chart component functionality"""
    print_header("Chart Component Basic Test", 2)
    
    try:
        from vibe_check.monitoring.dashboard import (
            ChartComponent, ChartConfig, ChartType, TimeRange, DataPoint
        )
        
        # Create chart configuration
        config = ChartConfig(
            chart_type=ChartType.LINE,
            title="Test CPU Usage",
            metric_name="cpu_usage_percent",
            time_range=TimeRange.LAST_HOUR,
            refresh_interval=1.0
        )
        
        # Create chart component
        chart = ChartComponent(config)
        print(f"  ✅ Chart component created: {config.title}")
        
        # Add test data points
        base_time = time.time()
        test_data = []
        
        for i in range(10):
            data_point = DataPoint(
                timestamp=base_time + (i * 60),  # 1-minute intervals
                value=float(50 + (i * 5)),  # Increasing values
                labels={"host": "test-server", "core": str(i % 4)}
            )
            test_data.append(data_point)
            chart.add_data_point(data_point)
        
        print(f"  ✅ Added {len(test_data)} data points")
        
        # Get chart data
        chart_data = chart.get_chart_data()
        stats = chart.get_stats()
        
        success = (
            chart_data['type'] == 'line' and
            chart_data['title'] == config.title and
            len(chart_data['data']['series']) > 0 and
            stats['data_points'] == 10
        )
        
        details = f"""Chart type: {chart_data['type']}
Chart title: {chart_data['title']}
Data series: {len(chart_data['data']['series'])}
Data points: {stats['data_points']}
Time span: {stats['time_span']:.1f}s
Value range: {stats['value_range']['min']:.1f} - {stats['value_range']['max']:.1f}"""
        
        print_result("Chart Component Basic", success, details)
        return success
        
    except Exception as e:
        print_result("Chart Component Basic", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_chart_types():
    """Test different chart types"""
    print_header("Chart Types Test", 2)
    
    try:
        from vibe_check.monitoring.dashboard import (
            ChartComponent, ChartConfig, ChartType, DataPoint
        )
        
        # Test different chart types
        chart_types = [
            ChartType.LINE,
            ChartType.BAR,
            ChartType.PIE,
            ChartType.GAUGE
        ]
        
        results = {}
        
        for chart_type in chart_types:
            config = ChartConfig(
                chart_type=chart_type,
                title=f"Test {chart_type.value.title()} Chart",
                metric_name="test_metric"
            )
            
            chart = ChartComponent(config)
            
            # Add test data
            base_time = time.time()
            for i in range(5):
                data_point = DataPoint(
                    timestamp=base_time + (i * 30),
                    value=float(10 + (i * 5)),
                    labels={"category": f"cat_{i}"}
                )
                chart.add_data_point(data_point)
            
            # Get chart data
            chart_data = chart.get_chart_data()
            
            results[chart_type.value] = {
                "success": chart_data['type'] == chart_type.value,
                "data_structure": type(chart_data['data']).__name__,
                "has_data": len(str(chart_data['data'])) > 10
            }
        
        success = all(result['success'] for result in results.values())
        
        details = f"""Chart types tested: {len(chart_types)}
Results:"""
        
        for chart_type, result in results.items():
            status = "✓" if result['success'] else "✗"
            details += f"\n  {status} {chart_type}: {result['data_structure']}"
        
        print_result("Chart Types", success, details)
        return success
        
    except Exception as e:
        print_result("Chart Types", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_data_aggregation():
    """Test data aggregation functionality"""
    print_header("Data Aggregation Test", 2)
    
    try:
        from vibe_check.monitoring.dashboard import (
            ChartComponent, ChartConfig, ChartType, DataPoint
        )
        
        # Create chart with aggregation
        config = ChartConfig(
            chart_type=ChartType.LINE,
            title="Aggregated Metrics",
            metric_name="response_time",
            aggregation_function="avg",
            group_by_labels=["service"]
        )
        
        chart = ChartComponent(config)
        
        # Add test data with different services
        base_time = time.time()
        services = ["web", "api", "db"]
        
        for i in range(30):  # 30 data points
            service = services[i % len(services)]
            data_point = DataPoint(
                timestamp=base_time + (i * 10),  # 10-second intervals
                value=float(100 + (i * 2) + (hash(service) % 50)),
                labels={"service": service, "region": "us-east"}
            )
            chart.add_data_point(data_point)
        
        # Get aggregated chart data
        chart_data = chart.get_chart_data()
        stats = chart.get_stats()
        
        success = (
            len(chart_data['data']['series']) == len(services) and
            stats['data_points'] == 30 and
            chart_data['title'] == config.title
        )
        
        details = f"""Original data points: {stats['data_points']}
Aggregated series: {len(chart_data['data']['series'])}
Services grouped: {[series['name'] for series in chart_data['data']['series']]}
Aggregation function: {config.aggregation_function}
Group by labels: {config.group_by_labels}"""
        
        print_result("Data Aggregation", success, details)
        return success
        
    except Exception as e:
        print_result("Data Aggregation", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_dashboard_integration():
    """Test dashboard integration with multiple components"""
    print_header("Dashboard Integration Test", 2)
    
    try:
        from vibe_check.monitoring.dashboard import (
            Dashboard, DashboardLayout, ChartComponent, ChartConfig,
            ChartType, DataPoint
        )
        
        # Create dashboard layout
        layout = DashboardLayout(
            title="Test Monitoring Dashboard",
            description="Real-time system monitoring",
            columns=2,
            auto_refresh=True,
            refresh_interval=1.0
        )
        
        # Create dashboard
        dashboard = Dashboard(layout)
        print(f"  ✅ Dashboard created: {layout.title}")
        
        # Create multiple chart components
        charts = {
            "cpu_chart": ChartConfig(
                chart_type=ChartType.LINE,
                title="CPU Usage",
                metric_name="cpu_percent"
            ),
            "memory_chart": ChartConfig(
                chart_type=ChartType.AREA,
                title="Memory Usage",
                metric_name="memory_percent"
            ),
            "requests_chart": ChartConfig(
                chart_type=ChartType.BAR,
                title="Request Count",
                metric_name="requests_total"
            )
        }
        
        # Add components to dashboard
        for chart_id, config in charts.items():
            component = ChartComponent(config)
            dashboard.add_component(chart_id, component)
        
        print(f"  ✅ Added {len(charts)} chart components")
        
        # Add test data to each component
        base_time = time.time()
        for i in range(20):
            timestamp = base_time + (i * 30)
            
            # Add data to each chart
            for chart_id, component in dashboard.components.items():
                value = 50 + (i * 2) + (hash(chart_id) % 30)
                data_point = DataPoint(
                    timestamp=timestamp,
                    value=float(value),
                    labels={"instance": f"server-{i % 3}"}
                )
                component.add_data_point(data_point)
        
        # Start dashboard
        await dashboard.start()
        
        # Wait briefly for auto-refresh
        await asyncio.sleep(2.0)
        
        # Get dashboard data
        dashboard_data = dashboard.get_dashboard_data()
        dashboard_stats = dashboard.get_dashboard_stats()
        
        # Stop dashboard
        await dashboard.stop()
        
        success = (
            dashboard_data['metadata']['component_count'] == len(charts) and
            dashboard_stats['total_components'] == len(charts) and
            dashboard_stats['total_data_points'] > 0 and
            not dashboard.running
        )
        
        details = f"""Dashboard title: {dashboard_data['layout']['title']}
Components: {dashboard_data['metadata']['component_count']}
Total data points: {dashboard_stats['total_data_points']}
Dashboard running: {dashboard.running}
Auto-refresh: {layout.auto_refresh}
Theme: {dashboard_data['layout']['theme']}"""
        
        print_result("Dashboard Integration", success, details)
        return success
        
    except Exception as e:
        print_result("Dashboard Integration", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_real_time_updates():
    """Test real-time data updates"""
    print_header("Real-time Updates Test", 2)
    
    try:
        from vibe_check.monitoring.dashboard import (
            ChartComponent, ChartConfig, ChartType, DataPoint
        )
        
        # Create chart component
        config = ChartConfig(
            chart_type=ChartType.LINE,
            title="Real-time Metrics",
            metric_name="live_data",
            refresh_interval=0.1  # Very fast refresh
        )
        
        chart = ChartComponent(config)
        
        # Track updates
        update_count = 0
        def on_data_update(data_points):
            nonlocal update_count
            update_count += len(data_points)
        
        chart.on_data_update = on_data_update
        
        # Simulate real-time data stream
        base_time = time.time()
        for i in range(50):
            data_point = DataPoint(
                timestamp=base_time + (i * 0.1),  # 100ms intervals
                value=float(i % 100),  # Cycling values
                labels={"stream": "live"}
            )
            chart.add_data_point(data_point)
            await asyncio.sleep(0.01)  # Small delay to simulate real-time
        
        # Get final stats
        stats = chart.get_stats()
        chart_data = chart.get_chart_data()
        
        success = (
            update_count == 50 and
            stats['data_points'] == 50 and
            len(chart_data['data']['series']) > 0
        )
        
        details = f"""Data points added: 50
Update callbacks: {update_count}
Final data points: {stats['data_points']}
Time span: {stats['time_span']:.1f}s
Series count: {len(chart_data['data']['series'])}
Last update: {stats['last_update']:.1f}"""
        
        print_result("Real-time Updates", success, details)
        return success
        
    except Exception as e:
        print_result("Real-time Updates", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run dashboard components tests"""
    print_header("Dashboard Components Test", 1)
    print("Testing real-time dashboard components with interactive charts")
    print("Validating chart types, data aggregation, and dashboard integration")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['basic'] = await test_chart_component_basic()
    test_results['chart_types'] = await test_chart_types()
    test_results['aggregation'] = await test_data_aggregation()
    test_results['dashboard'] = await test_dashboard_integration()
    test_results['real_time'] = await test_real_time_updates()
    
    # Summary
    print_header("Dashboard Components Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Dashboard components SUCCESSFUL")
        print(f"  🚀 Ready for web interface development")
    else:
        print(f"  ❌ Dashboard components FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
