#!/usr/bin/env python3
"""
Debug Integration Issues - Phase 2
===================================

Targeted debugging of specific integration issues.
"""

import asyncio
import time
import tempfile
import shutil
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

async def debug_tsdb_query():
    """Debug TSDB query issues"""
    print("🔍 Debugging TSDB Query Issues...")
    
    try:
        from vibe_check.monitoring.storage.time_series_engine import (
            TimeSeriesStorageEngine, TSDBConfig
        )
        
        # Create temporary storage
        temp_dir = Path(tempfile.mkdtemp())
        config = TSDBConfig(
            data_dir=temp_dir / "tsdb",
            flush_interval_seconds=1.0  # Fast flush for testing
        )
        
        # Initialize TSDB
        tsdb = TimeSeriesStorageEngine(config)
        
        # Store test data
        metric_name = "debug_test_metric"
        current_time = time.time()
        
        print(f"  📝 Storing test data...")
        for i in range(5):
            timestamp = current_time + i
            value = 100.0 + i * 10.0
            
            result = await tsdb.ingest_sample(
                metric_name=metric_name,
                value=value,
                labels={"test": "debug", "index": str(i)},
                timestamp=timestamp
            )
            print(f"    • Sample {i}: value={value}, timestamp={timestamp}, result={result}")
        
        # Wait for flush to occur (flush interval is 1.0s)
        await asyncio.sleep(2.0)
        
        # Try to query the data
        print(f"  🔍 Querying data...")
        start_time = current_time - 1
        end_time = current_time + 10
        
        series_list = await tsdb.query_range(
            metric_name=metric_name,
            start_time=start_time,
            end_time=end_time
        )
        
        print(f"    • Query result: {len(series_list)} series found")
        for i, series in enumerate(series_list):
            print(f"    • Series {i}: {len(series.samples)} samples")
            print(f"      Labels: {series.labels}")
            if series.samples:
                print(f"      First sample: {series.samples[0]}")
                print(f"      Last sample: {series.samples[-1]}")
        
        # Check internal state
        print(f"  📊 TSDB Internal State:")
        print(f"    • Series count: {len(tsdb.series_by_id)}")
        print(f"    • Write buffer size: {len(tsdb.write_buffer)}")
        
        # List all series IDs
        for series_id, series in tsdb.series_by_id.items():
            print(f"    • Series ID: {series_id}")
            print(f"      Samples: {len(series.samples)}")
            print(f"      Labels: {series.labels}")
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        return len(series_list) > 0
        
    except Exception as e:
        print(f"  ❌ TSDB debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def debug_metrics_manager():
    """Debug MetricsManager collector registration"""
    print("🔍 Debugging MetricsManager Issues...")
    
    try:
        from vibe_check.monitoring.collectors.metrics_manager import (
            MetricsManager, MetricsManagerConfig
        )
        from vibe_check.monitoring.collectors.system_collector import SystemMetricsCollector
        
        # Create temporary storage
        temp_dir = Path(tempfile.mkdtemp())
        
        # Configure manager with fast flush
        config = MetricsManagerConfig(
            enable_system_metrics=True,
            tsdb_data_dir=temp_dir / "tsdb",
            stats_collection_interval=1.0,
            ingestion_flush_interval=1.0  # Fast flush for testing
        )
        
        # Initialize manager
        manager = MetricsManager(config)
        print(f"  ✅ Manager created")
        
        # Check initial state
        print(f"  📊 Initial State:")
        print(f"    • System collector: {manager.system_collector}")
        print(f"    • Registry: {manager.registry}")
        
        # Start manager
        await manager.start()
        print(f"  ✅ Manager started")
        
        # Check state after start
        print(f"  📊 State After Start:")
        print(f"    • System collector: {manager.system_collector}")
        print(f"    • TSDB: {manager.tsdb}")
        
        # Check registry
        if hasattr(manager.registry, 'collectors'):
            print(f"    • Registry collectors: {len(manager.registry.collectors)}")
            for name, collector in manager.registry.collectors.items():
                print(f"      - {name}: {type(collector)}")
        
        # Let it run long enough for flush to occur
        await asyncio.sleep(3.0)
        
        # Get stats
        stats = manager.get_manager_stats()
        print(f"  📊 Manager Stats:")
        for key, value in stats.items():
            print(f"    • {key}: {value}")
        
        # Stop manager
        await manager.stop()
        print(f"  ✅ Manager stopped")
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        return stats.get('metrics_ingested', 0) > 0
        
    except Exception as e:
        print(f"  ❌ MetricsManager debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def debug_collector_direct():
    """Debug collector functionality directly"""
    print("🔍 Debugging Collector Direct...")
    
    try:
        from vibe_check.monitoring.collectors.system_collector import SystemMetricsCollector
        
        # Create collector
        collector = SystemMetricsCollector()
        print(f"  ✅ Collector created: {type(collector)}")
        
        # Test collection
        metrics = await collector.collect_metrics()
        print(f"  ✅ Metrics collected: {len(metrics)} metrics")
        
        # Show sample metrics
        for i, metric in enumerate(metrics[:3]):  # Show first 3
            print(f"    • Metric {i}: {metric.name} = {metric.value}")
            print(f"      Labels: {metric.labels}")
            print(f"      Timestamp: {metric.timestamp}")
        
        return len(metrics) > 0
        
    except Exception as e:
        print(f"  ❌ Collector debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run debug tests"""
    print("🚀 Debug Integration Issues - Phase 2")
    print("=" * 50)
    
    # Run debug tests
    results = {}
    results['tsdb_query'] = await debug_tsdb_query()
    results['metrics_manager'] = await debug_metrics_manager()
    results['collector_direct'] = await debug_collector_direct()
    
    # Summary
    print("\n📊 Debug Results:")
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
