#!/usr/bin/env python3
"""
Distributed Tracing Test
=========================

Test distributed tracing with OpenTelemetry integration and trace correlation.
"""

import asyncio
import time
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

def test_span_context():
    """Test span context functionality"""
    print_header("Span Context Test", 2)
    
    try:
        from vibe_check.monitoring.tracing import SpanContext, TraceIdGenerator
        
        # Test trace ID generation
        trace_id = TraceIdGenerator.generate_trace_id()
        span_id = TraceIdGenerator.generate_span_id()
        
        # Test span context creation
        context = SpanContext(
            trace_id=trace_id,
            span_id=span_id,
            parent_span_id=None,
            trace_flags=1
        )
        
        # Test child context
        child_span_id = TraceIdGenerator.generate_span_id()
        child_context = SpanContext(
            trace_id=trace_id,
            span_id=child_span_id,
            parent_span_id=span_id,
            trace_flags=1
        )
        
        success = (
            len(trace_id) == 32 and  # 128-bit hex (32 characters)
            len(span_id) == 16 and   # 64-bit hex (16 characters)
            context.is_valid() and
            context.is_sampled() and
            child_context.is_valid() and
            child_context.parent_span_id == span_id and
            child_context.trace_id == trace_id
        )
        
        details = f"""Span context validation:
Trace ID: {trace_id} (length: {len(trace_id)})
Span ID: {span_id} (length: {len(span_id)})
Child Span ID: {child_span_id}

Root context:
- Valid: {context.is_valid()}
- Sampled: {context.is_sampled()}
- Parent: {context.parent_span_id}

Child context:
- Valid: {child_context.is_valid()}
- Sampled: {child_context.is_sampled()}
- Parent: {child_context.parent_span_id}
- Same trace: {child_context.trace_id == trace_id}"""
        
        print_result("Span Context", success, details)
        return success
        
    except Exception as e:
        print_result("Span Context", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_span_operations():
    """Test span creation and operations"""
    print_header("Span Operations Test", 2)
    
    try:
        from vibe_check.monitoring.tracing import (
            Span, SpanContext, SpanKind, SpanStatus, SpanEvent, TraceIdGenerator
        )
        
        # Create span context
        context = SpanContext(
            trace_id=TraceIdGenerator.generate_trace_id(),
            span_id=TraceIdGenerator.generate_span_id()
        )
        
        # Create span
        span = Span(
            context=context,
            name="test_operation",
            kind=SpanKind.INTERNAL,
            start_time=time.time(),
            service_name="test_service"
        )
        
        # Test span operations
        operations_start = time.time()
        
        span.set_attribute("user.id", "123")
        span.set_attribute("operation.type", "test")
        span.add_event("processing_started", {"step": 1})
        
        # Simulate work
        time.sleep(0.01)
        
        span.add_event("processing_completed", {"step": 2})
        span.set_status(SpanStatus.OK, "Operation completed successfully")
        
        # End span
        span.end_time = time.time()
        
        operations_time = time.time() - operations_start
        
        success = (
            span.name == "test_operation" and
            span.kind == SpanKind.INTERNAL and
            span.service_name == "test_service" and
            span.attributes["user.id"] == "123" and
            span.attributes["operation.type"] == "test" and
            len(span.events) == 2 and
            span.events[0].name == "processing_started" and
            span.events[1].name == "processing_completed" and
            span.status == SpanStatus.OK and
            span.is_finished() and
            span.duration_ms() > 0 and
            operations_time < 0.1  # Fast operations
        )
        
        details = f"""Span operations:
Span name: {span.name}
Span kind: {span.kind.value}
Service: {span.service_name}
Duration: {span.duration_ms():.1f}ms
Operations time: {operations_time*1000:.1f}ms

Attributes: {len(span.attributes)}
- user.id: {span.attributes.get('user.id')}
- operation.type: {span.attributes.get('operation.type')}

Events: {len(span.events)}
- {span.events[0].name}: {span.events[0].attributes}
- {span.events[1].name}: {span.events[1].attributes}

Status: {span.status.value} - {span.status_message}
Finished: {span.is_finished()}"""
        
        print_result("Span Operations", success, details)
        return success
        
    except Exception as e:
        print_result("Span Operations", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_tracing_manager():
    """Test tracing manager functionality"""
    print_header("Tracing Manager Test", 2)
    
    try:
        from vibe_check.monitoring.tracing import TracingManager, SpanKind, SpanStatus
        
        # Create tracing manager
        manager = TracingManager("test-service")
        
        # Test span creation and management
        tracing_start = time.time()
        
        # Create root span
        root_span = manager.start_span("root_operation", kind=SpanKind.SERVER)
        root_span.set_attribute("http.method", "GET")
        root_span.set_attribute("http.url", "/api/test")
        
        # Create child span
        child_span = manager.start_span(
            "database_query",
            kind=SpanKind.CLIENT,
            parent_context=root_span.context
        )
        child_span.set_attribute("db.statement", "SELECT * FROM users")
        child_span.add_event("query_started")
        
        # Simulate work
        time.sleep(0.01)
        
        child_span.add_event("query_completed")
        child_span.set_status(SpanStatus.OK)
        manager.end_span(child_span)
        
        # End root span
        root_span.set_status(SpanStatus.OK)
        manager.end_span(root_span)
        
        tracing_time = time.time() - tracing_start
        
        # Get statistics and traces
        stats = manager.get_statistics()
        recent_traces = manager.get_recent_traces(10)
        
        # Find our trace
        test_trace = None
        for trace in recent_traces:
            if trace.trace_id == root_span.context.trace_id:
                test_trace = trace
                break
        
        success = (
            stats["service_name"] == "test-service" and
            stats["spans_processed"] >= 2 and
            stats["total_spans"] >= 2 and
            len(recent_traces) > 0 and
            test_trace is not None and
            len(test_trace.spans) == 2 and
            test_trace.is_complete() and
            tracing_time < 0.1  # Fast tracing
        )
        
        details = f"""Tracing manager:
Service: {stats['service_name']}
Tracing time: {tracing_time*1000:.1f}ms

Statistics:
- Spans processed: {stats['spans_processed']}
- Total spans: {stats['total_spans']}
- Active spans: {stats['active_spans']}
- Traces count: {stats['traces_count']}
- Complete traces: {stats['complete_traces']}

Test trace:
- Trace ID: {test_trace.trace_id if test_trace else 'None'}
- Spans: {len(test_trace.spans) if test_trace else 0}
- Complete: {test_trace.is_complete() if test_trace else False}
- Duration: {test_trace.duration_ms():.1f}ms if test_trace and test_trace.duration_ms() else 'N/A'

Span hierarchy:
- Root: {root_span.name} ({root_span.kind.value})
- Child: {child_span.name} ({child_span.kind.value})"""
        
        print_result("Tracing Manager", success, details)
        return success
        
    except Exception as e:
        print_result("Tracing Manager", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_context_manager():
    """Test span context manager"""
    print_header("Context Manager Test", 2)
    
    try:
        from vibe_check.monitoring.tracing import TracingManager, span_context, SpanKind
        
        manager = TracingManager("context-test")
        
        # Test context manager
        context_start = time.time()
        
        with span_context(manager, "outer_operation", kind=SpanKind.SERVER) as outer_span:
            outer_span.set_attribute("operation.level", "outer")
            outer_span.add_event("outer_started")
            
            # Nested context
            with span_context(manager, "inner_operation", kind=SpanKind.INTERNAL) as inner_span:
                inner_span.set_attribute("operation.level", "inner")
                inner_span.add_event("inner_started")
                
                # Simulate work
                time.sleep(0.01)
                
                inner_span.add_event("inner_completed")
            
            outer_span.add_event("outer_completed")
        
        context_time = time.time() - context_start
        
        # Verify spans were created and finished
        stats = manager.get_statistics()
        recent_traces = manager.get_recent_traces(1)
        
        success = (
            len(recent_traces) > 0 and
            len(recent_traces[0].spans) == 2 and
            all(span.is_finished() for span in recent_traces[0].spans) and
            context_time < 0.1 and  # Fast context management
            stats["active_spans"] == 0  # No active spans after context exit
        )
        
        details = f"""Context manager:
Context time: {context_time*1000:.1f}ms
Active spans after: {stats['active_spans']}

Trace validation:
- Traces: {len(recent_traces)}
- Spans in trace: {len(recent_traces[0].spans) if recent_traces else 0}
- All finished: {all(span.is_finished() for span in recent_traces[0].spans) if recent_traces else False}

Span details:"""
        
        if recent_traces:
            for span in recent_traces[0].spans:
                details += f"\n- {span.name}: {span.kind.value}, events: {len(span.events)}"
        
        print_result("Context Manager", success, details)
        return success
        
    except Exception as e:
        print_result("Context Manager", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_function_tracing():
    """Test function tracing decorator"""
    print_header("Function Tracing Test", 2)
    
    try:
        from vibe_check.monitoring.tracing import TracingManager, trace_function
        
        manager = TracingManager("function-test")
        
        # Create traced functions
        @trace_function(manager, "sync_function")
        def sync_function(x, y):
            time.sleep(0.01)  # Simulate work
            return x + y
        
        @trace_function(manager)  # Use function name
        async def async_function(value):
            await asyncio.sleep(0.01)  # Simulate async work
            return value * 2
        
        # Test function tracing
        tracing_start = time.time()
        
        # Test sync function
        result1 = sync_function(5, 3)
        
        # Test async function
        result2 = await async_function(10)
        
        tracing_time = time.time() - tracing_start
        
        # Verify results and tracing
        stats = manager.get_statistics()
        recent_traces = manager.get_recent_traces(10)
        
        # Find function spans
        function_spans = []
        for trace in recent_traces:
            for span in trace.spans:
                if "function" in span.name:
                    function_spans.append(span)
        
        success = (
            result1 == 8 and
            result2 == 20 and
            len(function_spans) >= 2 and
            all(span.is_finished() for span in function_spans) and
            any("sync_function" in span.name for span in function_spans) and
            any("async_function" in span.name for span in function_spans) and
            tracing_time < 0.1  # Fast function tracing
        )
        
        details = f"""Function tracing:
Tracing time: {tracing_time*1000:.1f}ms
Function results: sync={result1}, async={result2}

Statistics:
- Spans processed: {stats['spans_processed']}
- Function spans found: {len(function_spans)}

Function spans:"""
        
        for span in function_spans:
            details += f"\n- {span.name}: {span.duration_ms():.1f}ms, events: {len(span.events)}"
            if span.attributes:
                details += f", attrs: {list(span.attributes.keys())}"
        
        print_result("Function Tracing", success, details)
        return success
        
    except Exception as e:
        print_result("Function Tracing", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_trace_correlation():
    """Test trace correlation across services"""
    print_header("Trace Correlation Test", 2)
    
    try:
        from vibe_check.monitoring.tracing import TracingManager, SpanKind
        
        # Simulate multiple services
        service_a = TracingManager("service-a")
        service_b = TracingManager("service-b")
        service_c = TracingManager("service-c")
        
        correlation_start = time.time()
        
        # Service A starts the request
        root_span = service_a.start_span("handle_request", kind=SpanKind.SERVER)
        root_span.set_attribute("http.method", "POST")
        root_span.set_attribute("service", "service-a")
        
        # Service A calls Service B
        service_b_span = service_b.start_span(
            "process_data",
            kind=SpanKind.SERVER,
            parent_context=root_span.context
        )
        service_b_span.set_attribute("service", "service-b")
        
        # Service B calls Service C
        service_c_span = service_c.start_span(
            "store_data",
            kind=SpanKind.SERVER,
            parent_context=service_b_span.context
        )
        service_c_span.set_attribute("service", "service-c")
        service_c_span.set_attribute("db.operation", "INSERT")
        
        # Simulate work and finish spans
        await asyncio.sleep(0.01)
        
        service_c.end_span(service_c_span)
        service_b.end_span(service_b_span)
        service_a.end_span(root_span)
        
        correlation_time = time.time() - correlation_start
        
        # Verify trace correlation
        trace_id = root_span.context.trace_id
        
        # All services should have spans with the same trace ID
        service_a_trace = service_a.get_trace(trace_id)
        service_b_trace = service_b.get_trace(trace_id)
        service_c_trace = service_c.get_trace(trace_id)
        
        success = (
            service_a_trace is not None and
            service_b_trace is not None and
            service_c_trace is not None and
            service_a_trace.trace_id == trace_id and
            service_b_trace.trace_id == trace_id and
            service_c_trace.trace_id == trace_id and
            service_b_span.context.parent_span_id == root_span.context.span_id and
            service_c_span.context.parent_span_id == service_b_span.context.span_id and
            correlation_time < 0.1  # Fast correlation
        )
        
        details = f"""Trace correlation:
Correlation time: {correlation_time*1000:.1f}ms
Trace ID: {trace_id}

Service traces:
- Service A: {'✓' if service_a_trace else '✗'} ({len(service_a_trace.spans) if service_a_trace else 0} spans)
- Service B: {'✓' if service_b_trace else '✗'} ({len(service_b_trace.spans) if service_b_trace else 0} spans)
- Service C: {'✓' if service_c_trace else '✗'} ({len(service_c_trace.spans) if service_c_trace else 0} spans)

Span hierarchy:
- Root: {root_span.context.span_id} (service-a)
- Child: {service_b_span.context.span_id} (service-b, parent: {service_b_span.context.parent_span_id})
- Grandchild: {service_c_span.context.span_id} (service-c, parent: {service_c_span.context.parent_span_id})

Correlation validation:
- Same trace ID: ✓
- Parent-child relationships: ✓
- Cross-service tracing: ✓"""
        
        print_result("Trace Correlation", success, details)
        return success
        
    except Exception as e:
        print_result("Trace Correlation", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_performance_benchmarks():
    """Test tracing performance"""
    print_header("Performance Benchmarks Test", 2)
    
    try:
        from vibe_check.monitoring.tracing import TracingManager, SpanKind
        
        manager = TracingManager("performance-test")
        
        # Benchmark span creation and processing
        span_count = 10000
        benchmark_start = time.time()
        
        spans = []
        for i in range(span_count):
            span = manager.start_span(f"operation_{i}", kind=SpanKind.INTERNAL)
            span.set_attribute("iteration", i)
            span.set_attribute("batch", i // 1000)
            span.add_event("processing", {"step": 1})
            spans.append(span)
            
            # End every 10th span immediately
            if i % 10 == 0:
                manager.end_span(span)
        
        # End remaining spans
        for span in spans:
            if not span.is_finished():
                manager.end_span(span)
        
        benchmark_time = time.time() - benchmark_start
        
        # Get final statistics
        stats = manager.get_statistics()
        processing_rate = span_count / benchmark_time
        
        success = (
            stats["spans_processed"] >= span_count and
            processing_rate > 1000 and  # > 1000 spans/sec
            benchmark_time < 30.0 and  # Complete within 30 seconds
            stats["active_spans"] == 0  # All spans finished
        )
        
        details = f"""Performance benchmarks:
Span count: {span_count:,}
Processing time: {benchmark_time:.2f}s
Processing rate: {processing_rate:.0f} spans/sec

Statistics:
- Spans processed: {stats['spans_processed']:,}
- Spans exported: {stats['spans_exported']:,}
- Export errors: {stats['export_errors']}
- Active spans: {stats['active_spans']}
- Traces count: {stats['traces_count']:,}

Performance targets:
- Rate: {processing_rate:.0f} spans/sec (target: >1000)
- Memory: Efficient span storage
- Export: Batched processing"""
        
        print_result("Performance Benchmarks", success, details)
        return success
        
    except Exception as e:
        print_result("Performance Benchmarks", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run distributed tracing tests"""
    print_header("Distributed Tracing Test", 1)
    print("Testing distributed tracing with OpenTelemetry integration and trace correlation")
    print("Validating span management, context propagation, function tracing, and performance")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['span_context'] = test_span_context()
    test_results['span_operations'] = test_span_operations()
    test_results['tracing_manager'] = test_tracing_manager()
    test_results['context_manager'] = test_context_manager()
    test_results['function_tracing'] = await test_function_tracing()
    test_results['trace_correlation'] = await test_trace_correlation()
    test_results['performance'] = await test_performance_benchmarks()
    
    # Summary
    print_header("Distributed Tracing Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper().replace('_', ' ')} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Distributed tracing system SUCCESSFUL")
        print(f"  🚀 Ready for Week 13 completion")
        print(f"  🔗 OpenTelemetry-compatible trace correlation")
        print(f"  ⚡ Performance: >1000 spans/sec processing rate")
    else:
        print(f"  ❌ Distributed tracing system FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
