#!/usr/bin/env python3
"""
Execution Time Profiling Test Suite
==================================

Comprehensive test suite for Task 5.2: Execution Time Profiling.
Tests call graph generation, bottleneck detection, and profiling integration.
"""

import asyncio
import time
import sys
import json
from pathlib import Path
from typing import Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))


async def test_execution_profiler_basic():
    """Test basic execution profiler functionality"""
    print("🧪 Testing Execution Profiler Basic")
    print("=" * 36)
    
    try:
        # Direct import
        sys.path.insert(0, str(Path(__file__).parent / "vibe_check" / "monitoring"))
        
        from profiling.execution_profiler import ExecutionProfiler
        
        # Create profiler
        profiler = ExecutionProfiler(
            enable_memory_tracking=True,
            max_call_depth=50,
            bottleneck_threshold=0.01
        )
        
        print(f"  ✅ Profiler created:")
        print(f"    • Memory tracking: {profiler.enable_memory_tracking}")
        print(f"    • Max call depth: {profiler.max_call_depth}")
        print(f"    • Bottleneck threshold: {profiler.bottleneck_threshold}s")
        
        # Start profiling session
        session_id = profiler.start_profiling("test_session")
        
        print(f"  ✅ Profiling session started:")
        print(f"    • Session ID: {session_id}")
        print(f"    • Is profiling: {profiler.is_profiling}")
        
        # Define test functions
        @profiler.profile_function(name="calculate_fibonacci")
        def fibonacci(n: int) -> int:
            """Calculate fibonacci number"""
            if n <= 1:
                return n
            return fibonacci(n - 1) + fibonacci(n - 2)
        
        @profiler.profile_function(name="process_list", track_memory=True)
        def process_list(data: List[int]) -> List[int]:
            """Process list of numbers"""
            time.sleep(0.01)  # Simulate work
            return [x * 2 for x in data]
        
        @profiler.profile_function(name="async_operation")
        async def async_operation(delay: float) -> str:
            """Async operation with delay"""
            await asyncio.sleep(delay)
            return f"completed after {delay}s"
        
        print(f"  ✅ Functions instrumented: 3")
        
        # Execute profiled functions
        print("  🔄 Executing profiled functions...")
        
        # Test recursive function (creates call graph)
        fib_result = fibonacci(8)
        
        # Test list processing
        list_result = process_list([1, 2, 3, 4, 5])
        
        # Test async function
        async_result = await async_operation(0.02)
        
        print(f"    • Fibonacci(8): {fib_result}")
        print(f"    • List processing: {len(list_result)} items")
        print(f"    • Async result: {async_result}")
        
        # Stop profiling
        session = profiler.stop_profiling()
        
        if session:
            print(f"  ✅ Profiling session completed:")
            print(f"    • Session ID: {session.session_id}")
            print(f"    • Duration: {session.total_duration:.4f}s")
            print(f"    • Functions: {session.total_functions}")
            print(f"    • Root frames: {len(session.root_frames)}")
        
        # Get function statistics
        function_stats = profiler.get_function_statistics()
        
        print(f"  📊 Function statistics:")
        for func_key, stats in function_stats.items():
            print(f"    • {func_key}:")
            print(f"      - Calls: {stats['call_count']}")
            print(f"      - Avg time: {stats['avg_time']:.6f}s")
            print(f"      - Total time: {stats['total_time']:.6f}s")
        
        return {
            'profiler_created': True,
            'session_started': session_id == "test_session",
            'session_completed': session is not None,
            'functions_profiled': len(function_stats) >= 3,
            'call_graph_generated': session.total_functions > 3 if session else False,
            'fibonacci_calls': function_stats.get('__main__.calculate_fibonacci', {}).get('call_count', 0) > 1,
            'async_support': '__main__.async_operation' in function_stats
        }
        
    except Exception as e:
        print(f"❌ Execution profiler basic test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_call_graph_generation():
    """Test call graph generation and analysis"""
    print("\n🧪 Testing Call Graph Generation")
    print("=" * 34)
    
    try:
        from profiling.execution_profiler import ExecutionProfiler
        
        # Create profiler
        profiler = ExecutionProfiler(enable_memory_tracking=True)
        
        # Start profiling
        session_id = profiler.start_profiling("call_graph_test")
        
        # Define nested functions for call graph
        @profiler.profile_function(name="level_1")
        def level_1():
            """Top level function"""
            level_2_a()
            level_2_b()
            return "level_1_complete"
        
        @profiler.profile_function(name="level_2_a")
        def level_2_a():
            """Second level function A"""
            level_3()
            time.sleep(0.005)
            return "level_2_a_complete"
        
        @profiler.profile_function(name="level_2_b")
        def level_2_b():
            """Second level function B"""
            level_3()
            level_3()
            return "level_2_b_complete"
        
        @profiler.profile_function(name="level_3")
        def level_3():
            """Third level function"""
            time.sleep(0.002)
            return "level_3_complete"
        
        print(f"  ✅ Nested functions defined: 4 levels")
        
        # Execute to create call graph
        print("  🔄 Executing nested function calls...")
        result = level_1()
        
        # Stop profiling
        session = profiler.stop_profiling()
        
        # Get call graph
        call_graph = profiler.get_call_graph(session_id)
        
        if call_graph:
            print(f"  ✅ Call graph generated:")
            print(f"    • Session: {call_graph['session_id']}")
            print(f"    • Total functions: {call_graph['total_functions']}")
            print(f"    • Root frames: {len(call_graph['root_frames'])}")
            
            # Analyze call graph structure
            def analyze_frame(frame, depth=0):
                indent = "  " * depth
                print(f"    {indent}• {frame['function_name']} ({frame['duration']:.6f}s)")
                for child in frame.get('child_frames', []):
                    analyze_frame(child, depth + 1)
            
            print(f"  🌳 Call graph structure:")
            for root_frame in call_graph['root_frames']:
                analyze_frame(root_frame)
        
        # Get bottlenecks
        bottlenecks = profiler.get_bottlenecks(session_id)
        
        print(f"  🔍 Bottlenecks detected: {len(bottlenecks)}")
        for i, bottleneck in enumerate(bottlenecks[:3]):
            print(f"    {i+1}. {bottleneck['function']}: {bottleneck['duration']:.6f}s")
        
        return {
            'call_graph_generated': call_graph is not None,
            'nested_calls_tracked': call_graph['total_functions'] >= 6 if call_graph else False,
            'call_hierarchy': len(call_graph['root_frames']) == 1 if call_graph else False,
            'bottlenecks_detected': len(bottlenecks) > 0,
            'execution_successful': result == "level_1_complete"
        }
        
    except Exception as e:
        print(f"❌ Call graph generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_profiling_collector_integration():
    """Test profiling collector integration"""
    print("\n🧪 Testing Profiling Collector Integration")
    print("=" * 42)
    
    try:
        from profiling.execution_profiler import ExecutionProfiler
        from collectors.profiling_collector import ProfilingMetricsCollector
        from collectors.base_collector import CollectorConfig, CollectionInterval
        
        # Create profiler
        profiler = ExecutionProfiler(enable_memory_tracking=True)
        
        # Create collector
        config = CollectorConfig(
            collection_interval=CollectionInterval.NORMAL.value,
            max_collection_time=5.0,
            labels={"test": "integration"}
        )
        
        collector = ProfilingMetricsCollector(profiler, config)
        
        print(f"  ✅ Collector created:")
        print(f"    • Name: {collector.name}")
        print(f"    • Profiler attached: {collector.profiler is not None}")
        
        # Test metric registration
        metric_definitions = collector.get_metric_definitions()
        
        session_metrics = [name for name in metric_definitions if 'session' in name]
        function_metrics = [name for name in metric_definitions if 'function' in name]
        bottleneck_metrics = [name for name in metric_definitions if 'bottleneck' in name]
        
        print(f"  ✅ Metrics registered:")
        print(f"    • Total metrics: {len(metric_definitions)}")
        print(f"    • Session metrics: {len(session_metrics)}")
        print(f"    • Function metrics: {len(function_metrics)}")
        print(f"    • Bottleneck metrics: {len(bottleneck_metrics)}")
        
        # Run profiling session
        session_id = profiler.start_profiling("collector_test")
        
        @profiler.profile_function(name="test_function")
        def test_function(n: int) -> int:
            """Test function for collector"""
            total = 0
            for i in range(n):
                total += i * i
            time.sleep(0.001)
            return total
        
        # Execute function multiple times
        for i in range(5):
            result = test_function(100)
        
        # Stop profiling
        session = profiler.stop_profiling()
        
        # Collect metrics
        print("  🔄 Collecting profiling metrics...")
        start_time = time.time()
        
        metrics = await collector.collect_metrics()
        
        collection_time = time.time() - start_time
        
        print(f"    • Metrics collected: {len(metrics)}")
        print(f"    • Collection time: {collection_time:.3f}s")
        
        # Analyze collected metrics
        if metrics:
            session_count_metrics = [m for m in metrics if m.name == "profiling_sessions_total"]
            function_call_metrics = [m for m in metrics if m.name == "profiling_function_calls_total"]
            duration_metrics = [m for m in metrics if m.name == "profiling_function_duration_seconds"]
            overhead_metrics = [m for m in metrics if m.name == "profiling_overhead_percent"]
            
            print(f"  ✅ Metrics analysis:")
            print(f"    • Session metrics: {len(session_count_metrics)}")
            print(f"    • Function call metrics: {len(function_call_metrics)}")
            print(f"    • Duration metrics: {len(duration_metrics)}")
            print(f"    • Overhead metrics: {len(overhead_metrics)}")
            
            if function_call_metrics:
                print(f"    • Function calls tracked: {function_call_metrics[0].value}")
            
            if overhead_metrics:
                print(f"    • Profiler overhead: {overhead_metrics[0].value:.4f}%")
        
        # Test summary
        summary = collector.get_profiling_summary()
        
        print(f"  📊 Profiling summary:")
        print(f"    • Total sessions: {summary['total_sessions']}")
        print(f"    • Function calls: {summary['total_function_calls']}")
        print(f"    • Execution time: {summary['total_execution_time']:.6f}s")
        print(f"    • Overhead: {summary['profiler_overhead']:.4f}%")
        
        collector.cleanup()
        
        return {
            'collector_created': collector.name == "ProfilingMetricsCollector",
            'metrics_registered': len(metric_definitions) >= 10,
            'metrics_collected': len(metrics) >= 5,
            'collection_fast': collection_time < 5.0,
            'session_tracking': len(session_count_metrics) > 0 if metrics else False,
            'function_tracking': len(function_call_metrics) > 0 if metrics else False,
            'overhead_tracking': len(overhead_metrics) > 0 if metrics else False,
            'profiling_successful': summary['total_function_calls'] >= 5
        }
        
    except Exception as e:
        print(f"❌ Profiling collector integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_bottleneck_detection():
    """Test performance bottleneck detection"""
    print("\n🧪 Testing Bottleneck Detection")
    print("=" * 32)
    
    try:
        from profiling.execution_profiler import ExecutionProfiler
        
        # Create profiler with low threshold for testing
        profiler = ExecutionProfiler(
            enable_memory_tracking=True,
            bottleneck_threshold=0.005  # 5ms threshold
        )
        
        # Start profiling
        session_id = profiler.start_profiling("bottleneck_test")
        
        # Define functions with different performance characteristics
        @profiler.profile_function(name="fast_function")
        def fast_function():
            """Fast function"""
            return sum(range(100))
        
        @profiler.profile_function(name="slow_function")
        def slow_function():
            """Slow function (bottleneck)"""
            time.sleep(0.02)  # 20ms - should be detected as bottleneck
            return "slow_complete"
        
        @profiler.profile_function(name="medium_function")
        def medium_function():
            """Medium speed function"""
            time.sleep(0.008)  # 8ms - should be detected as bottleneck
            fast_function()  # Call fast function
            return "medium_complete"
        
        @profiler.profile_function(name="main_function")
        def main_function():
            """Main function that calls others"""
            fast_function()
            medium_function()
            slow_function()
            return "main_complete"
        
        print(f"  ✅ Functions with varying performance defined")
        
        # Execute functions
        print("  🔄 Executing functions to create bottlenecks...")
        result = main_function()
        
        # Stop profiling
        session = profiler.stop_profiling()
        
        # Get bottlenecks
        bottlenecks = profiler.get_bottlenecks(session_id)
        
        print(f"  🔍 Bottleneck analysis:")
        print(f"    • Total bottlenecks: {len(bottlenecks)}")
        print(f"    • Threshold: {profiler.bottleneck_threshold}s")
        
        for i, bottleneck in enumerate(bottlenecks):
            print(f"    {i+1}. {bottleneck['function']}:")
            print(f"       - Duration: {bottleneck['duration']:.6f}s")
            print(f"       - Depth: {bottleneck['depth']}")
            print(f"       - Memory: {bottleneck['memory_delta']} bytes")
        
        # Verify bottleneck detection
        slow_detected = any('slow_function' in b['function'] for b in bottlenecks)
        medium_detected = any('medium_function' in b['function'] for b in bottlenecks)
        fast_not_detected = not any('fast_function' in b['function'] for b in bottlenecks)
        
        print(f"  ✅ Bottleneck detection results:")
        print(f"    • Slow function detected: {slow_detected}")
        print(f"    • Medium function detected: {medium_detected}")
        print(f"    • Fast function not detected: {fast_not_detected}")
        
        return {
            'bottlenecks_detected': len(bottlenecks) > 0,
            'slow_function_detected': slow_detected,
            'medium_function_detected': medium_detected,
            'fast_function_excluded': fast_not_detected,
            'threshold_working': len(bottlenecks) >= 2,
            'execution_successful': result == "main_complete"
        }
        
    except Exception as e:
        print(f"❌ Bottleneck detection test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def main():
    """Main test function"""
    print("🚀 Execution Time Profiling Test Suite - Task 5.2")
    print("=" * 60)
    
    # Run tests
    basic_results = await test_execution_profiler_basic()
    call_graph_results = await test_call_graph_generation()
    collector_results = await test_profiling_collector_integration()
    bottleneck_results = await test_bottleneck_detection()
    
    print("\n" + "=" * 60)
    print("📊 EXECUTION TIME PROFILING SUMMARY")
    print("=" * 60)
    
    # Evaluate results
    targets_met = 0
    total_targets = 5
    
    # Target 1: Basic profiling
    if (basic_results.get('profiler_created') and 
        basic_results.get('session_completed') and 
        basic_results.get('functions_profiled')):
        print("  ✅ Basic execution profiling working")
        targets_met += 1
    else:
        print("  ❌ Basic execution profiling issues")
    
    # Target 2: Call graph generation
    if (call_graph_results.get('call_graph_generated') and 
        call_graph_results.get('nested_calls_tracked') and 
        call_graph_results.get('call_hierarchy')):
        print("  ✅ Call graph generation working")
        targets_met += 1
    else:
        print("  ❌ Call graph generation issues")
    
    # Target 3: Collector integration
    if (collector_results.get('collector_created') and 
        collector_results.get('metrics_collected') and 
        collector_results.get('function_tracking')):
        print("  ✅ Collector integration working")
        targets_met += 1
    else:
        print("  ❌ Collector integration issues")
    
    # Target 4: Bottleneck detection
    if (bottleneck_results.get('bottlenecks_detected') and 
        bottleneck_results.get('slow_function_detected') and 
        bottleneck_results.get('threshold_working')):
        print("  ✅ Bottleneck detection working")
        targets_met += 1
    else:
        print("  ❌ Bottleneck detection issues")
    
    # Target 5: Overall performance
    if (basic_results.get('async_support', False) and 
        collector_results.get('collection_fast', False) and 
        call_graph_results.get('execution_successful', False)):
        print("  ✅ Overall performance good")
        targets_met += 1
    else:
        print("  ❌ Overall performance issues")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 4:
        print("✅ Task 5.2: Execution Time Profiling SUCCESSFUL")
        print("🚀 Ready to proceed with Task 5.3: Memory Usage Tracking")
        
        print(f"\n🏆 Key Achievements:")
        if basic_results:
            print(f"  • Basic profiling: {basic_results.get('functions_profiled', False)} functions")
            print(f"  • Async support: {basic_results.get('async_support', False)}")
        if call_graph_results:
            print(f"  • Call graph: {call_graph_results.get('nested_calls_tracked', False)} nested calls")
            print(f"  • Call hierarchy: {call_graph_results.get('call_hierarchy', False)}")
        if collector_results:
            print(f"  • Metrics integration: {collector_results.get('metrics_collected', False)}")
        if bottleneck_results:
            print(f"  • Bottleneck detection: {bottleneck_results.get('bottlenecks_detected', False)}")
        print(f"  • Call graph generation and analysis")
        print(f"  • Performance bottleneck identification")
        print(f"  • Execution time profiling with stack traces")
        print(f"  • Integration with metrics collection framework")
        print(f"  • Sync and async function profiling support")
        
        return 0
    else:
        print("⚠️  Task 5.2: Execution Time Profiling needs improvement")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
