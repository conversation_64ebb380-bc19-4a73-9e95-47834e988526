#!/usr/bin/env python3
"""
Infrastructure Topology Test
============================

Test infrastructure topology visualization with service dependencies and resource overlays.
"""

import random
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

def generate_test_infrastructure():
    """Generate test infrastructure nodes and connections"""
    from vibe_check.monitoring.visualization.charts import (
        InfrastructureNode, InfrastructureConnection, ResourceMetrics,
        NodeType, ConnectionType, HealthStatus
    )
    
    # Create test nodes
    nodes = []
    
    # External services
    nodes.append(InfrastructureNode(
        id="ext_api", name="External API", node_type=NodeType.EXTERNAL_SERVICE,
        health_status=HealthStatus.HEALTHY, zone="external", environment="production",
        metrics=ResourceMetrics(requests_per_second=100.0, response_time_ms=50.0)
    ))
    
    # Load balancer
    nodes.append(InfrastructureNode(
        id="lb_main", name="Main Load Balancer", node_type=NodeType.LOAD_BALANCER,
        health_status=HealthStatus.HEALTHY, zone="dmz", environment="production",
        metrics=ResourceMetrics(cpu_percent=45.0, memory_percent=60.0, requests_per_second=500.0)
    ))
    
    # API Gateway
    nodes.append(InfrastructureNode(
        id="api_gateway", name="API Gateway", node_type=NodeType.API_GATEWAY,
        health_status=HealthStatus.WARNING, zone="app", environment="production",
        metrics=ResourceMetrics(cpu_percent=75.0, memory_percent=80.0, response_time_ms=120.0)
    ))
    
    # Services
    for i in range(3):
        nodes.append(InfrastructureNode(
            id=f"service_{i}", name=f"Service {i+1}", node_type=NodeType.SERVICE,
            health_status=random.choice([HealthStatus.HEALTHY, HealthStatus.WARNING]),
            zone="app", environment="production",
            metrics=ResourceMetrics(
                cpu_percent=random.uniform(20, 80),
                memory_percent=random.uniform(30, 90),
                requests_per_second=random.uniform(10, 100)
            )
        ))
    
    # Cache
    nodes.append(InfrastructureNode(
        id="cache_redis", name="Redis Cache", node_type=NodeType.CACHE,
        health_status=HealthStatus.HEALTHY, zone="data", environment="production",
        metrics=ResourceMetrics(memory_percent=65.0, connections_count=150)
    ))
    
    # Database
    nodes.append(InfrastructureNode(
        id="db_postgres", name="PostgreSQL DB", node_type=NodeType.DATABASE,
        health_status=HealthStatus.CRITICAL, zone="data", environment="production",
        metrics=ResourceMetrics(cpu_percent=90.0, memory_percent=85.0, disk_percent=75.0)
    ))
    
    # Message Queue
    nodes.append(InfrastructureNode(
        id="mq_rabbitmq", name="RabbitMQ", node_type=NodeType.MESSAGE_QUEUE,
        health_status=HealthStatus.HEALTHY, zone="data", environment="production",
        metrics=ResourceMetrics(memory_percent=40.0, connections_count=50)
    ))
    
    # Create test connections
    connections = []
    
    # External API -> Load Balancer
    connections.append(InfrastructureConnection(
        source_id="ext_api", target_id="lb_main", connection_type=ConnectionType.HTTPS,
        bandwidth_mbps=100.0, latency_ms=10.0, requests_count=1000
    ))
    
    # Load Balancer -> API Gateway
    connections.append(InfrastructureConnection(
        source_id="lb_main", target_id="api_gateway", connection_type=ConnectionType.HTTP,
        bandwidth_mbps=1000.0, latency_ms=2.0, requests_count=5000
    ))
    
    # API Gateway -> Services
    for i in range(3):
        connections.append(InfrastructureConnection(
            source_id="api_gateway", target_id=f"service_{i}", connection_type=ConnectionType.HTTP,
            bandwidth_mbps=500.0, latency_ms=5.0, requests_count=random.randint(100, 1000)
        ))
    
    # Services -> Cache
    for i in range(3):
        connections.append(InfrastructureConnection(
            source_id=f"service_{i}", target_id="cache_redis", connection_type=ConnectionType.TCP,
            bandwidth_mbps=100.0, latency_ms=1.0, requests_count=random.randint(50, 500)
        ))
    
    # Services -> Database
    for i in range(3):
        connections.append(InfrastructureConnection(
            source_id=f"service_{i}", target_id="db_postgres", connection_type=ConnectionType.DATABASE_CONNECTION,
            bandwidth_mbps=200.0, latency_ms=3.0, requests_count=random.randint(20, 200)
        ))
    
    # Services -> Message Queue
    connections.append(InfrastructureConnection(
        source_id="service_0", target_id="mq_rabbitmq", connection_type=ConnectionType.MESSAGE_QUEUE,
        bandwidth_mbps=50.0, latency_ms=2.0, requests_count=100
    ))
    
    return nodes, connections

def test_topology_processor():
    """Test topology data processing"""
    print_header("Topology Processor Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import TopologyProcessor
        
        # Generate test data
        nodes, connections = generate_test_infrastructure()
        
        # Test health score calculation
        health_scores = []
        for node in nodes:
            score = TopologyProcessor.calculate_node_health_score(node)
            health_scores.append(score)
        
        # Test critical path identification
        critical_paths = TopologyProcessor.identify_critical_paths(nodes, connections)
        
        # Test zone metrics calculation
        zones = set(node.zone for node in nodes)
        zone_metrics = {}
        for zone in zones:
            metrics = TopologyProcessor.calculate_zone_metrics(nodes, zone)
            zone_metrics[zone] = metrics
        
        success = (
            len(health_scores) == len(nodes) and
            all(0 <= score <= 100 for score in health_scores) and
            len(critical_paths) >= 0 and  # May or may not have critical paths
            len(zone_metrics) == len(zones) and
            all("node_count" in metrics for metrics in zone_metrics.values())
        )
        
        details = f"""Processing results:
Nodes processed: {len(nodes)}
Health scores calculated: {len(health_scores)}
Score range: {min(health_scores):.1f} - {max(health_scores):.1f}
Critical paths found: {len(critical_paths)}
Zones analyzed: {len(zone_metrics)}

Zone metrics:"""
        
        for zone, metrics in zone_metrics.items():
            details += f"\n- {zone}: {metrics.get('node_count', 0)} nodes, {metrics.get('healthy_nodes', 0)} healthy"
        
        print_result("Topology Processor", success, details)
        return success
        
    except Exception as e:
        print_result("Topology Processor", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_layout_engines():
    """Test topology layout engines"""
    print_header("Layout Engines Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import TopologyLayoutEngine
        
        # Generate test data
        nodes, connections = generate_test_infrastructure()
        
        width, height = 1200, 800
        
        # Test force-directed layout
        force_positions = TopologyLayoutEngine.generate_force_directed_layout(
            nodes, connections, width, height
        )
        
        # Test hierarchical layout
        hierarchical_positions = TopologyLayoutEngine.generate_hierarchical_layout(
            nodes, connections, width, height
        )
        
        success = (
            len(force_positions) == len(nodes) and
            len(hierarchical_positions) == len(nodes) and
            all(0 <= x <= width and 0 <= y <= height 
                for x, y in force_positions.values()) and
            all(0 <= x <= width and 0 <= y <= height 
                for x, y in hierarchical_positions.values())
        )
        
        details = f"""Layout generation:
Nodes positioned: {len(nodes)}
Force-directed positions: {len(force_positions)}
Hierarchical positions: {len(hierarchical_positions)}
Canvas size: {width}x{height}

Position validation:
- Force-directed bounds: ✓
- Hierarchical bounds: ✓
- All nodes positioned: ✓

Layout algorithms:
- Force-directed: Repulsive + attractive forces
- Hierarchical: Node type-based levels"""
        
        print_result("Layout Engines", success, details)
        return success
        
    except Exception as e:
        print_result("Layout Engines", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_topology_visualizer():
    """Test infrastructure topology visualizer"""
    print_header("Topology Visualizer Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            InfrastructureTopologyVisualizer, TopologyConfig, LayoutType, HealthStatus
        )
        
        # Create configuration
        config = TopologyConfig(
            layout_type=LayoutType.FORCE_DIRECTED,
            title="Test Infrastructure Topology",
            width=1200,
            height=800,
            show_metrics=True,
            show_health_status=True,
            group_by_zone=True
        )
        
        # Create visualizer
        visualizer = InfrastructureTopologyVisualizer(config)
        
        # Add test data
        nodes, connections = generate_test_infrastructure()
        
        for node in nodes:
            visualizer.add_node(node)
        
        for connection in connections:
            visualizer.add_connection(connection)
        
        # Generate topology data
        topology_data = visualizer.generate_topology_data()
        
        # Export data
        json_export = visualizer.export_data("json")
        
        # Get statistics
        stats = visualizer.get_statistics()
        
        success = (
            topology_data["type"] == "infrastructure_topology" and
            len(topology_data["nodes"]) == len(nodes) and
            len(topology_data["connections"]) == len(connections) and
            "zones" in topology_data and
            "summary" in topology_data and
            len(json_export) > 1000 and  # Substantial export
            stats["nodes_count"] == len(nodes)
        )
        
        summary = topology_data["summary"]
        
        details = f"""Topology visualization:
Type: {topology_data['type']}
Layout: {topology_data['layout']}
Nodes: {len(topology_data['nodes'])}
Connections: {len(topology_data['connections'])}
Zones: {len(topology_data['zones'])}

Summary statistics:
- Total nodes: {summary['totalNodes']}
- Total connections: {summary['totalConnections']}
- Overall health: {summary['overallHealth']:.1f}%
- Critical paths: {summary['criticalPaths']}
- Zones: {len(summary['zones'])}

Features validated:
- Position calculation: ✓
- Health scoring: ✓
- Zone grouping: ✓
- Metrics overlay: ✓
- Export functionality: ✓"""
        
        print_result("Topology Visualizer", success, details)
        return success
        
    except Exception as e:
        print_result("Topology Visualizer", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_filtering_and_grouping():
    """Test topology filtering and grouping"""
    print_header("Filtering and Grouping Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            InfrastructureTopologyVisualizer, TopologyConfig, NodeType, HealthStatus
        )
        
        # Create configuration with filters
        config = TopologyConfig(
            title="Filtered Topology",
            node_type_filter=[NodeType.SERVICE, NodeType.DATABASE],
            health_filter=[HealthStatus.HEALTHY, HealthStatus.WARNING],
            zone_filter=["app", "data"],
            group_by_zone=True
        )
        
        # Create visualizer
        visualizer = InfrastructureTopologyVisualizer(config)
        
        # Add test data
        nodes, connections = generate_test_infrastructure()
        
        for node in nodes:
            visualizer.add_node(node)
        
        for connection in connections:
            visualizer.add_connection(connection)
        
        # Generate filtered topology data
        topology_data = visualizer.generate_topology_data()
        
        # Count filtered results
        filtered_nodes = topology_data["nodes"]
        filtered_connections = topology_data["connections"]
        
        # Validate filtering
        node_types_in_result = set(node["type"] for node in filtered_nodes)
        health_statuses_in_result = set(node["healthStatus"] for node in filtered_nodes)
        zones_in_result = set(node["zone"] for node in filtered_nodes)
        
        success = (
            len(filtered_nodes) < len(nodes) and  # Should be filtered
            node_types_in_result.issubset({"service", "database"}) and
            health_statuses_in_result.issubset({"healthy", "warning"}) and
            zones_in_result.issubset({"app", "data"}) and
            len(filtered_connections) <= len(connections)  # Connections may be filtered too
        )
        
        details = f"""Filtering results:
Original nodes: {len(nodes)}
Filtered nodes: {len(filtered_nodes)}
Original connections: {len(connections)}
Filtered connections: {len(filtered_connections)}

Filter validation:
- Node types: {node_types_in_result} (expected: service, database)
- Health statuses: {health_statuses_in_result} (expected: healthy, warning)
- Zones: {zones_in_result} (expected: app, data)

Grouping features:
- Zone grouping: {'✓' if config.group_by_zone else '✗'}
- Zone metrics: {len(topology_data['zones'])} zones"""
        
        print_result("Filtering and Grouping", success, details)
        return success
        
    except Exception as e:
        print_result("Filtering and Grouping", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_resource_overlays():
    """Test resource overlay functionality"""
    print_header("Resource Overlays Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            InfrastructureTopologyVisualizer, TopologyConfig
        )
        
        # Create configuration with resource overlays
        config = TopologyConfig(
            title="Resource Overlay Topology",
            show_cpu_overlay=True,
            show_memory_overlay=True,
            show_network_overlay=True,
            show_metrics=True
        )
        
        # Create visualizer
        visualizer = InfrastructureTopologyVisualizer(config)
        
        # Add test data
        nodes, connections = generate_test_infrastructure()
        
        for node in nodes:
            visualizer.add_node(node)
        
        for connection in connections:
            visualizer.add_connection(connection)
        
        # Generate topology data
        topology_data = visualizer.generate_topology_data()
        
        # Validate resource data
        nodes_with_metrics = [
            node for node in topology_data["nodes"]
            if "metrics" in node and any(
                node["metrics"][metric] > 0
                for metric in ["cpu", "memory", "networkIn", "networkOut"]
            )
        ]
        
        connections_with_traffic = [
            conn for conn in topology_data["connections"]
            if "traffic" in conn and conn["traffic"]["requestsCount"] > 0
        ]
        
        success = (
            len(nodes_with_metrics) > 0 and
            len(connections_with_traffic) > 0 and
            all("healthScore" in node for node in topology_data["nodes"]) and
            config.show_metrics and
            "zones" in topology_data
        )
        
        details = f"""Resource overlay validation:
Nodes with metrics: {len(nodes_with_metrics)}/{len(topology_data['nodes'])}
Connections with traffic: {len(connections_with_traffic)}/{len(topology_data['connections'])}

Overlay features enabled:
- CPU overlay: {'✓' if config.show_cpu_overlay else '✗'}
- Memory overlay: {'✓' if config.show_memory_overlay else '✗'}
- Network overlay: {'✓' if config.show_network_overlay else '✗'}
- Metrics display: {'✓' if config.show_metrics else '✗'}

Health scoring: ✓
Zone aggregation: ✓
Traffic visualization: ✓"""
        
        print_result("Resource Overlays", success, details)
        return success
        
    except Exception as e:
        print_result("Resource Overlays", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run infrastructure topology tests"""
    print_header("Infrastructure Topology Test", 1)
    print("Testing infrastructure topology visualization with service dependencies and resource overlays")
    print("Validating topology processing, layout engines, visualization generation, and filtering")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['processor'] = test_topology_processor()
    test_results['layout'] = test_layout_engines()
    test_results['visualizer'] = test_topology_visualizer()
    test_results['filtering'] = test_filtering_and_grouping()
    test_results['overlays'] = test_resource_overlays()
    
    # Summary
    print_header("Infrastructure Topology Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Infrastructure topology system SUCCESSFUL")
        print(f"  🚀 Ready for Week 10 completion")
    else:
        print(f"  ❌ Infrastructure topology system FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
