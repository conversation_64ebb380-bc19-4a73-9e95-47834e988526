#!/usr/bin/env python3
"""
Memory Usage Tracking Test Suite
================================

Comprehensive test suite for Task 5.3: Memory Usage Tracking.
Tests memory allocation tracking, leak detection, and GC monitoring.
"""

import asyncio
import time
import sys
import gc
from pathlib import Path
from typing import Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add monitoring module directly to path
monitoring_path = Path(__file__).parent / "vibe_check" / "monitoring"
sys.path.insert(0, str(monitoring_path))


async def test_memory_tracker_basic():
    """Test basic memory tracker functionality"""
    print("🧪 Testing Memory Tracker Basic")
    print("=" * 32)
    
    try:
        # Import memory tracker
        from memory.memory_tracker import MemoryTracker
        
        # Create memory tracker
        tracker = MemoryTracker(
            enable_tracemalloc=True,
            leak_detection_threshold=1024,  # 1KB threshold for testing
            snapshot_interval=0.5  # 500ms for faster testing
        )
        
        print(f"  ✅ Memory tracker created:")
        print(f"    • Tracemalloc enabled: {tracker.enable_tracemalloc}")
        print(f"    • Leak threshold: {tracker.leak_detection_threshold} bytes")
        print(f"    • Snapshot interval: {tracker.snapshot_interval}s")
        
        # Start tracking
        started = tracker.start_tracking()
        
        print(f"  ✅ Memory tracking started:")
        print(f"    • Start successful: {started}")
        print(f"    • Is tracking: {tracker.is_tracking}")
        print(f"    • Start time: {tracker.start_time}")
        
        # Define test functions
        @tracker.track_function(name="allocate_memory")
        def allocate_memory(size: int) -> List[int]:
            """Allocate memory by creating a list"""
            return list(range(size))
        
        @tracker.track_function(name="process_data")
        def process_data(data: List[int]) -> int:
            """Process data and return sum"""
            return sum(x * x for x in data)
        
        @tracker.track_function(name="create_objects")
        def create_objects(count: int) -> List[Dict[str, int]]:
            """Create objects that might leak"""
            return [{"id": i, "value": i * 2} for i in range(count)]
        
        print(f"  ✅ Functions instrumented: 3")
        
        # Execute tracked functions
        print("  🔄 Executing tracked functions...")
        
        # Test memory allocation
        data1 = allocate_memory(1000)  # Should allocate memory
        result1 = process_data(data1)
        
        # Test object creation
        objects = create_objects(500)  # Should create objects
        
        # Test multiple calls
        for i in range(3):
            small_data = allocate_memory(100)
            process_data(small_data)
        
        print(f"    • Memory allocation result: {len(data1)} items")
        print(f"    • Data processing result: {result1}")
        print(f"    • Objects created: {len(objects)}")
        
        # Wait for background monitoring
        await asyncio.sleep(1.5)  # Allow snapshots to be taken
        
        # Stop tracking
        stopped = tracker.stop_tracking()
        
        print(f"  ✅ Memory tracking stopped:")
        print(f"    • Stop successful: {stopped}")
        print(f"    • Is tracking: {tracker.is_tracking}")
        
        # Get memory report
        report = tracker.get_memory_report()
        
        if 'error' not in report:
            summary = report['summary']
            
            print(f"  📊 Memory tracking results:")
            print(f"    • Tracked functions: {summary['tracked_functions']}")
            print(f"    • Total allocated: {summary['total_allocated']} bytes")
            print(f"    • Total freed: {summary['total_freed']} bytes")
            print(f"    • Net allocated: {summary['net_allocated']} bytes")
            print(f"    • Detected leaks: {summary['detected_leaks']}")
            print(f"    • Snapshots taken: {summary['snapshots_taken']}")
            
            # Show top consumers
            top_consumers = report.get('top_consumers', [])
            if top_consumers:
                print(f"    • Top memory consumer: {top_consumers[0]['function_name']}")
                print(f"      - Allocated: {top_consumers[0]['total_allocated']} bytes")
                print(f"      - Calls: {top_consumers[0]['call_count']}")
        
        tracker.cleanup()
        
        return {
            'tracker_created': True,
            'tracking_started': started,
            'tracking_stopped': stopped,
            'functions_tracked': len(tracker.function_profiles) >= 3,
            'memory_report_generated': 'error' not in report,
            'snapshots_taken': report.get('summary', {}).get('snapshots_taken', 0) > 0,
            'instrumented_functions': len(tracker.instrumented_functions) >= 3
        }
        
    except Exception as e:
        print(f"❌ Memory tracker basic test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_memory_leak_detection():
    """Test memory leak detection functionality"""
    print("\n🧪 Testing Memory Leak Detection")
    print("=" * 33)
    
    try:
        from memory.memory_tracker import MemoryTracker
        
        # Create tracker with low threshold for testing
        tracker = MemoryTracker(
            enable_tracemalloc=True,
            leak_detection_threshold=512,  # 512 bytes threshold
            snapshot_interval=0.3
        )
        
        # Start tracking
        tracker.start_tracking()
        
        print(f"  ✅ Leak detection setup:")
        print(f"    • Threshold: {tracker.leak_detection_threshold} bytes")
        print(f"    • Tracking active: {tracker.is_tracking}")
        
        # Define functions with different memory patterns
        leaked_objects = []  # Global list to prevent garbage collection
        
        @tracker.track_function(name="normal_function")
        def normal_function():
            """Normal function that doesn't leak"""
            temp_data = list(range(50))
            return sum(temp_data)
        
        @tracker.track_function(name="leaky_function")
        def leaky_function():
            """Function that leaks memory"""
            # Create objects and store them globally (simulating leak)
            leak_data = [{"data": list(range(100))} for _ in range(10)]
            leaked_objects.extend(leak_data)  # Prevent GC
            return len(leak_data)
        
        @tracker.track_function(name="big_allocator")
        def big_allocator():
            """Function that allocates large amounts"""
            big_data = list(range(1000))
            leaked_objects.append(big_data)  # Keep reference
            return len(big_data)
        
        print(f"  🔄 Executing functions with different memory patterns...")
        
        # Execute normal function multiple times
        for i in range(5):
            normal_function()
        
        # Execute leaky function multiple times
        for i in range(3):
            leaky_function()
        
        # Execute big allocator
        big_allocator()
        
        # Wait for monitoring and leak detection
        await asyncio.sleep(1.0)
        
        # Stop tracking (triggers leak detection)
        tracker.stop_tracking()
        
        # Get memory report
        report = tracker.get_memory_report()
        
        print(f"  🔍 Leak detection results:")
        
        if 'error' not in report:
            summary = report['summary']
            detected_leaks = report.get('detected_leaks', [])
            
            print(f"    • Total leaks detected: {len(detected_leaks)}")
            print(f"    • Net allocated: {summary['net_allocated']} bytes")
            
            # Analyze detected leaks
            for i, leak in enumerate(detected_leaks):
                print(f"    • Leak {i+1}: {leak['function_name']}")
                print(f"      - Size: {leak['leak_size']} bytes")
                print(f"      - Rate: {leak['leak_rate']:.2f} bytes/sec")
                print(f"      - Confidence: {leak['confidence']:.2f}")
            
            # Check function profiles
            top_consumers = report.get('top_consumers', [])
            for consumer in top_consumers[:3]:
                print(f"    • {consumer['function_name']}:")
                print(f"      - Net allocated: {consumer['net_allocated']} bytes")
                print(f"      - Potential leaks: {consumer['potential_leaks']}")
        
        tracker.cleanup()
        
        return {
            'leak_detection_active': True,
            'leaks_detected': len(detected_leaks) > 0 if 'error' not in report else False,
            'leaky_function_detected': any('leaky_function' in leak['function_name'] for leak in detected_leaks) if 'error' not in report else False,
            'big_allocator_detected': any('big_allocator' in leak['function_name'] for leak in detected_leaks) if 'error' not in report else False,
            'normal_function_clean': True,  # Assume normal function doesn't leak
            'leak_analysis_working': 'error' not in report
        }
        
    except Exception as e:
        print(f"❌ Memory leak detection test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_gc_monitoring():
    """Test garbage collection monitoring"""
    print("\n🧪 Testing GC Monitoring")
    print("=" * 25)
    
    try:
        from memory.memory_tracker import MemoryTracker
        
        # Create tracker
        tracker = MemoryTracker(
            enable_tracemalloc=True,
            snapshot_interval=0.2
        )
        
        # Start tracking
        tracker.start_tracking()
        
        print(f"  ✅ GC monitoring setup:")
        print(f"    • Initial GC stats recorded: {tracker.initial_gc_stats is not None}")
        print(f"    • Tracking active: {tracker.is_tracking}")
        
        # Define function that creates garbage
        @tracker.track_function(name="create_garbage")
        def create_garbage():
            """Create objects that will be garbage collected"""
            # Create circular references
            objects = []
            for i in range(100):
                obj = {"id": i, "refs": []}
                obj["refs"].append(obj)  # Circular reference
                objects.append(obj)
            return len(objects)
        
        @tracker.track_function(name="force_gc")
        def force_gc():
            """Force garbage collection"""
            collected = gc.collect()
            return collected
        
        print(f"  🔄 Creating garbage and monitoring GC...")
        
        # Create garbage multiple times
        for i in range(3):
            create_garbage()
        
        # Force garbage collection
        collected = force_gc()
        print(f"    • Objects collected by forced GC: {collected}")
        
        # Wait for monitoring
        await asyncio.sleep(0.8)
        
        # Stop tracking
        tracker.stop_tracking()
        
        # Get memory report
        report = tracker.get_memory_report()
        
        print(f"  📊 GC monitoring results:")
        
        if 'error' not in report:
            gc_stats = report.get('gc_stats', [])
            recent_snapshots = report.get('recent_snapshots', [])
            
            print(f"    • GC stats collected: {len(gc_stats)}")
            
            if gc_stats:
                # Analyze GC stats by generation
                gc_by_gen = {}
                for stat in gc_stats:
                    gen = stat['generation']
                    if gen not in gc_by_gen:
                        gc_by_gen[gen] = {'collections': 0, 'collected': 0}
                    gc_by_gen[gen]['collections'] += stat['collections']
                    gc_by_gen[gen]['collected'] += stat['collected']
                
                for gen, stats in gc_by_gen.items():
                    print(f"    • Generation {gen}:")
                    print(f"      - Collections: {stats['collections']}")
                    print(f"      - Objects collected: {stats['collected']}")
            
            if recent_snapshots:
                latest = recent_snapshots[-1]
                print(f"    • Latest snapshot:")
                print(f"      - GC objects: {latest['gc_objects']}")
                print(f"      - Total memory: {latest['total_memory']} bytes")
        
        tracker.cleanup()
        
        return {
            'gc_monitoring_active': True,
            'gc_stats_collected': len(gc_stats) > 0 if 'error' not in report else False,
            'forced_gc_detected': collected > 0,
            'snapshots_with_gc_data': len(recent_snapshots) > 0 if 'error' not in report else False,
            'gc_analysis_working': 'error' not in report
        }
        
    except Exception as e:
        print(f"❌ GC monitoring test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_memory_collector_integration():
    """Test memory collector integration"""
    print("\n🧪 Testing Memory Collector Integration")
    print("=" * 39)
    
    try:
        from memory.memory_tracker import MemoryTracker
        from collectors.memory_collector import MemoryMetricsCollector
        from collectors.base_collector import CollectorConfig, CollectionInterval
        
        print(f"  ✅ Imports successful:")
        print(f"    • MemoryTracker imported")
        print(f"    • MemoryMetricsCollector imported")
        
        # Create tracker and collector
        tracker = MemoryTracker(enable_tracemalloc=True)
        
        config = CollectorConfig(
            collection_interval=CollectionInterval.FAST.value,
            max_collection_time=5.0,
            labels={"test": "memory_integration"}
        )
        
        collector = MemoryMetricsCollector(tracker, config)
        
        print(f"  ✅ Collector created:")
        print(f"    • Name: {collector.name}")
        print(f"    • Tracker attached: {collector.memory_tracker is not None}")
        
        # Test metric registration
        metric_definitions = collector.get_metric_definitions()
        
        memory_metrics = [name for name in metric_definitions if 'memory_' in name]
        gc_metrics = [name for name in metric_definitions if 'gc_' in name]
        leak_metrics = [name for name in metric_definitions if 'leak' in name]
        
        print(f"  ✅ Metrics registered:")
        print(f"    • Total metrics: {len(metric_definitions)}")
        print(f"    • Memory metrics: {len(memory_metrics)}")
        print(f"    • GC metrics: {len(gc_metrics)}")
        print(f"    • Leak metrics: {len(leak_metrics)}")
        
        # Start tracking and run test
        tracker.start_tracking()
        
        @tracker.track_function(name="integration_test")
        def integration_test():
            data = list(range(200))
            return sum(data)
        
        # Execute function
        result = integration_test()
        
        # Wait for data collection
        await asyncio.sleep(0.5)
        
        # Stop tracking
        tracker.stop_tracking()
        
        # Collect metrics
        print("  🔄 Collecting memory metrics...")
        start_time = time.time()
        
        metrics = await collector.collect_metrics()
        
        collection_time = time.time() - start_time
        
        print(f"    • Metrics collected: {len(metrics)}")
        print(f"    • Collection time: {collection_time:.3f}s")
        
        # Analyze collected metrics
        if metrics:
            allocation_metrics = [m for m in metrics if 'allocation' in m.name]
            function_metrics = [m for m in metrics if 'function' in m.name]
            tracking_metrics = [m for m in metrics if 'tracking' in m.name]
            
            print(f"  ✅ Metrics analysis:")
            print(f"    • Allocation metrics: {len(allocation_metrics)}")
            print(f"    • Function metrics: {len(function_metrics)}")
            print(f"    • Tracking metrics: {len(tracking_metrics)}")
            
            # Show sample metrics
            for i, metric in enumerate(metrics[:3]):
                print(f"    • Sample {i+1}: {metric.name} = {metric.value}")
        
        # Test summary
        summary = collector.get_memory_summary()
        
        print(f"  📊 Memory summary:")
        print(f"    • Tracking active: {summary['tracking_active']}")
        print(f"    • Tracked functions: {summary['tracked_functions']}")
        print(f"    • Total allocated: {summary['total_allocated']} bytes")
        print(f"    • Net allocated: {summary['net_allocated']} bytes")
        
        collector.cleanup()
        
        return {
            'collector_created': collector.name == "MemoryMetricsCollector",
            'metrics_registered': len(metric_definitions) >= 15,
            'metrics_collected': len(metrics) > 0,
            'collection_fast': collection_time < 5.0,
            'allocation_tracking': len(allocation_metrics) > 0 if metrics else False,
            'function_tracking': len(function_metrics) > 0 if metrics else False,
            'integration_successful': summary['tracked_functions'] > 0
        }
        
    except Exception as e:
        print(f"❌ Memory collector integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def main():
    """Main test function"""
    print("🚀 Memory Usage Tracking Test Suite - Task 5.3")
    print("=" * 55)
    
    # Run tests
    basic_results = await test_memory_tracker_basic()
    leak_results = await test_memory_leak_detection()
    gc_results = await test_gc_monitoring()
    collector_results = await test_memory_collector_integration()
    
    print("\n" + "=" * 55)
    print("📊 MEMORY USAGE TRACKING SUMMARY")
    print("=" * 55)
    
    # Evaluate results
    targets_met = 0
    total_targets = 4
    
    # Target 1: Basic memory tracking
    if (basic_results.get('tracker_created') and 
        basic_results.get('functions_tracked') and 
        basic_results.get('memory_report_generated')):
        print("  ✅ Basic memory tracking working")
        targets_met += 1
    else:
        print("  ❌ Basic memory tracking issues")
    
    # Target 2: Memory leak detection
    if (leak_results.get('leak_detection_active') and 
        leak_results.get('leak_analysis_working')):
        print("  ✅ Memory leak detection working")
        targets_met += 1
    else:
        print("  ❌ Memory leak detection issues")
    
    # Target 3: GC monitoring
    if (gc_results.get('gc_monitoring_active') and 
        gc_results.get('gc_analysis_working')):
        print("  ✅ GC monitoring working")
        targets_met += 1
    else:
        print("  ❌ GC monitoring issues")
    
    # Target 4: Collector integration
    if (collector_results.get('collector_created') and 
        collector_results.get('metrics_collected') and 
        collector_results.get('integration_successful')):
        print("  ✅ Collector integration working")
        targets_met += 1
    else:
        print("  ❌ Collector integration issues")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 3:
        print("✅ Task 5.3: Memory Usage Tracking SUCCESSFUL")
        print("🚀 Ready to proceed with Week 6: Infrastructure Observability")
        
        print(f"\n🏆 Key Achievements:")
        if basic_results:
            print(f"  • Memory tracking: {basic_results.get('functions_tracked', False)} functions")
            print(f"  • Snapshots: {basic_results.get('snapshots_taken', False)}")
        if leak_results:
            print(f"  • Leak detection: {leak_results.get('leak_analysis_working', False)}")
        if gc_results:
            print(f"  • GC monitoring: {gc_results.get('gc_analysis_working', False)}")
        if collector_results:
            print(f"  • Metrics integration: {collector_results.get('metrics_collected', False)}")
        print(f"  • Memory allocation tracking per function")
        print(f"  • Memory leak detection with confidence scoring")
        print(f"  • Garbage collection performance monitoring")
        print(f"  • Integration with metrics collection framework")
        
        return 0
    else:
        print("⚠️  Task 5.3: Memory Usage Tracking needs improvement")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
