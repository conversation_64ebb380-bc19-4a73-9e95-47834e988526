#!/usr/bin/env python3
"""
Test and demonstration of the Metrics Collection Framework
==========================================================

This script tests and demonstrates the comprehensive metrics collection framework
that forms the foundation of our Prometheus replacement system.
"""

import asyncio
import time
import psutil
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass


@dataclass
class MetricDefinition:
    """Definition of a metric"""
    name: str
    description: str
    unit: str
    labels: Dict[str, str]
    collection_interval: float = 10.0


class MetricsCollector(ABC):
    """Base class for metrics collectors"""
    
    def __init__(self, name: str, interval: float = 10.0, enabled: bool = True):
        self.name = name
        self.interval = interval
        self.enabled = enabled
        self.running = False
        self.error_count = 0
        self.last_collection_time = 0.0
        self.collected_metrics = []
        
    @abstractmethod
    async def collect(self) -> Dict[str, float]:
        """Override this method to implement metric collection"""
        pass
    
    def get_metric_definitions(self) -> List[MetricDefinition]:
        """Get definitions of metrics this collector provides"""
        return []
    
    async def start_collection(self, duration: int = 30):
        """Start collecting metrics for testing"""
        self.running = True
        print(f"🔧 Starting {self.name} collector for {duration} seconds...")
        
        end_time = time.time() + duration
        collection_count = 0
        
        while time.time() < end_time and self.running:
            try:
                start_time = time.time()
                
                # Collect metrics
                metrics = await self.collect()
                self.collected_metrics.append({
                    'timestamp': time.time(),
                    'metrics': metrics
                })
                
                self.last_collection_time = time.time()
                collection_duration = time.time() - start_time
                collection_count += 1
                
                # Show progress every 5 seconds
                elapsed = int(time.time() - (end_time - duration))
                if elapsed % 5 == 0 and elapsed > 0:
                    print(f"  ⏰ {elapsed}s - {collection_count} collections, last duration: {collection_duration:.3f}s")
                
                # Wait for next collection
                sleep_time = max(0, self.interval - collection_duration)
                await asyncio.sleep(sleep_time)
                
            except Exception as e:
                self.error_count += 1
                print(f"  ❌ Error in {self.name}: {e}")
                await asyncio.sleep(self.interval)
        
        self.running = False
        print(f"  ✅ {self.name} completed {collection_count} collections")
        return collection_count
    
    def get_status(self) -> Dict[str, Any]:
        """Get collector status"""
        return {
            'name': self.name,
            'enabled': self.enabled,
            'running': self.running,
            'interval': self.interval,
            'error_count': self.error_count,
            'last_collection_time': self.last_collection_time,
            'total_collections': len(self.collected_metrics)
        }


class SystemMetricsCollector(MetricsCollector):
    """Collect system performance metrics"""
    
    def __init__(self, interval: float = 5.0):
        super().__init__("system", interval)
        self.boot_time = psutil.boot_time()
    
    async def collect(self) -> Dict[str, float]:
        """Collect system metrics"""
        metrics = {}
        
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=None)
        metrics['cpu_percent'] = cpu_percent
        metrics['cpu_count'] = psutil.cpu_count()
        
        # Load average (Unix-like systems)
        if hasattr(psutil, 'getloadavg'):
            load_avg = psutil.getloadavg()
            metrics['load_avg_1m'] = load_avg[0]
            metrics['load_avg_5m'] = load_avg[1]
            metrics['load_avg_15m'] = load_avg[2]
        
        # Memory metrics
        memory = psutil.virtual_memory()
        metrics['memory_total_bytes'] = memory.total
        metrics['memory_available_bytes'] = memory.available
        metrics['memory_used_bytes'] = memory.used
        metrics['memory_percent'] = memory.percent
        
        # Swap metrics
        swap = psutil.swap_memory()
        metrics['swap_total_bytes'] = swap.total
        metrics['swap_used_bytes'] = swap.used
        metrics['swap_percent'] = swap.percent
        
        # Disk metrics
        disk_usage = psutil.disk_usage('/')
        metrics['disk_total_bytes'] = disk_usage.total
        metrics['disk_used_bytes'] = disk_usage.used
        metrics['disk_free_bytes'] = disk_usage.free
        metrics['disk_percent'] = (disk_usage.used / disk_usage.total) * 100
        
        # Process metrics
        metrics['process_count'] = len(psutil.pids())
        
        # System uptime
        metrics['uptime_seconds'] = time.time() - self.boot_time
        
        return metrics
    
    def get_metric_definitions(self) -> List[MetricDefinition]:
        """Get system metric definitions"""
        return [
            MetricDefinition("cpu_percent", "CPU utilization percentage", "%", {"collector": "system"}),
            MetricDefinition("memory_percent", "Memory utilization percentage", "%", {"collector": "system"}),
            MetricDefinition("disk_percent", "Disk utilization percentage", "%", {"collector": "system"}),
            MetricDefinition("process_count", "Number of running processes", "count", {"collector": "system"}),
            MetricDefinition("uptime_seconds", "System uptime", "seconds", {"collector": "system"}),
        ]


class ProcessMetricsCollector(MetricsCollector):
    """Collect metrics for the current Python process"""
    
    def __init__(self, interval: float = 10.0):
        super().__init__("process", interval)
        self.process = psutil.Process()
        self.start_time = time.time()
    
    async def collect(self) -> Dict[str, float]:
        """Collect process metrics"""
        metrics = {}
        
        try:
            # Memory metrics
            memory_info = self.process.memory_info()
            metrics['memory_rss_bytes'] = memory_info.rss
            metrics['memory_vms_bytes'] = memory_info.vms
            
            # CPU metrics
            metrics['cpu_percent'] = self.process.cpu_percent()
            
            # Thread count
            metrics['threads'] = self.process.num_threads()
            
            # File descriptors (Unix-like systems)
            if hasattr(self.process, 'num_fds'):
                metrics['file_descriptors'] = self.process.num_fds()
            
            # Process uptime
            metrics['uptime_seconds'] = time.time() - self.start_time
            
            # I/O counters (if available)
            if hasattr(self.process, 'io_counters'):
                io_counters = self.process.io_counters()
                metrics['io_read_bytes'] = io_counters.read_bytes
                metrics['io_write_bytes'] = io_counters.write_bytes
                metrics['io_read_count'] = io_counters.read_count
                metrics['io_write_count'] = io_counters.write_count
            
        except psutil.NoSuchProcess:
            # Process no longer exists
            pass
        except Exception as e:
            print(f"Error collecting process metrics: {e}")
        
        return metrics
    
    def get_metric_definitions(self) -> List[MetricDefinition]:
        """Get process metric definitions"""
        return [
            MetricDefinition("memory_rss_bytes", "Resident Set Size memory", "bytes", {"collector": "process"}),
            MetricDefinition("cpu_percent", "Process CPU utilization", "%", {"collector": "process"}),
            MetricDefinition("threads", "Number of threads", "count", {"collector": "process"}),
            MetricDefinition("uptime_seconds", "Process uptime", "seconds", {"collector": "process"}),
        ]


class CodeQualityCollector(MetricsCollector):
    """Collect code quality metrics from project analysis"""
    
    def __init__(self, project_path: str, interval: float = 60.0):
        super().__init__("code_quality", interval)
        self.project_path = Path(project_path)
        self.last_analysis_result = None
    
    async def collect(self) -> Dict[str, float]:
        """Collect code quality metrics"""
        try:
            # Simulate code analysis (in real implementation, this would call vibe_check analyzer)
            metrics = {}
            
            # Count Python files
            python_files = list(self.project_path.rglob("*.py"))
            metrics['files_total'] = len(python_files)
            
            # Count total lines
            total_lines = 0
            for file_path in python_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        total_lines += len(f.readlines())
                except:
                    pass
            
            metrics['lines_total'] = total_lines
            
            # Simulate other metrics
            metrics['complexity_avg'] = 15.5  # Would be calculated from actual analysis
            metrics['issues_total'] = 42  # Would be from actual analysis
            metrics['quality_score'] = 7.8  # Would be calculated
            
            return metrics
            
        except Exception as e:
            print(f"Error collecting code quality metrics: {e}")
            return {}
    
    def get_metric_definitions(self) -> List[MetricDefinition]:
        """Get code quality metric definitions"""
        return [
            MetricDefinition("complexity_avg", "Average code complexity", "score", {"collector": "code_quality"}),
            MetricDefinition("issues_total", "Total number of issues", "count", {"collector": "code_quality"}),
            MetricDefinition("files_total", "Total number of files", "count", {"collector": "code_quality"}),
            MetricDefinition("quality_score", "Overall quality score", "score", {"collector": "code_quality"}),
        ]


async def test_metrics_collection_framework():
    """Test the metrics collection framework"""
    print("🧪 Testing Metrics Collection Framework")
    print("=" * 50)
    
    # Create collectors
    system_collector = SystemMetricsCollector(interval=2.0)
    process_collector = ProcessMetricsCollector(interval=3.0)
    code_collector = CodeQualityCollector(".", interval=5.0)
    
    collectors = [system_collector, process_collector, code_collector]
    
    # Show metric definitions
    print("📋 Available Metrics:")
    for collector in collectors:
        definitions = collector.get_metric_definitions()
        print(f"\n🔧 {collector.name.upper()} Collector:")
        for defn in definitions:
            print(f"  • {defn.name} ({defn.unit}): {defn.description}")
    
    print("\n" + "=" * 50)
    
    # Test each collector
    for collector in collectors:
        await collector.start_collection(duration=10)
        
        # Show results
        status = collector.get_status()
        print(f"📊 {collector.name.upper()} Results:")
        print(f"  • Collections: {status['total_collections']}")
        print(f"  • Errors: {status['error_count']}")
        print(f"  • Last collection: {status['last_collection_time']:.1f}")
        
        if collector.collected_metrics:
            latest = collector.collected_metrics[-1]['metrics']
            print(f"  • Latest metrics:")
            for name, value in list(latest.items())[:5]:  # Show first 5
                print(f"    - {name}: {value}")
            if len(latest) > 5:
                print(f"    - ... and {len(latest) - 5} more")
        
        print()
    
    print("✅ Metrics Collection Framework Test Complete!")
    print("🎉 All collectors working properly!")


async def main():
    """Main test function"""
    try:
        await test_metrics_collection_framework()
        return 0
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
