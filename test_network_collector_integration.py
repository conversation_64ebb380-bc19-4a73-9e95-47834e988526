#!/usr/bin/env python3
"""
Network Collector Integration Test
==================================

Test network collector integration with TSDB pipeline for network monitoring metrics.
"""

import asyncio
import time
import tempfile
import shutil
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

async def test_network_collector_basic():
    """Test basic network collector functionality"""
    print_header("Network Collector Basic Test", 2)
    
    try:
        from vibe_check.monitoring.collectors.network_collector import NetworkMetricsCollector
        
        # Create collector
        collector = NetworkMetricsCollector()
        print(f"  ✅ Collector created: {type(collector)}")
        
        # Test collection
        metrics = await collector.collect_metrics()
        print(f"  ✅ Metrics collected: {len(metrics)} metrics")
        
        # Show sample metrics
        for i, metric in enumerate(metrics[:5]):  # Show first 5
            print(f"    • Metric {i}: {metric.name} = {metric.value}")
            print(f"      Labels: {metric.labels}")
            print(f"      Timestamp: {metric.timestamp}")
        
        success = len(metrics) > 0
        details = f"""Metrics collected: {len(metrics)}
Collector type: {type(collector).__name__}
Sample metrics: {[m.name for m in metrics[:5]]}"""
        
        print_result("Network Collector Basic", success, details)
        return success
        
    except Exception as e:
        print_result("Network Collector Basic", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_network_collector_with_tsdb():
    """Test network collector integration with TSDB"""
    print_header("Network Collector TSDB Integration", 2)
    
    try:
        from vibe_check.monitoring.collectors.network_collector import NetworkMetricsCollector
        from vibe_check.monitoring.storage.time_series_engine import (
            TimeSeriesStorageEngine, TSDBConfig
        )
        
        # Create temporary storage
        temp_dir = Path(tempfile.mkdtemp())
        config = TSDBConfig(
            data_dir=temp_dir / "tsdb",
            flush_interval_seconds=1.0  # Fast flush for testing
        )
        
        # Initialize TSDB
        tsdb = TimeSeriesStorageEngine(config)
        
        # Create collector
        collector = NetworkMetricsCollector()
        
        # Collect metrics
        metrics = await collector.collect_metrics()
        
        # Store metrics in TSDB
        stored_count = 0
        for metric in metrics:
            result = await tsdb.ingest_sample(
                metric_name=metric.name,
                value=metric.value,
                labels=metric.labels,
                timestamp=metric.timestamp
            )
            if result:
                stored_count += 1
        
        # Wait for flush
        await asyncio.sleep(2.0)
        
        # Query back the data
        retrieved_series = []
        for metric in metrics[:3]:  # Test first 3 metrics
            if metric.timestamp is not None:
                series_list = await tsdb.query_range(
                    metric_name=metric.name,
                    start_time=metric.timestamp - 1,
                    end_time=metric.timestamp + 1
                )
                retrieved_series.extend(series_list)
        
        total_samples = sum(len(series.samples) for series in retrieved_series)
        
        success = stored_count > 0 and total_samples > 0
        details = f"""Metrics collected: {len(metrics)}
Metrics stored: {stored_count}
Series retrieved: {len(retrieved_series)}
Total samples: {total_samples}
Storage path: {config.data_dir}"""
        
        print_result("Network TSDB Integration", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Network TSDB Integration", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_network_collector_with_manager():
    """Test network collector with MetricsManager"""
    print_header("Network Collector Manager Integration", 2)
    
    try:
        from vibe_check.monitoring.collectors.metrics_manager import (
            MetricsManager, MetricsManagerConfig
        )
        from vibe_check.monitoring.collectors.network_collector import NetworkMetricsCollector
        
        # Create temporary storage
        temp_dir = Path(tempfile.mkdtemp())
        
        # Configure manager with network enabled
        config = MetricsManagerConfig(
            enable_system_metrics=False,  # Disable system to focus on network
            enable_code_quality_metrics=False,
            tsdb_data_dir=temp_dir / "tsdb",
            ingestion_flush_interval=1.0  # Fast flush for testing
        )
        
        # Initialize manager
        manager = MetricsManager(config)
        
        # Manually register network collector
        network_collector = NetworkMetricsCollector()
        manager.registry.register_collector(network_collector)
        
        # Start manager
        await manager.start()
        
        # Let it run briefly
        await asyncio.sleep(3.0)
        
        # Get stats
        stats = manager.get_manager_stats()
        
        # Stop manager
        await manager.stop()
        
        # Validate results
        collectors_info = stats.get('collectors', {})
        total_collectors = collectors_info.get('total_collectors', 0)
        metrics_ingested = stats.get('metrics_ingested', 0)
        
        success = (
            total_collectors >= 1 and
            metrics_ingested > 0 and
            stats.get('ingestion_errors', 0) == 0
        )
        
        details = f"""Total collectors: {total_collectors}
Metrics ingested: {metrics_ingested}
Ingestion rate: {stats.get('ingestion_rate', 0):.1f} metrics/s
Ingestion errors: {stats.get('ingestion_errors', 0)}
Buffer size: {stats.get('buffer_size', 0)}"""
        
        print_result("Network Manager Integration", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Network Manager Integration", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_network_performance():
    """Test network collector performance"""
    print_header("Network Collector Performance", 2)
    
    try:
        from vibe_check.monitoring.collectors.network_collector import NetworkMetricsCollector
        
        # Create collector
        collector = NetworkMetricsCollector()
        
        # Benchmark collection performance
        iterations = 50  # Moderate iterations for network metrics
        start_time = time.time()
        
        total_metrics = 0
        for _ in range(iterations):
            metrics = await collector.collect_metrics()
            total_metrics += len(metrics)
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = (total_time / iterations) * 1000  # Convert to ms
        
        # Performance targets for network metrics
        target_collection_time = 200.0  # 200ms target (network operations can be slower)
        target_throughput = 5.0  # 5 collections/second
        
        actual_throughput = iterations / total_time
        
        success = (
            avg_time < target_collection_time and
            actual_throughput > target_throughput
        )
        
        details = f"""Collection iterations: {iterations}
Total metrics collected: {total_metrics}
Average collection time: {avg_time:.1f}ms (target: <{target_collection_time}ms)
Throughput: {actual_throughput:.1f} collections/s (target: >{target_throughput}/s)
Total benchmark time: {total_time:.2f}s"""
        
        print_result("Network Performance", success, details)
        return success
        
    except Exception as e:
        print_result("Network Performance", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run network collector integration tests"""
    print_header("Network Collector Integration Test", 1)
    print("Testing network collector integration with TSDB pipeline")
    print("Validating network monitoring metrics and data pipeline functionality")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['basic'] = await test_network_collector_basic()
    test_results['tsdb'] = await test_network_collector_with_tsdb()
    test_results['manager'] = await test_network_collector_with_manager()
    test_results['performance'] = await test_network_performance()
    
    # Summary
    print_header("Network Integration Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Network collector integration SUCCESSFUL")
        print(f"  🚀 Ready for service discovery integration")
    else:
        print(f"  ❌ Network collector integration FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
