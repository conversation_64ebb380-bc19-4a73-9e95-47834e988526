#!/usr/bin/env python3
"""
Service Discovery Integration Test
==================================

Test service discovery integration with health checks and dynamic configuration.
"""

import asyncio
import time
import tempfile
import shutil
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

async def test_service_discovery_basic():
    """Test basic service discovery functionality"""
    print_header("Service Discovery Basic Test", 2)
    
    try:
        from vibe_check.monitoring.discovery import (
            ServiceDiscovery, ServiceEndpoint, HealthCheck, ServiceStatus
        )
        
        # Create service discovery
        discovery = ServiceDiscovery(check_interval=1.0)
        print(f"  ✅ Service discovery created: {type(discovery)}")
        
        # Register a test service
        endpoint = ServiceEndpoint(
            name="test_service",
            host="127.0.0.1",
            port=80,
            protocol="tcp"
        )
        
        health_check = HealthCheck(
            method="GET",
            path="/health",
            interval=1.0,
            timeout=1.0
        )
        
        service_id = discovery.register_service(endpoint, health_check)
        print(f"  ✅ Service registered: {service_id}")
        
        # Start discovery
        await discovery.start()
        print(f"  ✅ Service discovery started")
        
        # Let it run briefly
        await asyncio.sleep(2.0)
        
        # Get summary
        summary = discovery.get_service_summary()
        
        # Stop discovery
        await discovery.stop()
        print(f"  ✅ Service discovery stopped")
        
        success = (
            summary['total_services'] > 0 and
            discovery.running == False
        )
        
        details = f"""Total services: {summary['total_services']}
Status counts: {summary['status_counts']}
Discovery running: {discovery.running}
Service ID: {service_id}"""
        
        print_result("Service Discovery Basic", success, details)
        return success
        
    except Exception as e:
        print_result("Service Discovery Basic", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_service_discovery_methods():
    """Test different service discovery methods"""
    print_header("Service Discovery Methods Test", 2)
    
    try:
        from vibe_check.monitoring.discovery import (
            ServiceDiscovery, DiscoveryMethod
        )
        
        discovery = ServiceDiscovery()
        
        # Test static discovery
        static_config = {
            'services': [
                {
                    'name': 'web_server',
                    'host': '127.0.0.1',
                    'port': 8080,
                    'protocol': 'http',
                    'tags': ['web', 'api']
                },
                {
                    'name': 'database',
                    'host': '127.0.0.1',
                    'port': 5432,
                    'protocol': 'tcp',
                    'tags': ['db', 'postgres']
                }
            ]
        }
        
        static_services = await discovery.discover_services(
            DiscoveryMethod.STATIC, static_config
        )
        print(f"  ✅ Static discovery found {len(static_services)} services")
        
        # Test DNS discovery
        dns_config = {
            'domain': 'localhost',
            'service': 'test_service',
            'port': 80
        }
        
        dns_services = await discovery.discover_services(
            DiscoveryMethod.DNS, dns_config
        )
        print(f"  ✅ DNS discovery found {len(dns_services)} services")
        
        # Test TCP discovery
        tcp_config = {
            'host': '127.0.0.1',
            'ports': [22, 80]  # Common ports that might be open
        }
        
        tcp_services = await discovery.discover_services(
            DiscoveryMethod.TCP, tcp_config
        )
        print(f"  ✅ TCP discovery found {len(tcp_services)} services")
        
        total_discovered = len(static_services) + len(dns_services) + len(tcp_services)
        
        success = total_discovered > 0
        details = f"""Static services: {len(static_services)}
DNS services: {len(dns_services)}
TCP services: {len(tcp_services)}
Total discovered: {total_discovered}"""
        
        print_result("Service Discovery Methods", success, details)
        return success
        
    except Exception as e:
        print_result("Service Discovery Methods", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_health_checks():
    """Test health check functionality"""
    print_header("Health Checks Test", 2)
    
    try:
        from vibe_check.monitoring.discovery import (
            ServiceDiscovery, ServiceEndpoint, HealthCheck, ServiceStatus
        )
        
        discovery = ServiceDiscovery(check_interval=0.5)
        
        # Register services with different health check configurations
        services = []
        
        # TCP service (localhost should be reachable)
        tcp_endpoint = ServiceEndpoint(
            name="localhost_tcp",
            host="127.0.0.1",
            port=22,  # SSH port, commonly open
            protocol="tcp"
        )
        tcp_health = HealthCheck(timeout=1.0)
        services.append(discovery.register_service(tcp_endpoint, tcp_health))
        
        # Unreachable service
        unreachable_endpoint = ServiceEndpoint(
            name="unreachable_service",
            host="*********",  # RFC5737 test address
            port=9999,
            protocol="tcp"
        )
        unreachable_health = HealthCheck(timeout=1.0)
        services.append(discovery.register_service(unreachable_endpoint, unreachable_health))
        
        # Start health checking
        await discovery.start()
        
        # Wait for health checks to complete
        await asyncio.sleep(3.0)
        
        # Check results
        summary = discovery.get_service_summary()
        healthy_services = discovery.get_healthy_services()
        
        await discovery.stop()
        
        success = (
            summary['total_services'] == 2 and
            len(summary['status_counts']) > 0
        )
        
        details = f"""Total services: {summary['total_services']}
Status counts: {summary['status_counts']}
Healthy services: {len(healthy_services)}
Registered services: {len(services)}"""
        
        print_result("Health Checks", success, details)
        return success
        
    except Exception as e:
        print_result("Health Checks", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_service_events():
    """Test service event callbacks"""
    print_header("Service Events Test", 2)
    
    try:
        from vibe_check.monitoring.discovery import (
            ServiceDiscovery, ServiceEndpoint, HealthCheck
        )
        
        # Event tracking
        events = {
            'service_up': 0,
            'service_down': 0,
            'service_discovered': 0
        }
        
        def on_service_up(instance):
            events['service_up'] += 1
            print(f"    📈 Service UP: {instance.endpoint.name}")
        
        def on_service_down(instance):
            events['service_down'] += 1
            print(f"    📉 Service DOWN: {instance.endpoint.name}")
        
        def on_service_discovered(instance):
            events['service_discovered'] += 1
            print(f"    🔍 Service DISCOVERED: {instance.endpoint.name}")
        
        discovery = ServiceDiscovery(check_interval=0.5)
        discovery.on_service_up = on_service_up
        discovery.on_service_down = on_service_down
        discovery.on_service_discovered = on_service_discovered
        
        # Register a service
        endpoint = ServiceEndpoint(
            name="event_test_service",
            host="127.0.0.1",
            port=22,
            protocol="tcp"
        )
        
        service_id = discovery.register_service(endpoint)
        
        # Start and let it run
        await discovery.start()
        await asyncio.sleep(2.0)
        await discovery.stop()
        
        success = events['service_discovered'] > 0
        details = f"""Service discovered events: {events['service_discovered']}
Service up events: {events['service_up']}
Service down events: {events['service_down']}
Total events: {sum(events.values())}"""
        
        print_result("Service Events", success, details)
        return success
        
    except Exception as e:
        print_result("Service Events", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run service discovery integration tests"""
    print_header("Service Discovery Integration Test", 1)
    print("Testing service discovery with health checks and dynamic configuration")
    print("Validating automatic service discovery and monitoring capabilities")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['basic'] = await test_service_discovery_basic()
    test_results['methods'] = await test_service_discovery_methods()
    test_results['health_checks'] = await test_health_checks()
    test_results['events'] = await test_service_events()
    
    # Summary
    print_header("Service Discovery Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Service discovery integration SUCCESSFUL")
        print(f"  🚀 Ready for high-frequency data ingestion")
    else:
        print(f"  ❌ Service discovery integration FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
