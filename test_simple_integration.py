#!/usr/bin/env python3
"""
Simple Integration Test - Phase 2 Debug
========================================

Simplified test to debug the integration issues.
"""

import asyncio
import time
import tempfile
import shutil
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

async def test_basic_tsdb():
    """Test basic TSDB functionality"""
    print("🔧 Testing Basic TSDB...")
    
    try:
        from vibe_check.monitoring.storage.time_series_engine import (
            TimeSeriesStorageEngine, TSDBConfig
        )
        
        # Create temporary storage
        temp_dir = Path(tempfile.mkdtemp())
        config = TSDBConfig(data_dir=temp_dir / "tsdb")
        
        # Initialize TSDB
        tsdb = TimeSeriesStorageEngine(config)
        print(f"  ✅ TSDB created: {type(tsdb)}")
        
        # Test basic ingestion
        result = await tsdb.ingest_sample(
            metric_name="test_metric",
            value=42.0,
            labels={"test": "true"},
            timestamp=time.time()
        )
        print(f"  ✅ Sample ingested: {result}")
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return True
        
    except Exception as e:
        print(f"  ❌ TSDB test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_basic_collector():
    """Test basic collector functionality"""
    print("🔧 Testing Basic Collector...")
    
    try:
        from vibe_check.monitoring.collectors.system_collector import SystemMetricsCollector
        
        # Initialize collector
        collector = SystemMetricsCollector()
        print(f"  ✅ Collector created: {type(collector)}")
        
        # Test collection
        metrics = await collector.collect_metrics()
        print(f"  ✅ Metrics collected: {len(metrics)} metrics")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Collector test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_basic_manager():
    """Test basic manager functionality"""
    print("🔧 Testing Basic Manager...")
    
    try:
        from vibe_check.monitoring.collectors.metrics_manager import (
            MetricsManager, MetricsManagerConfig
        )
        
        # Create temporary storage
        temp_dir = Path(tempfile.mkdtemp())
        
        # Configure manager
        config = MetricsManagerConfig(
            enable_system_metrics=True,
            tsdb_data_dir=temp_dir / "tsdb"
        )
        
        # Initialize manager
        manager = MetricsManager(config)
        print(f"  ✅ Manager created: {type(manager)}")
        
        # Test start/stop
        await manager.start()
        print(f"  ✅ Manager started")
        
        await asyncio.sleep(1.0)  # Let it run briefly
        
        await manager.stop()
        print(f"  ✅ Manager stopped")
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return True
        
    except Exception as e:
        print(f"  ❌ Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run simple integration tests"""
    print("🚀 Simple Integration Test - Phase 2 Debug")
    print("=" * 50)
    
    # Run basic tests
    results = {}
    results['tsdb'] = await test_basic_tsdb()
    results['collector'] = await test_basic_collector()
    results['manager'] = await test_basic_manager()
    
    # Summary
    print("\n📊 Test Results:")
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
