#!/usr/bin/env python3
"""
Time-Series Storage Engine Test Suite
====================================

Comprehensive test suite for Task 4.1: Time-Series Storage Engine.
Tests ingestion performance, query functionality, and PromQL compatibility.
"""

import asyncio
import time
import sys
import tempfile
import shutil
import random
from pathlib import Path
from typing import Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))


async def test_time_series_storage_basic():
    """Test basic time-series storage functionality"""
    print("🧪 Testing Time-Series Storage (Basic)")
    print("=" * 39)
    
    try:
        from vibe_check.monitoring.storage import TimeSeriesStorageEngine, TSDBConfig, MetricSample
        
        # Create temporary storage
        with tempfile.TemporaryDirectory() as temp_dir:
            config = TSDBConfig(
                data_dir=Path(temp_dir),
                max_series_in_memory=100,
                max_samples_per_series=1000,
                write_batch_size=10
            )
            
            engine = TimeSeriesStorageEngine(config)
            
            # Test basic ingestion
            current_time = time.time()
            
            await engine.ingest_sample("cpu_usage", 45.2, {"host": "server1"}, current_time)
            await engine.ingest_sample("cpu_usage", 47.8, {"host": "server1"}, current_time + 10)
            await engine.ingest_sample("cpu_usage", 52.1, {"host": "server2"}, current_time)
            await engine.ingest_sample("memory_usage", 78.5, {"host": "server1"}, current_time)
            
            # Force flush
            await engine._flush_write_buffer()
            
            print(f"  ✅ Basic ingestion:")
            print(f"    • Samples ingested: 4")
            print(f"    • Series created: {len(engine.series_by_id)}")
            print(f"    • Metrics: {list(engine.series_by_name.keys())}")
            
            # Test instant queries
            cpu_samples = await engine.query_instant("cpu_usage", current_time + 5)
            memory_samples = await engine.query_instant("memory_usage", current_time + 5)
            
            print(f"  ✅ Instant queries:")
            print(f"    • CPU samples found: {len(cpu_samples)}")
            print(f"    • Memory samples found: {len(memory_samples)}")
            
            # Test range queries
            cpu_series = await engine.query_range("cpu_usage", current_time - 5, current_time + 15)
            
            print(f"  ✅ Range queries:")
            print(f"    • CPU series found: {len(cpu_series)}")
            if cpu_series:
                print(f"    • Samples in first series: {len(cpu_series[0].samples)}")
            
            # Test statistics
            stats = engine.get_stats()
            print(f"  📊 Engine statistics:")
            print(f"    • Series count: {stats['series_count']}")
            print(f"    • Total samples: {stats['total_samples']}")
            print(f"    • Ingestion rate: {stats['ingestion_rate']:.1f} samples/sec")
            
            await engine.shutdown()
            
            return {
                'basic_ingestion': len(engine.series_by_id) >= 3,
                'instant_queries': len(cpu_samples) > 0 and len(memory_samples) > 0,
                'range_queries': len(cpu_series) > 0,
                'series_count': stats['series_count'],
                'total_samples': stats['total_samples']
            }
    
    except Exception as e:
        print(f"❌ Time-series storage test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_high_frequency_ingestion():
    """Test high-frequency data ingestion performance"""
    print("\n🧪 Testing High-Frequency Ingestion")
    print("=" * 36)
    
    try:
        from vibe_check.monitoring.storage import TimeSeriesStorageEngine, TSDBConfig
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config = TSDBConfig(
                data_dir=Path(temp_dir),
                max_series_in_memory=1000,
                write_batch_size=100,
                max_ingestion_rate=5000
            )
            
            engine = TimeSeriesStorageEngine(config)
            
            # Generate high-frequency test data
            print("  🔄 Generating test metrics...")
            
            metrics = []
            base_time = time.time()
            
            # Create 1000 samples across 10 metrics
            for i in range(1000):
                metric_name = f"test_metric_{i % 10}"
                timestamp = base_time + (i * 0.1)  # 10 samples per second
                value = random.uniform(0, 100)
                labels = {"instance": f"host_{i % 5}", "job": "test"}
                
                metrics.append({
                    'name': metric_name,
                    'value': value,
                    'labels': labels,
                    'timestamp': timestamp
                })
            
            # Test batch ingestion performance
            print("  ⚡ Testing batch ingestion...")
            start_time = time.time()
            
            from vibe_check.monitoring.storage import ingest_metrics
            ingested_count = await ingest_metrics(engine, metrics)
            
            ingestion_time = time.time() - start_time
            ingestion_rate = ingested_count / ingestion_time
            
            print(f"    • Metrics ingested: {ingested_count}")
            print(f"    • Ingestion time: {ingestion_time:.3f}s")
            print(f"    • Ingestion rate: {ingestion_rate:.1f} samples/sec")
            
            # Force flush and get stats
            await engine._flush_write_buffer()
            stats = engine.get_stats()
            
            print(f"  📊 Final statistics:")
            print(f"    • Series created: {stats['series_count']}")
            print(f"    • Total samples: {stats['total_samples']}")
            print(f"    • Memory usage: {stats['memory_usage_mb']:.1f} MB")
            
            await engine.shutdown()
            
            return {
                'ingested_count': ingested_count,
                'ingestion_rate': ingestion_rate,
                'series_count': stats['series_count'],
                'total_samples': stats['total_samples'],
                'target_met': ingestion_rate >= 1000  # Target: 1000+ samples/sec
            }
    
    except Exception as e:
        print(f"❌ High-frequency ingestion test failed: {e}")
        return {}


async def test_promql_engine():
    """Test PromQL query engine functionality"""
    print("\n🧪 Testing PromQL Engine")
    print("=" * 24)
    
    try:
        from vibe_check.monitoring.storage import TimeSeriesStorageEngine, TSDBConfig
        from vibe_check.monitoring.query import PromQLEngine, create_promql_engine
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config = TSDBConfig(data_dir=Path(temp_dir))
            storage = TimeSeriesStorageEngine(config)
            engine = await create_promql_engine(storage)
            
            # Ingest test data for PromQL testing
            base_time = time.time()
            
            # Create counter-like data for rate() function
            for i in range(10):
                timestamp = base_time + (i * 10)  # 10-second intervals
                value = i * 100  # Increasing counter
                
                await storage.ingest_sample(
                    "http_requests_total",
                    value,
                    {"method": "GET", "status": "200"},
                    timestamp
                )
            
            await storage._flush_write_buffer()
            
            print(f"  ✅ Test data ingested:")
            print(f"    • Metric: http_requests_total")
            print(f"    • Samples: 10 (counter data)")
            print(f"    • Time range: {10 * 10}s")
            
            # Test instant query
            print("  🔄 Testing instant queries...")
            instant_results = await engine.execute_query(
                "http_requests_total",
                timestamp=base_time + 50
            )
            
            print(f"    • Instant query results: {len(instant_results)}")
            if instant_results:
                print(f"    • Latest value: {instant_results[0].values[0][1]}")
            
            # Test range query
            print("  ⚡ Testing range queries...")
            range_results = await engine.execute_query(
                "http_requests_total",
                start_time=base_time,
                end_time=base_time + 100
            )
            
            print(f"    • Range query results: {len(range_results)}")
            if range_results:
                print(f"    • Samples in range: {len(range_results[0].values)}")
            
            # Test PromQL functions
            print("  🔧 Testing PromQL functions...")
            
            # Test rate() function
            rate_results = await engine.execute_query(
                "rate(http_requests_total[60s])",
                start_time=base_time,
                end_time=base_time + 100
            )
            
            print(f"    • rate() function results: {len(rate_results)}")
            
            # Test supported functions
            supported_functions = engine.get_supported_functions()
            print(f"    • Supported functions: {supported_functions}")
            
            await storage.shutdown()
            
            return {
                'instant_queries': len(instant_results) > 0,
                'range_queries': len(range_results) > 0 and len(range_results[0].values) > 0,
                'promql_functions': len(rate_results) > 0,
                'supported_functions': len(supported_functions) >= 5
            }
    
    except Exception as e:
        print(f"❌ PromQL engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_cache_integration():
    """Test cache integration with time-series storage"""
    print("\n🧪 Testing Cache Integration")
    print("=" * 30)
    
    try:
        from vibe_check.monitoring.storage import TimeSeriesStorageEngine, TSDBConfig
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config = TSDBConfig(
                data_dir=Path(temp_dir),
                enable_query_cache=True,
                cache_ttl_seconds=60.0
            )
            
            engine = TimeSeriesStorageEngine(config)
            
            # Ingest test data
            base_time = time.time()
            for i in range(50):
                await engine.ingest_sample(
                    "cache_test_metric",
                    float(i),
                    {"test": "cache"},
                    base_time + i
                )
            
            await engine._flush_write_buffer()
            
            # First query (cache miss)
            print("  🔄 First query (cache miss)...")
            start_time = time.time()
            
            results1 = await engine.query_range(
                "cache_test_metric",
                base_time,
                base_time + 50
            )
            
            first_query_time = time.time() - start_time
            
            print(f"    • Results: {len(results1)} series")
            print(f"    • Query time: {first_query_time:.4f}s")
            
            # Second query (cache hit)
            print("  ⚡ Second query (cache hit)...")
            start_time = time.time()
            
            results2 = await engine.query_range(
                "cache_test_metric",
                base_time,
                base_time + 50
            )
            
            second_query_time = time.time() - start_time
            
            print(f"    • Results: {len(results2)} series")
            print(f"    • Query time: {second_query_time:.4f}s")
            
            # Calculate speedup
            if second_query_time > 0:
                speedup = first_query_time / second_query_time
            else:
                speedup = float('inf')
            
            print(f"  🚀 Cache performance:")
            print(f"    • Speedup: {speedup:.1f}x")
            print(f"    • Time reduction: {((first_query_time - second_query_time) / first_query_time * 100):.1f}%")
            
            # Test cache statistics
            cache_stats = engine.cache_manager.get_stats()
            print(f"  📊 Cache statistics:")
            print(f"    • Cache system working: {bool(cache_stats)}")
            
            await engine.shutdown()
            
            return {
                'cache_integration': len(results1) == len(results2),
                'cache_speedup': speedup,
                'first_query_time': first_query_time,
                'second_query_time': second_query_time,
                'cache_working': bool(cache_stats)
            }
    
    except Exception as e:
        print(f"❌ Cache integration test failed: {e}")
        return {}


async def main():
    """Main test function"""
    print("🚀 Time-Series Storage Engine Test Suite - Task 4.1")
    print("=" * 60)
    
    # Run all tests
    basic_results = await test_time_series_storage_basic()
    ingestion_results = await test_high_frequency_ingestion()
    promql_results = await test_promql_engine()
    cache_results = await test_cache_integration()
    
    print("\n" + "=" * 60)
    print("📊 TIME-SERIES STORAGE ENGINE SUMMARY")
    print("=" * 60)
    
    # Evaluate results
    targets_met = 0
    total_targets = 5
    
    # Target 1: Basic storage functionality
    if (basic_results.get('basic_ingestion') and 
        basic_results.get('instant_queries') and 
        basic_results.get('range_queries')):
        print("  ✅ Basic storage functionality working")
        targets_met += 1
    else:
        print("  ❌ Basic storage functionality issues")
    
    # Target 2: High-frequency ingestion (1000+ samples/sec)
    if ingestion_results.get('target_met', False):
        print(f"  ✅ High-frequency ingestion: {ingestion_results.get('ingestion_rate', 0):.1f} samples/sec")
        targets_met += 1
    else:
        print(f"  ⚠️  Ingestion rate: {ingestion_results.get('ingestion_rate', 0):.1f} samples/sec (target: 1000)")
    
    # Target 3: PromQL compatibility
    if (promql_results.get('instant_queries') and 
        promql_results.get('range_queries') and 
        promql_results.get('promql_functions')):
        print("  ✅ PromQL compatibility working")
        targets_met += 1
    else:
        print("  ❌ PromQL compatibility issues")
    
    # Target 4: Cache integration
    if cache_results.get('cache_integration') and cache_results.get('cache_speedup', 0) > 1:
        print(f"  ✅ Cache integration: {cache_results.get('cache_speedup', 0):.1f}x speedup")
        targets_met += 1
    else:
        print("  ❌ Cache integration issues")
    
    # Target 5: Overall system performance
    if (basic_results.get('series_count', 0) > 0 and 
        ingestion_results.get('ingested_count', 0) > 500):
        print("  ✅ Overall system performance good")
        targets_met += 1
    else:
        print("  ❌ Overall system performance issues")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 4:
        print("✅ Task 4.1: Time-Series Storage Engine SUCCESSFUL")
        print("🚀 Ready to proceed with Task 4.2: Basic Metrics Collection Framework")
        
        # Show key achievements
        print(f"\n🏆 Key Achievements:")
        if ingestion_results:
            print(f"  • Ingestion rate: {ingestion_results.get('ingestion_rate', 0):.1f} samples/sec")
            print(f"  • Series handling: {ingestion_results.get('series_count', 0)} series")
        if promql_results:
            print(f"  • PromQL functions: {promql_results.get('supported_functions', 0)} supported")
        if cache_results:
            print(f"  • Cache speedup: {cache_results.get('cache_speedup', 0):.1f}x")
        print(f"  • Time-series database with compression")
        print(f"  • Multi-level caching integration")
        print(f"  • Async operations throughout")
        print(f"  • PromQL compatibility layer")
        
        return 0
    else:
        print("⚠️  Task 4.1: Time-Series Storage Engine needs improvement")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
