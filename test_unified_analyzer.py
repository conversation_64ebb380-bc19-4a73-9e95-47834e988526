#!/usr/bin/env python3
"""
Test Unified Analysis Engine
============================

Test the new unified analysis engine and compare performance with existing analyzers.
"""

import asyncio
import time
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from vibe_check.core.unified_analyzer import UnifiedAnalysisEngine, AnalysisConfig, analyze_project


async def test_unified_analyzer():
    """Test the unified analysis engine"""
    print("🧪 Testing Unified Analysis Engine")
    print("=" * 40)
    
    # Test configuration
    config = AnalysisConfig(
        max_workers=4,
        use_async=True,
        enable_caching=True,
        analysis_timeout=60.0,
        include_tests=False,
        complexity_threshold=15
    )
    
    # Test on vibe_check directory
    test_path = Path("vibe_check")
    if not test_path.exists():
        test_path = Path(".")
    
    print(f"📁 Analyzing: {test_path}")
    
    # Test async analysis
    print("\n⚡ Testing Async Analysis...")
    start_time = time.time()
    
    try:
        result = await analyze_project(test_path, config)
        
        async_time = time.time() - start_time
        
        print(f"✅ Async Analysis Results:")
        print(f"  • Files analyzed: {result.files_analyzed}")
        print(f"  • Analysis time: {result.analysis_time:.2f}s")
        print(f"  • Total time: {async_time:.2f}s")
        print(f"  • Average complexity: {result.project_metrics.average_complexity:.1f}")
        print(f"  • Quality score: {result.project_metrics.quality_score:.1f}/10")
        print(f"  • Total issues: {result.project_metrics.total_issues}")
        print(f"  • Errors: {len(result.errors)}")
        print(f"  • Warnings: {len(result.warnings)}")
        
        # Show top 5 most complex files
        if result.file_metrics:
            complex_files = sorted(result.file_metrics, key=lambda x: x.complexity, reverse=True)
            print(f"\n📊 Top 5 Most Complex Files:")
            for i, fm in enumerate(complex_files[:5]):
                file_name = Path(fm.file_path).name
                print(f"  {i+1}. {file_name}: {fm.complexity} complexity, {fm.quality_score:.1f} quality")
        
        # Show directory breakdown
        if result.directory_metrics:
            print(f"\n📁 Directory Breakdown:")
            for dm in result.directory_metrics[:5]:  # Top 5 directories
                dir_name = Path(dm.directory_path).name or "root"
                print(f"  • {dir_name}: {dm.file_count} files, {dm.average_complexity:.1f} avg complexity")
        
        return result
        
    except Exception as e:
        print(f"❌ Async analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_performance_comparison():
    """Test performance comparison"""
    print("\n🏁 Performance Comparison")
    print("=" * 30)
    
    test_path = Path("vibe_check/core")
    if not test_path.exists():
        test_path = Path("vibe_check")
        if not test_path.exists():
            test_path = Path(".")
    
    # Test with different configurations
    configs = [
        ("Async + Cache", AnalysisConfig(use_async=True, enable_caching=True, max_workers=4)),
        ("Async Only", AnalysisConfig(use_async=True, enable_caching=False, max_workers=4)),
        ("Sync + Cache", AnalysisConfig(use_async=False, enable_caching=True, max_workers=1)),
        ("Sync Only", AnalysisConfig(use_async=False, enable_caching=False, max_workers=1)),
    ]
    
    results = {}
    
    for name, config in configs:
        print(f"\n🔄 Testing: {name}")
        
        try:
            start_time = time.time()
            result = await analyze_project(test_path, config)
            total_time = time.time() - start_time
            
            results[name] = {
                'total_time': total_time,
                'analysis_time': result.analysis_time,
                'files_analyzed': result.files_analyzed,
                'files_per_second': result.files_analyzed / total_time if total_time > 0 else 0
            }
            
            print(f"  ✅ {name}: {total_time:.2f}s total, {result.files_analyzed} files, {results[name]['files_per_second']:.1f} files/sec")
            
        except Exception as e:
            print(f"  ❌ {name} failed: {e}")
            results[name] = {'error': str(e)}
    
    # Show comparison
    print(f"\n📊 Performance Summary:")
    print(f"{'Configuration':<15} {'Time':<8} {'Files':<6} {'Files/sec':<10}")
    print("-" * 45)
    
    for name, data in results.items():
        if 'error' not in data:
            print(f"{name:<15} {data['total_time']:<8.2f} {data['files_analyzed']:<6} {data['files_per_second']:<10.1f}")
        else:
            print(f"{name:<15} {'ERROR':<8} {'N/A':<6} {'N/A':<10}")
    
    return results


async def test_memory_usage():
    """Test memory usage"""
    print("\n💾 Memory Usage Test")
    print("=" * 20)
    
    import psutil
    process = psutil.Process()
    
    # Initial memory
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"Initial memory: {initial_memory:.1f} MB")
    
    # Run analysis
    test_path = Path("vibe_check")
    if not test_path.exists():
        test_path = Path(".")
    
    config = AnalysisConfig(use_async=True, enable_caching=True)
    
    try:
        result = await analyze_project(test_path, config)
        
        # Final memory
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"Final memory: {final_memory:.1f} MB")
        print(f"Memory increase: {memory_increase:.1f} MB")
        print(f"Memory per file: {memory_increase / result.files_analyzed:.2f} MB/file")
        
        return {
            'initial_memory': initial_memory,
            'final_memory': final_memory,
            'memory_increase': memory_increase,
            'files_analyzed': result.files_analyzed
        }
        
    except Exception as e:
        print(f"❌ Memory test failed: {e}")
        return None


async def main():
    """Main test function"""
    print("🚀 Unified Analysis Engine Test Suite")
    print("=" * 50)
    
    try:
        # Test basic functionality
        basic_result = await test_unified_analyzer()
        
        if basic_result:
            # Test performance comparison
            perf_results = await test_performance_comparison()
            
            # Test memory usage
            memory_results = await test_memory_usage()
            
            print("\n" + "=" * 50)
            print("✅ All tests completed successfully!")
            
            # Summary
            if basic_result and perf_results:
                best_config = min(perf_results.items(), 
                                key=lambda x: x[1].get('total_time', float('inf')) if 'error' not in x[1] else float('inf'))
                
                print(f"\n🏆 Best Performance: {best_config[0]}")
                print(f"   Time: {best_config[1]['total_time']:.2f}s")
                print(f"   Speed: {best_config[1]['files_per_second']:.1f} files/sec")
                
                if memory_results:
                    print(f"\n💾 Memory Efficiency: {memory_results['memory_increase']:.1f} MB total")
                    print(f"   Per file: {memory_results['memory_increase'] / memory_results['files_analyzed']:.2f} MB/file")
            
            print("\n🎉 Unified Analysis Engine is ready for production!")
            
        else:
            print("❌ Basic test failed, skipping other tests")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
