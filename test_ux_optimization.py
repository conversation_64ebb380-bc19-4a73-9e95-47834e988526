#!/usr/bin/env python3
"""
UX Optimization Test
====================

Test user experience optimization with responsive design, accessibility, and performance.
"""

import asyncio
import time
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

async def test_responsive_design():
    """Test responsive design functionality"""
    print_header("Responsive Design Test", 2)
    
    try:
        from vibe_check.monitoring.ux import (
            UXOptimizer, DeviceType, ResponsiveBreakpoints
        )
        
        # Create UX optimizer
        ux_optimizer = UXOptimizer()
        print(f"  ✅ UX optimizer created")
        
        # Test device detection
        test_cases = [
            (320, DeviceType.MOBILE),
            (768, DeviceType.MOBILE),
            (800, DeviceType.TABLET),
            (1024, DeviceType.TABLET),
            (1200, DeviceType.DESKTOP),
            (1440, DeviceType.DESKTOP),
            (1600, DeviceType.LARGE_SCREEN)
        ]
        
        detection_results = []
        for viewport_width, expected_device in test_cases:
            detected_device = ux_optimizer.detect_device_type(viewport_width)
            detection_results.append(detected_device == expected_device)
            ux_optimizer.set_device_type(detected_device)
        
        # Generate responsive CSS
        responsive_css = ux_optimizer.get_responsive_css()
        
        # Test CSS generation
        css_valid = (
            "@media" in responsive_css and
            "grid-template-columns" in responsive_css and
            "mobile-max" in responsive_css and
            len(responsive_css) > 1000
        )
        
        success = all(detection_results) and css_valid
        
        details = f"""Device detection tests: {sum(detection_results)}/{len(detection_results)}
CSS generation: {'✓' if css_valid else '✗'}
CSS length: {len(responsive_css)} characters
Breakpoints configured: 4
Current device: {ux_optimizer.current_device.value}"""
        
        print_result("Responsive Design", success, details)
        return success
        
    except Exception as e:
        print_result("Responsive Design", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_accessibility_features():
    """Test accessibility features"""
    print_header("Accessibility Features Test", 2)
    
    try:
        from vibe_check.monitoring.ux import (
            UXOptimizer, AccessibilityConfig, AccessibilityLevel
        )
        
        # Create UX optimizer
        ux_optimizer = UXOptimizer()
        
        # Test different accessibility configurations
        configs = [
            AccessibilityConfig(
                level=AccessibilityLevel.AA,
                enable_screen_reader=True,
                enable_keyboard_navigation=True,
                font_size_scaling=1.2
            ),
            AccessibilityConfig(
                level=AccessibilityLevel.AAA,
                enable_high_contrast=True,
                enable_focus_indicators=True,
                animation_reduced=True
            )
        ]
        
        config_results = []
        for config in configs:
            ux_optimizer.update_accessibility_config(config)
            
            # Get accessibility attributes
            attributes = ux_optimizer.get_accessibility_attributes()
            
            # Generate CSS with accessibility features
            css = ux_optimizer.get_responsive_css()
            
            # Validate accessibility features
            has_aria = any('aria-' in attr for attr in attributes.keys())
            has_focus_styles = 'focus' in css
            has_contrast_styles = 'prefers-contrast' in css
            has_motion_styles = 'prefers-reduced-motion' in css
            
            config_results.append(has_aria and has_focus_styles)
        
        # Test font scaling
        ux_optimizer.accessibility_config.font_size_scaling = 1.5
        css_with_scaling = ux_optimizer.get_responsive_css()
        scaling_applied = "1.5" in css_with_scaling
        
        success = all(config_results) and scaling_applied
        
        details = f"""Accessibility configs tested: {len(configs)}
Config validation: {sum(config_results)}/{len(config_results)}
ARIA attributes: Generated
Focus indicators: Included
High contrast: Supported
Reduced motion: Supported
Font scaling: {'✓' if scaling_applied else '✗'}"""
        
        print_result("Accessibility Features", success, details)
        return success
        
    except Exception as e:
        print_result("Accessibility Features", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_theme_management():
    """Test theme management functionality"""
    print_header("Theme Management Test", 2)
    
    try:
        from vibe_check.monitoring.ux import UXOptimizer, ThemeMode
        
        # Create UX optimizer
        ux_optimizer = UXOptimizer()
        
        # Test theme changes
        themes = [ThemeMode.LIGHT, ThemeMode.DARK, ThemeMode.HIGH_CONTRAST]
        theme_results = []
        
        for theme in themes:
            ux_optimizer.set_theme(theme)
            theme_css = ux_optimizer.get_theme_css()
            
            # Validate theme-specific CSS
            if theme == ThemeMode.DARK:
                valid = "#1a1a1a" in theme_css and "Dark theme" in theme_css
            elif theme == ThemeMode.HIGH_CONTRAST:
                valid = "#000000" in theme_css and "High contrast" in theme_css
            else:  # LIGHT
                valid = "#ffffff" in theme_css and "Light theme" in theme_css
            
            theme_results.append(valid)
        
        # Test theme change callback
        callback_triggered = False
        def on_theme_change(new_theme):
            nonlocal callback_triggered
            callback_triggered = True
        
        ux_optimizer.on_theme_change = on_theme_change
        ux_optimizer.set_theme(ThemeMode.DARK)
        
        success = all(theme_results) and callback_triggered
        
        details = f"""Themes tested: {len(themes)}
Theme validation: {sum(theme_results)}/{len(theme_results)}
CSS generation: Working
Callback system: {'✓' if callback_triggered else '✗'}
Current theme: {ux_optimizer.current_theme.value}"""
        
        print_result("Theme Management", success, details)
        return success
        
    except Exception as e:
        print_result("Theme Management", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_performance_optimization():
    """Test performance optimization features"""
    print_header("Performance Optimization Test", 2)
    
    try:
        from vibe_check.monitoring.ux import UXOptimizer, PerformanceConfig, DeviceType
        
        # Create UX optimizer
        ux_optimizer = UXOptimizer()
        
        # Test data optimization
        large_dataset = list(range(5000))  # 5000 data points
        optimized_data = ux_optimizer.optimize_chart_data(large_dataset)
        
        # Test lazy loading decisions
        ux_optimizer.set_device_type(DeviceType.MOBILE)
        should_lazy_load_mobile = ux_optimizer.should_lazy_load("test_component")
        
        ux_optimizer.set_device_type(DeviceType.DESKTOP)
        should_lazy_load_desktop = ux_optimizer.should_lazy_load("test_component")
        
        # Test performance hints
        hints = ux_optimizer.get_performance_hints()
        
        # Test metrics recording
        ux_optimizer.record_metric('page_load_time', 2.5)
        ux_optimizer.record_metric('first_contentful_paint', 1.8)
        ux_optimizer.record_metric('cumulative_layout_shift', 0.05)
        
        # Calculate UX score
        ux_score = ux_optimizer.get_ux_score()
        
        success = (
            len(optimized_data) <= 1000 and  # Data was optimized
            should_lazy_load_mobile and  # Mobile should lazy load
            not should_lazy_load_desktop and  # Desktop shouldn't lazy load
            len(hints) > 5 and  # Performance hints generated
            ux_score > 0  # UX score calculated
        )
        
        details = f"""Original data points: {len(large_dataset)}
Optimized data points: {len(optimized_data)}
Mobile lazy loading: {'✓' if should_lazy_load_mobile else '✗'}
Desktop lazy loading: {'✗' if not should_lazy_load_desktop else '✓'}
Performance hints: {len(hints)}
UX score: {ux_score:.1f}/100
Metrics recorded: 3"""
        
        print_result("Performance Optimization", success, details)
        return success
        
    except Exception as e:
        print_result("Performance Optimization", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_ux_reporting():
    """Test UX reporting and recommendations"""
    print_header("UX Reporting Test", 2)
    
    try:
        from vibe_check.monitoring.ux import UXOptimizer, AccessibilityConfig, DeviceType
        
        # Create UX optimizer
        ux_optimizer = UXOptimizer()
        
        # Set up test scenario
        ux_optimizer.set_device_type(DeviceType.MOBILE)
        ux_optimizer.record_metric('page_load_time', 4.5)  # Poor performance
        ux_optimizer.record_metric('cumulative_layout_shift', 0.3)  # Poor CLS
        
        # Disable some accessibility features
        config = AccessibilityConfig(
            enable_screen_reader=False,
            font_size_scaling=0.9  # Too small for mobile
        )
        ux_optimizer.update_accessibility_config(config)
        
        # Get recommendations
        recommendations = ux_optimizer.get_optimization_recommendations()
        
        # Generate UX report
        ux_report = ux_optimizer.export_ux_report()
        
        # Validate report structure
        report_valid = (
            'timestamp' in ux_report and
            'ux_score' in ux_report and
            'metrics' in ux_report and
            'configuration' in ux_report and
            'recommendations' in ux_report and
            'responsive_breakpoints' in ux_report
        )
        
        # Check if recommendations are relevant
        relevant_recommendations = any(
            'compression' in rec.lower() or 'font size' in rec.lower()
            for rec in recommendations
        )
        
        success = (
            len(recommendations) > 0 and
            report_valid and
            relevant_recommendations and
            ux_report['ux_score'] < 80  # Should be low due to poor metrics
        )
        
        details = f"""Recommendations generated: {len(recommendations)}
Report structure: {'✓' if report_valid else '✗'}
UX score: {ux_report['ux_score']:.1f}/100
Relevant recommendations: {'✓' if relevant_recommendations else '✗'}
Device type: {ux_report['configuration']['device_type']}
Accessibility level: {ux_report['configuration']['accessibility_level']}"""
        
        print_result("UX Reporting", success, details)
        return success
        
    except Exception as e:
        print_result("UX Reporting", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run UX optimization tests"""
    print_header("UX Optimization Test", 1)
    print("Testing user experience optimization with responsive design and accessibility")
    print("Validating responsive breakpoints, accessibility features, and performance optimization")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['responsive'] = await test_responsive_design()
    test_results['accessibility'] = await test_accessibility_features()
    test_results['themes'] = await test_theme_management()
    test_results['performance'] = await test_performance_optimization()
    test_results['reporting'] = await test_ux_reporting()
    
    # Summary
    print_header("UX Optimization Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ UX optimization SUCCESSFUL")
        print(f"  🚀 Ready for production deployment")
    else:
        print(f"  ❌ UX optimization FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
