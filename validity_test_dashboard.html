
        <!DOCTYPE html>
        <html>
        <head>
            <title>Validity Test Dashboard</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .dashboard-header { text-align: center; margin-bottom: 20px; }
                .panel { 
                    display: inline-block; 
                    margin: 10px; 
                    padding: 15px; 
                    border: 1px solid #ddd; 
                    border-radius: 8px;
                    background: white;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .panel-title { font-weight: bold; margin-bottom: 10px; }
                .metric-value { font-size: 2em; text-align: center; color: #007bff; }
            </style>
        </head>
        <body>
            <div class="dashboard-header">
                <h1>Validity Test Dashboard</h1>
                <p></p>
            </div>
        
            <div class="panel" style="width: 200px; height: 150px;">
                <div class="panel-title">Test Metric</div>
                
        <div class="metric-value">42</div>
        <div style="text-align: center; margin-top: 10px;">test_metric</div>
        
            </div>
            
            <div class="panel" style="width: 400px; height: 300px;">
                <div class="panel-title">Test Chart</div>
                
        <div style="width: 370px; height: 240px;">
            <canvas id="chart1"></canvas>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            const ctx = document.getElementById('chart1').getContext('2d');
            const chart = new Chart(ctx, {"type": "line", "data": {"labels": [1, 2, 3, 4], "datasets": [{"label": "", "data": [10, 20, 15, 25], "backgroundColor": "rgba(54, 162, 235, 0.2)", "borderColor": "rgba(54, 162, 235, 1)", "borderWidth": 1}]}, "options": {"responsive": true, "plugins": {"title": {"display": false, "text": ""}}}});
        </script>
        
            </div>
            
            <div class="panel" style="width: 400px; height: 300px;">
                <div class="panel-title">Test Bar Chart</div>
                
        <div style="width: 370px; height: 240px;">
            <canvas id="chart2"></canvas>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            const ctx = document.getElementById('chart2').getContext('2d');
            const chart = new Chart(ctx, {"type": "bar", "data": {"labels": ["A", "B", "C"], "datasets": [{"label": "", "data": [30, 40, 35], "backgroundColor": "rgba(54, 162, 235, 0.2)", "borderColor": "rgba(54, 162, 235, 1)", "borderWidth": 1}]}, "options": {"responsive": true, "plugins": {"title": {"display": false, "text": ""}}}});
        </script>
        
            </div>
            
            <div class="panel" style="width: 400px; height: 300px;">
                <div class="panel-title">Test Pie Chart</div>
                
        <div style="width: 370px; height: 240px;">
            <canvas id="chart3"></canvas>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            const ctx = document.getElementById('chart3').getContext('2d');
            const chart = new Chart(ctx, {"type": "pie", "data": {"labels": ["X", "Y", "Z"], "datasets": [{"label": "", "data": [25, 35, 40], "backgroundColor": "rgba(54, 162, 235, 0.2)", "borderColor": "rgba(54, 162, 235, 1)", "borderWidth": 1}]}, "options": {"responsive": true, "plugins": {"title": {"display": false, "text": ""}}}});
        </script>
        
            </div>
            
        </body>
        </html>
        