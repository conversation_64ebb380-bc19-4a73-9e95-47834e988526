"""
Core Data Models
===============

Core data models for Vibe Check analysis results.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
import time


@dataclass
class FileMetrics:
    """Metrics for a single file"""
    file_path: str
    lines: int
    code_lines: int
    complexity: int
    functions: int
    classes: int
    quality_score: float
    issues: int
    analysis_time: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'file_path': self.file_path,
            'lines': self.lines,
            'code_lines': self.code_lines,
            'complexity': self.complexity,
            'functions': self.functions,
            'classes': self.classes,
            'quality_score': self.quality_score,
            'issues': self.issues,
            'analysis_time': self.analysis_time
        }


@dataclass
class DirectoryMetrics:
    """Metrics for a directory"""
    directory_path: str
    file_count: int
    total_lines: int
    average_complexity: float
    average_quality: float
    total_issues: int
    files: List[FileMetrics] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'directory_path': self.directory_path,
            'file_count': self.file_count,
            'total_lines': self.total_lines,
            'average_complexity': self.average_complexity,
            'average_quality': self.average_quality,
            'total_issues': self.total_issues,
            'files': [f.to_dict() for f in self.files]
        }


@dataclass
class ProjectMetrics:
    """Metrics for entire project"""
    total_files: int
    total_lines: int
    average_complexity: float
    max_complexity: int
    average_quality: float
    total_issues: int
    quality_score: float
    directories: List[DirectoryMetrics] = field(default_factory=list)
    files: List[FileMetrics] = field(default_factory=list)
    analysis_time: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'total_files': self.total_files,
            'total_lines': self.total_lines,
            'average_complexity': self.average_complexity,
            'max_complexity': self.max_complexity,
            'average_quality': self.average_quality,
            'total_issues': self.total_issues,
            'quality_score': self.quality_score,
            'directories': [d.to_dict() for d in self.directories],
            'files': [f.to_dict() for f in self.files],
            'analysis_time': self.analysis_time
        }


@dataclass
class AnalysisConfig:
    """Configuration for analysis"""
    max_workers: int = 4
    use_async: bool = True
    enable_caching: bool = True
    analysis_timeout: float = 300.0
    include_tests: bool = False
    include_docs: bool = True
    complexity_threshold: int = 10
    quality_threshold: float = 7.0


@dataclass
class AnalysisResult:
    """Complete analysis result"""
    project_metrics: ProjectMetrics
    file_metrics: List[FileMetrics]
    directory_metrics: List[DirectoryMetrics]
    analysis_time: float
    files_analyzed: int
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'project_metrics': self.project_metrics.to_dict(),
            'file_metrics': [fm.to_dict() for fm in self.file_metrics],
            'directory_metrics': [dm.to_dict() for dm in self.directory_metrics],
            'analysis_time': self.analysis_time,
            'files_analyzed': self.files_analyzed,
            'errors': self.errors,
            'warnings': self.warnings
        }
