"""
Project Metrics Model
===================

This module defines the ProjectMetrics class, which stores analysis results
for the entire project.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any

import networkx as nx

from .directory_metrics import DirectoryMetrics
from .file_metrics import FileMetrics


@dataclass
class ProjectMetrics:
    """
    Complete project analysis results.

    This is a core domain entity representing comprehensive analysis
    results for the entire project, including file metrics, directory
    metrics, and project-wide metrics.

    Attributes:
        project_path: Path to the project being analyzed
        files: Dictionary mapping file paths to FileMetrics objects
        directories: Dictionary mapping directory paths to DirectoryMetrics objects
        dependency_graph: NetworkX directed graph of dependencies
        complexity_scores: Dictionary mapping file paths to complexity scores
        quality_metrics: Dictionary mapping file paths to quality scores
        type_coverage: Dictionary mapping file paths to type coverage percentages
        doc_coverage: Dictionary mapping file paths to documentation coverage percentages
        documentation_files: Dictionary mapping documentation file paths to FileMetrics objects
        documentation_quality_scores: Dictionary mapping documentation file paths to quality scores
        documentation_size_distribution: Dictionary mapping file size ranges to counts
        tool_results: Dictionary of tool outputs for the entire project
        file_metrics: Dictionary mapping file paths to FileMetrics objects (alias for files)
        directory_metrics: Dictionary mapping directory paths to DirectoryMetrics objects (alias for directories)
        total_file_count: Total number of files in the project
        total_line_count: Total number of lines across all files
        avg_complexity: Average complexity score across all files
        max_complexity: Maximum complexity score across all files
        issue_count: Total number of issues across all files
        issues_by_severity: Dictionary mapping severity to count of issues
    """
    project_path: str = ""
    files: Dict[str, FileMetrics] = field(default_factory=dict)
    directories: Dict[str, DirectoryMetrics] = field(default_factory=dict)
    dependency_graph: nx.DiGraph = field(default_factory=nx.DiGraph)
    complexity_scores: Dict[str, int] = field(default_factory=dict)
    quality_metrics: Dict[str, float] = field(default_factory=dict)
    type_coverage: Dict[str, float] = field(default_factory=dict)
    doc_coverage: Dict[str, float] = field(default_factory=dict)

    # Documentation-specific metrics
    documentation_files: Dict[str, FileMetrics] = field(default_factory=dict)
    documentation_quality_scores: Dict[str, float] = field(default_factory=dict)
    documentation_size_distribution: Dict[str, int] = field(default_factory=dict)

    # Tool results for the entire project
    tool_results: Dict[str, Any] = field(default_factory=dict)

    # Trend analysis results
    trend_results: Dict[str, Any] = field(default_factory=dict)
    trend_visualizations: Dict[str, str] = field(default_factory=dict)

    # Progress analysis results
    progress_results: Dict[str, Any] = field(default_factory=dict)
    progress_visualizations: Dict[str, str] = field(default_factory=dict)

    # Import analysis results
    import_analysis: Optional[Any] = None  # Will hold ImportAnalysisResult
    import_visualizations: Dict[str, str] = field(default_factory=dict)

    # Metrics for test compatibility
    _max_complexity: int = 0
    _issue_count: int = 0
    _issues_by_severity: Dict[str, int] = field(default_factory=dict)
    _issues_by_tool: Dict[str, int] = field(default_factory=dict)
    _file_count_by_type: Dict[str, int] = field(default_factory=dict)

    @property
    def file_metrics(self) -> Dict[str, FileMetrics]:
        """Get the file metrics (alias for files)."""
        return self.files

    @property
    def directory_metrics(self) -> Dict[str, DirectoryMetrics]:
        """Get the directory metrics (alias for directories)."""
        return self.directories

    @property
    def total_line_count(self) -> int:
        """Get the total number of lines across all files."""
        return sum(fm.lines for fm in self.files.values())

    @property
    def max_complexity(self) -> int:
        """Get the maximum complexity score across all files."""
        if not self.complexity_scores:
            return 0
        return max(self.complexity_scores.values())

    @property
    def issue_count(self) -> int:
        """Get the total number of issues across all files."""
        total = sum(len(fm.issues) for fm in self.files.values())
        import logging
        logger = logging.getLogger("vibe_check_project_metrics")
        logger.info(f"Calculated total issue count: {total} across {len(self.files)} files")
        return total

    @property
    def issues_by_severity(self) -> Dict[str, int]:
        """Get the count of issues by severity."""
        result = {}
        for fm in self.files.values():
            for issue in fm.issues:
                severity = issue.get("severity", "unknown")
                result[severity] = result.get(severity, 0) + 1

        import logging
        logger = logging.getLogger("vibe_check_project_metrics")
        logger.info(f"Calculated issues by severity: {result}")
        return result

    def add_file_metrics(self, file_metrics: FileMetrics) -> None:
        """
        Add file metrics to the project metrics.

        Args:
            file_metrics: FileMetrics object to add
        """
        import logging
        logger = logging.getLogger("vibe_check_project_metrics")

        # Log the file metrics being added
        issue_count = len(getattr(file_metrics, "issues", []))
        security_issues = getattr(file_metrics, "security_issues", 0)
        high_severity_issues = getattr(file_metrics, "high_severity_issues", 0)

        logger.info(f"Adding file metrics for {file_metrics.path} with {issue_count} issues "
                   f"({security_issues} security issues, {high_severity_issues} high severity)")

        self.files[file_metrics.path] = file_metrics

        # Update related collections
        if file_metrics.is_documentation:
            self.documentation_files[file_metrics.path] = file_metrics
            self.documentation_quality_scores[file_metrics.path] = file_metrics.documentation_quality

            # Update documentation size distribution
            size_category = self._categorize_file_size(file_metrics.size)
            self.documentation_size_distribution[size_category] = (
                self.documentation_size_distribution.get(size_category, 0) + 1
            )

        # Update complexity and quality metrics
        self.complexity_scores[file_metrics.path] = file_metrics.complexity
        self.quality_metrics[file_metrics.path] = file_metrics.maintainability_index
        self.type_coverage[file_metrics.path] = file_metrics.type_coverage
        self.doc_coverage[file_metrics.path] = file_metrics.docstring_coverage

        # Update dependency graph
        self._update_dependency_graph(file_metrics)

        # Update issues by severity and tool
        for issue in file_metrics.issues:
            severity = issue.get("severity", "unknown")
            tool = issue.get("tool", "unknown")

            self._issues_by_severity[severity] = self._issues_by_severity.get(severity, 0) + 1
            self._issues_by_tool[tool] = self._issues_by_tool.get(tool, 0) + 1

            # Log each issue being added
            logger.debug(f"Added issue to project metrics: {tool} - {severity} - {issue.get('code', '')}")

        # Update file count by type
        file_type = "python" if file_metrics.is_python else "markdown" if file_metrics.is_markdown else "other"
        self._file_count_by_type[file_type] = self._file_count_by_type.get(file_type, 0) + 1

        # Log the updated issue counts
        logger.info(f"Updated issues by severity: {self._issues_by_severity}")
        logger.info(f"Updated issues by tool: {self._issues_by_tool}")

    def add_directory_metrics(self, directory_metrics: DirectoryMetrics) -> None:
        """
        Add directory metrics to the project metrics.

        Args:
            directory_metrics: DirectoryMetrics object to add
        """
        self.directories[directory_metrics.path] = directory_metrics

    def add_tool_results(self, tool_name: str, results: Dict[str, Any]) -> None:
        """
        Add tool results to the project metrics.

        Args:
            tool_name: Name of the tool
            results: Tool results
        """
        self.tool_results[tool_name] = results

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the project metrics to a dictionary.

        Returns:
            Dictionary representation of the project metrics
        """
        result = {
            "project_path": self.project_path,
            "file_metrics": {path: fm.to_dict() for path, fm in self.files.items()},
            "directory_metrics": {path: dm.to_dict() for path, dm in self.directories.items()},
            "tool_results": self.tool_results,
            "total_file_count": self.total_file_count,
            "total_line_count": self.total_line_count,
            "avg_complexity": self.avg_complexity,
            "max_complexity": self.max_complexity,
            "issue_count": self.issue_count,
            "issues_by_severity": self.issues_by_severity,
            "issues_by_tool": self._issues_by_tool,
            "file_count_by_type": self._file_count_by_type,
            "avg_type_coverage": self.avg_type_coverage,
            "avg_docstring_coverage": self.avg_doc_coverage
        }

        # Add trend results if available
        if self.trend_results:
            result["trend_results"] = self.trend_results

        if self.trend_visualizations:
            result["trend_visualizations"] = self.trend_visualizations

        # Add progress results if available
        if self.progress_results:
            result["progress_results"] = self.progress_results

        if self.progress_visualizations:
            result["progress_visualizations"] = self.progress_visualizations

        return result

    def _update_dependency_graph(self, file_metrics: FileMetrics) -> None:
        """
        Update the dependency graph with a file's dependencies.

        Args:
            file_metrics: FileMetrics object with dependency information
        """
        # Add the file as a node if it's not already in the graph
        if not self.dependency_graph.has_node(file_metrics.path):
            self.dependency_graph.add_node(file_metrics.path,
                                           name=file_metrics.name,
                                           size=file_metrics.size,
                                           complexity=file_metrics.complexity)

        # Add edges for internal dependencies
        for dep in file_metrics.internal_deps:
            self.dependency_graph.add_edge(file_metrics.path, dep)

    def _categorize_file_size(self, size_bytes: int) -> str:
        """
        Categorize a file size into a size category.

        Args:
            size_bytes: File size in bytes

        Returns:
            Size category string
        """
        if size_bytes < 1024:  # < 1KB
            return "< 1KB"
        elif size_bytes < 10 * 1024:  # < 10KB
            return "1KB-10KB"
        elif size_bytes < 100 * 1024:  # < 100KB
            return "10KB-100KB"
        elif size_bytes < 1024 * 1024:  # < 1MB
            return "100KB-1MB"
        else:  # >= 1MB
            return "> 1MB"

    @property
    def python_file_count(self) -> int:
        """Get the number of Python files in the project."""
        return sum(1 for fm in self.files.values() if fm.is_python)

    @property
    def markdown_file_count(self) -> int:
        """Get the number of Markdown files in the project."""
        return sum(1 for fm in self.files.values() if fm.is_markdown)

    @property
    def total_file_count(self) -> int:
        """Get the total number of files in the project."""
        return len(self.files)

    @property
    def total_directory_count(self) -> int:
        """Get the total number of directories in the project."""
        return len(self.directories)

    @property
    def avg_complexity(self) -> float:
        """Get the average complexity score across all files."""
        if not self.complexity_scores:
            return 0.0
        return sum(self.complexity_scores.values()) / len(self.complexity_scores)

    @property
    def highest_complexity_files(self) -> List[str]:
        """Get the paths of the five most complex files."""
        return sorted(self.complexity_scores.keys(),
                     key=lambda f: self.complexity_scores[f],
                     reverse=True)[:5]

    @property
    def avg_doc_coverage(self) -> float:
        """Get the average documentation coverage percentage."""
        if not self.doc_coverage:
            return 0.0
        return sum(self.doc_coverage.values()) / len(self.doc_coverage)

    @property
    def avg_type_coverage(self) -> float:
        """Get the average type coverage percentage."""
        if not self.type_coverage:
            return 0.0
        return sum(self.type_coverage.values()) / len(self.type_coverage)

    def get_circular_dependencies(self) -> List[List[str]]:
        """
        Find circular dependencies in the project.

        Returns:
            List of cycles, where each cycle is a list of file paths
        """
        try:
            # Find all simple cycles in the dependency graph
            cycles = list(nx.simple_cycles(self.dependency_graph))
            # Sort cycles by length (shorter cycles first)
            return sorted(cycles, key=len)
        except nx.NetworkXNoCycle:
            # No cycles found
            return []

    def get_most_imported_files(self, limit: int = 10) -> List[str]:
        """
        Get the most frequently imported files in the project.

        Args:
            limit: Maximum number of files to return

        Returns:
            List of file paths, sorted by import count (descending)
        """
        # Count incoming edges (imports) for each node
        in_degree = self.dependency_graph.in_degree()
        # Sort by count (descending)
        most_imported = sorted(in_degree, key=lambda x: x[1], reverse=True)
        # Return the file paths
        return [path for path, _ in most_imported[:limit]]

    def get_most_importing_files(self, limit: int = 10) -> List[str]:
        """
        Get the files that import the most other files.

        Args:
            limit: Maximum number of files to return

        Returns:
            List of file paths, sorted by outgoing import count (descending)
        """
        # Count outgoing edges (imports) for each node
        out_degree = self.dependency_graph.out_degree()
        # Sort by count (descending)
        most_importing = sorted(out_degree, key=lambda x: x[1], reverse=True)
        # Return the file paths
        return [path for path, _ in most_importing[:limit]]

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectMetrics':
        """
        Create a ProjectMetrics instance from a dictionary.

        Args:
            data: Dictionary containing project metrics data

        Returns:
            A new ProjectMetrics instance
        """
        from .file_metrics import FileMetrics
        from .directory_metrics import DirectoryMetrics

        # Create a new instance with the project path
        metrics = cls(project_path=data.get("project_path", ""))

        # Add file metrics
        if "file_metrics" in data:
            for path, fm_data in data["file_metrics"].items():
                if isinstance(fm_data, dict):
                    metrics.files[path] = FileMetrics.from_dict(fm_data)
                elif isinstance(fm_data, FileMetrics):
                    metrics.files[path] = fm_data

        # Add directory metrics
        if "directory_metrics" in data:
            for path, dm_data in data["directory_metrics"].items():
                if isinstance(dm_data, dict):
                    metrics.directories[path] = DirectoryMetrics.from_dict(dm_data)
                elif isinstance(dm_data, DirectoryMetrics):
                    metrics.directories[path] = dm_data

        # Add tool results
        if "tool_results" in data:
            metrics.tool_results = data["tool_results"]

        # Add trend results
        if "trend_results" in data:
            metrics.trend_results = data["trend_results"]

        # Add trend visualizations
        if "trend_visualizations" in data:
            metrics.trend_visualizations = data["trend_visualizations"]

        # Add progress results
        if "progress_results" in data:
            metrics.progress_results = data["progress_results"]

        # Add progress visualizations
        if "progress_visualizations" in data:
            metrics.progress_visualizations = data["progress_visualizations"]

        # Add issues by severity
        if "issues_by_severity" in data:
            metrics._issues_by_severity = data["issues_by_severity"]

        # Add issues by tool
        if "issues_by_tool" in data:
            metrics._issues_by_tool = data["issues_by_tool"]

        # Add file count by type
        if "file_count_by_type" in data:
            metrics._file_count_by_type = data["file_count_by_type"]

        return metrics
