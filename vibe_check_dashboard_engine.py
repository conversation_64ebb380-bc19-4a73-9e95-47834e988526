"""
Vibe Check Dashboard Engine - Grafana Replacement
Interactive dashboard system with real-time updates and custom visualizations
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import aiohttp
from aiohttp import web, WSMsgType
import aiofiles
import weakref
from pathlib import Path


class ChartType(Enum):
    """Supported chart types"""
    LINE = "line"
    BAR = "bar"
    AREA = "area"
    SCATTER = "scatter"
    HEATMAP = "heatmap"
    GAUGE = "gauge"
    STAT = "stat"
    TABLE = "table"
    GRAPH = "graph"


@dataclass
class PanelConfig:
    """Configuration for a dashboard panel"""
    id: str
    title: str
    chart_type: ChartType
    metrics: List[str]
    time_range: str = "1h"
    refresh_interval: int = 30
    width: int = 6
    height: int = 4
    x: int = 0
    y: int = 0
    options: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DashboardConfig:
    """Configuration for a complete dashboard"""
    id: str
    title: str
    description: str = ""
    panels: List[PanelConfig] = field(default_factory=list)
    refresh_interval: int = 30
    time_range: str = "1h"
    variables: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)


class DataSource:
    """Base class for data sources"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
    
    async def query(self, query: str, start_time: float, end_time: float) -> Dict[str, Any]:
        """Execute a query and return results"""
        raise NotImplementedError


class VibeCheckDataSource(DataSource):
    """Data source for Vibe Check metrics"""
    
    def __init__(self, monitoring_engine):
        super().__init__("vibe_check", {})
        self.engine = monitoring_engine
    
    async def query(self, query: str, start_time: float, end_time: float) -> Dict[str, Any]:
        """Query Vibe Check metrics"""
        # Parse simple metric queries (extend for PromQL compatibility)
        metric_name = query.strip()
        
        points = await self.engine.query(metric_name, start_time, end_time)
        
        return {
            "metric": metric_name,
            "data": [
                {"timestamp": p.timestamp, "value": p.value, "labels": p.labels}
                for p in points
            ]
        }


class ChartRenderer:
    """Renders charts in various formats"""
    
    @staticmethod
    def render_line_chart(data: Dict[str, Any], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Render a line chart configuration"""
        options = options or {}
        
        return {
            "type": "line",
            "data": {
                "datasets": [{
                    "label": data["metric"],
                    "data": [
                        {"x": point["timestamp"] * 1000, "y": point["value"]}
                        for point in data["data"]
                    ],
                    "borderColor": options.get("color", "#3498db"),
                    "backgroundColor": options.get("backgroundColor", "transparent"),
                    "tension": options.get("tension", 0.1)
                }]
            },
            "options": {
                "responsive": True,
                "maintainAspectRatio": False,
                "scales": {
                    "x": {
                        "type": "time",
                        "time": {
                            "displayFormats": {
                                "minute": "HH:mm",
                                "hour": "HH:mm"
                            }
                        }
                    },
                    "y": {
                        "beginAtZero": options.get("beginAtZero", False)
                    }
                },
                "plugins": {
                    "legend": {
                        "display": options.get("showLegend", True)
                    },
                    "tooltip": {
                        "mode": "index",
                        "intersect": False
                    }
                }
            }
        }
    
    @staticmethod
    def render_gauge(data: Dict[str, Any], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Render a gauge chart"""
        options = options or {}
        
        # Get latest value
        latest_value = data["data"][-1]["value"] if data["data"] else 0
        
        return {
            "type": "gauge",
            "value": latest_value,
            "min": options.get("min", 0),
            "max": options.get("max", 100),
            "thresholds": options.get("thresholds", [
                {"value": 0, "color": "#22c55e"},
                {"value": 70, "color": "#f59e0b"},
                {"value": 90, "color": "#ef4444"}
            ]),
            "unit": options.get("unit", ""),
            "title": options.get("title", data["metric"])
        }
    
    @staticmethod
    def render_stat(data: Dict[str, Any], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Render a stat panel"""
        options = options or {}
        
        # Calculate statistics
        values = [point["value"] for point in data["data"]]
        if not values:
            return {"type": "stat", "value": 0, "change": 0}
        
        current_value = values[-1]
        previous_value = values[-2] if len(values) > 1 else current_value
        change = ((current_value - previous_value) / previous_value * 100) if previous_value != 0 else 0
        
        return {
            "type": "stat",
            "value": current_value,
            "change": change,
            "unit": options.get("unit", ""),
            "decimals": options.get("decimals", 2),
            "colorMode": options.get("colorMode", "value"),
            "thresholds": options.get("thresholds", [])
        }


class DashboardEngine:
    """Main dashboard engine - Grafana replacement"""
    
    def __init__(self, monitoring_engine, port: int = 8080):
        self.monitoring_engine = monitoring_engine
        self.port = port
        self.app = web.Application()
        self.websockets: List[web.WebSocketResponse] = []
        self.dashboards: Dict[str, DashboardConfig] = {}
        self.data_sources: Dict[str, DataSource] = {}
        self.chart_renderer = ChartRenderer()
        
        # Add default data source
        self.data_sources["vibe_check"] = VibeCheckDataSource(monitoring_engine)
        
        # Setup routes
        self._setup_routes()
        
        # Load default dashboards
        self._load_default_dashboards()
    
    def _setup_routes(self):
        """Setup HTTP routes"""
        self.app.router.add_get('/', self._serve_dashboard)
        self.app.router.add_get('/api/dashboards', self._list_dashboards)
        self.app.router.add_get('/api/dashboards/{dashboard_id}', self._get_dashboard)
        self.app.router.add_post('/api/dashboards', self._create_dashboard)
        self.app.router.add_get('/api/panels/{panel_id}/data', self._get_panel_data)
        self.app.router.add_get('/ws', self._websocket_handler)
        self.app.router.add_static('/', path='static', name='static')
    
    def _load_default_dashboards(self):
        """Load default dashboards"""
        # System Overview Dashboard
        system_dashboard = DashboardConfig(
            id="system_overview",
            title="System Overview",
            description="System performance and resource utilization",
            panels=[
                PanelConfig(
                    id="cpu_usage",
                    title="CPU Usage",
                    chart_type=ChartType.LINE,
                    metrics=["system_cpu_percent"],
                    width=6, height=4, x=0, y=0
                ),
                PanelConfig(
                    id="memory_usage",
                    title="Memory Usage",
                    chart_type=ChartType.GAUGE,
                    metrics=["system_memory_percent"],
                    width=6, height=4, x=6, y=0,
                    options={"max": 100, "unit": "%"}
                ),
                PanelConfig(
                    id="disk_usage",
                    title="Disk Usage",
                    chart_type=ChartType.STAT,
                    metrics=["system_disk_percent"],
                    width=6, height=2, x=0, y=4,
                    options={"unit": "%"}
                ),
                PanelConfig(
                    id="process_count",
                    title="Process Count",
                    chart_type=ChartType.STAT,
                    metrics=["system_process_count"],
                    width=6, height=2, x=6, y=4
                )
            ]
        )
        
        # Code Quality Dashboard
        code_dashboard = DashboardConfig(
            id="code_quality",
            title="Code Quality",
            description="Code quality metrics and trends",
            panels=[
                PanelConfig(
                    id="quality_score",
                    title="Quality Score",
                    chart_type=ChartType.GAUGE,
                    metrics=["code_quality_quality_score"],
                    width=6, height=4, x=0, y=0,
                    options={"max": 10, "unit": "/10"}
                ),
                PanelConfig(
                    id="complexity",
                    title="Average Complexity",
                    chart_type=ChartType.LINE,
                    metrics=["code_quality_complexity_avg"],
                    width=6, height=4, x=6, y=0
                ),
                PanelConfig(
                    id="issues_total",
                    title="Total Issues",
                    chart_type=ChartType.STAT,
                    metrics=["code_quality_issues_total"],
                    width=6, height=2, x=0, y=4
                ),
                PanelConfig(
                    id="files_total",
                    title="Total Files",
                    chart_type=ChartType.STAT,
                    metrics=["code_quality_files_total"],
                    width=6, height=2, x=6, y=4
                )
            ]
        )
        
        self.dashboards["system_overview"] = system_dashboard
        self.dashboards["code_quality"] = code_dashboard
    
    async def _serve_dashboard(self, request):
        """Serve the main dashboard page"""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Vibe Check Dashboard</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                .dashboard { display: grid; grid-template-columns: repeat(12, 1fr); gap: 20px; }
                .panel { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .panel h3 { margin: 0 0 15px 0; color: #333; }
                .chart-container { position: relative; height: 300px; }
                .stat-value { font-size: 2em; font-weight: bold; color: #2563eb; }
                .stat-change { font-size: 0.9em; margin-top: 5px; }
                .stat-change.positive { color: #16a34a; }
                .stat-change.negative { color: #dc2626; }
            </style>
        </head>
        <body>
            <h1>Vibe Check Dashboard</h1>
            <div id="dashboard" class="dashboard"></div>
            
            <script>
                // Dashboard JavaScript will be injected here
                const ws = new WebSocket('ws://localhost:8080/ws');
                const charts = {};
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    updatePanel(data.panel_id, data.chart_config);
                };
                
                function updatePanel(panelId, chartConfig) {
                    // Update panel with new data
                    console.log('Updating panel:', panelId, chartConfig);
                }
                
                // Load initial dashboard
                loadDashboard('system_overview');
                
                async function loadDashboard(dashboardId) {
                    const response = await fetch(`/api/dashboards/${dashboardId}`);
                    const dashboard = await response.json();
                    renderDashboard(dashboard);
                }
                
                function renderDashboard(dashboard) {
                    const container = document.getElementById('dashboard');
                    container.innerHTML = '';
                    
                    dashboard.panels.forEach(panel => {
                        const panelElement = createPanelElement(panel);
                        container.appendChild(panelElement);
                    });
                }
                
                function createPanelElement(panel) {
                    const div = document.createElement('div');
                    div.className = 'panel';
                    div.style.gridColumn = `span ${panel.width}`;
                    div.innerHTML = `
                        <h3>${panel.title}</h3>
                        <div class="chart-container">
                            <canvas id="chart-${panel.id}"></canvas>
                        </div>
                    `;
                    return div;
                }
            </script>
        </body>
        </html>
        """
        return web.Response(text=html_content, content_type='text/html')
    
    async def _list_dashboards(self, request):
        """List all available dashboards"""
        dashboards = [
            {
                "id": dashboard.id,
                "title": dashboard.title,
                "description": dashboard.description,
                "tags": dashboard.tags
            }
            for dashboard in self.dashboards.values()
        ]
        return web.json_response(dashboards)
    
    async def _get_dashboard(self, request):
        """Get a specific dashboard configuration"""
        dashboard_id = request.match_info['dashboard_id']
        
        if dashboard_id not in self.dashboards:
            return web.json_response({"error": "Dashboard not found"}, status=404)
        
        dashboard = self.dashboards[dashboard_id]
        return web.json_response({
            "id": dashboard.id,
            "title": dashboard.title,
            "description": dashboard.description,
            "panels": [
                {
                    "id": panel.id,
                    "title": panel.title,
                    "chart_type": panel.chart_type.value,
                    "metrics": panel.metrics,
                    "width": panel.width,
                    "height": panel.height,
                    "x": panel.x,
                    "y": panel.y,
                    "options": panel.options
                }
                for panel in dashboard.panels
            ],
            "refresh_interval": dashboard.refresh_interval,
            "time_range": dashboard.time_range
        })
    
    async def _create_dashboard(self, request):
        """Create a new dashboard"""
        data = await request.json()
        
        dashboard = DashboardConfig(
            id=data["id"],
            title=data["title"],
            description=data.get("description", ""),
            panels=[
                PanelConfig(**panel_data) for panel_data in data.get("panels", [])
            ]
        )
        
        self.dashboards[dashboard.id] = dashboard
        return web.json_response({"status": "created", "id": dashboard.id})
    
    async def _get_panel_data(self, request):
        """Get data for a specific panel"""
        panel_id = request.match_info['panel_id']
        time_range = request.query.get('time_range', '1h')
        
        # Find panel in dashboards
        panel = None
        for dashboard in self.dashboards.values():
            for p in dashboard.panels:
                if p.id == panel_id:
                    panel = p
                    break
        
        if not panel:
            return web.json_response({"error": "Panel not found"}, status=404)
        
        # Calculate time range
        end_time = time.time()
        if time_range == "1h":
            start_time = end_time - 3600
        elif time_range == "24h":
            start_time = end_time - 86400
        else:
            start_time = end_time - 3600  # Default to 1 hour
        
        # Query data for all metrics in the panel
        panel_data = []
        for metric in panel.metrics:
            data_source = self.data_sources["vibe_check"]
            metric_data = await data_source.query(metric, start_time, end_time)
            panel_data.append(metric_data)
        
        # Render chart based on panel type
        if panel.chart_type == ChartType.LINE:
            chart_config = self.chart_renderer.render_line_chart(panel_data[0], panel.options)
        elif panel.chart_type == ChartType.GAUGE:
            chart_config = self.chart_renderer.render_gauge(panel_data[0], panel.options)
        elif panel.chart_type == ChartType.STAT:
            chart_config = self.chart_renderer.render_stat(panel_data[0], panel.options)
        else:
            chart_config = {"error": f"Unsupported chart type: {panel.chart_type}"}
        
        return web.json_response({
            "panel_id": panel_id,
            "chart_config": chart_config,
            "last_updated": time.time()
        })
    
    async def _websocket_handler(self, request):
        """Handle WebSocket connections for real-time updates"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        self.websockets.append(ws)
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    data = json.loads(msg.data)
                    # Handle WebSocket messages (e.g., subscribe to panels)
                elif msg.type == WSMsgType.ERROR:
                    print(f'WebSocket error: {ws.exception()}')
        finally:
            self.websockets.remove(ws)
        
        return ws
    
    async def broadcast_update(self, panel_id: str, chart_config: Dict[str, Any]):
        """Broadcast panel updates to all connected WebSocket clients"""
        message = json.dumps({
            "panel_id": panel_id,
            "chart_config": chart_config,
            "timestamp": time.time()
        })
        
        # Remove closed connections
        self.websockets = [ws for ws in self.websockets if not ws.closed]
        
        # Broadcast to all active connections
        for ws in self.websockets:
            try:
                await ws.send_str(message)
            except Exception as e:
                print(f"Error broadcasting to WebSocket: {e}")
    
    async def start(self):
        """Start the dashboard server"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', self.port)
        await site.start()
        
        print(f"Vibe Check Dashboard running on http://localhost:{self.port}")
    
    async def stop(self):
        """Stop the dashboard server"""
        await self.app.cleanup()


# Example usage
async def main():
    """Example usage of the dashboard engine"""
    from vibe_check_monitoring_engine import VibeCheckMonitoringEngine, SystemMetricsCollector, CodeQualityCollector
    
    # Start monitoring engine
    monitoring_engine = VibeCheckMonitoringEngine()
    monitoring_engine.add_collector(SystemMetricsCollector(interval=5.0))
    monitoring_engine.add_collector(CodeQualityCollector("/path/to/project", interval=60.0))
    await monitoring_engine.start()
    
    # Start dashboard
    dashboard = DashboardEngine(monitoring_engine)
    await dashboard.start()
    
    print("Dashboard available at http://localhost:8080")
    
    # Keep running
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        await dashboard.stop()
        await monitoring_engine.stop()


if __name__ == "__main__":
    asyncio.run(main())
