# Project Analysis Report

**Project:** /Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check

**Date:** 2025-06-25 18:42:05

## Summary

- **Total Files:** 264
- **Total Directories:** 42
- **Total Issues:** 686
- **Max Complexity:** 48

## Files

| File | Lines | Issues | Complexity |
|------|-------|--------|------------|
| __init__.py | 37 | 1 | 0 |
| __main__.py | 11 | 1 | 0 |
| ai/__init__.py | 27 | 1 | 0 |
| ai/explanation/__init__.py | 16 | 1 | 0 |
| ai/explanation/comment_analyzer.py | 526 | 8 | 41 |
| ai/explanation/documentation_generator.py | 636 | 5 | 41 |
| ai/explanation/explanation_engine.py | 496 | 6 | 40 |
| ai/infrastructure/__init__.py | 24 | 1 | 0 |
| ai/infrastructure/model_manager.py | 517 | 4 | 39 |
| ai/infrastructure/model_optimizer.py | 503 | 3 | 39 |
| ai/infrastructure/privacy_processor.py | 422 | 2 | 33 |
| ai/refactoring/__init__.py | 26 | 1 | 0 |
| ai/refactoring/code_smell_detector.py | 575 | 5 | 39 |
| ai/refactoring/impact_analyzer.py | 648 | 5 | 39 |
| ai/refactoring/pattern_recommender.py | 621 | 5 | 38 |
| ai/refactoring/refactoring_engine.py | 603 | 4 | 39 |
| ai/temporal/__init__.py | 26 | 1 | 0 |
| ai/temporal/debt_predictor.py | 553 | 4 | 34 |
| ai/temporal/productivity_analyzer.py | 726 | 5 | 41 |
| ai/temporal/temporal_engine.py | 580 | 7 | 40 |
| ai/temporal/trend_visualizer.py | 640 | 5 | 38 |
| ai/visualization/__init__.py | 26 | 1 | 0 |
| ai/visualization/dashboard_engine.py | 778 | 3 | 36 |
| ai/visualization/data_aggregator.py | 552 | 6 | 42 |
| ai/visualization/interactive_charts.py | 641 | 4 | 34 |
| ai/visualization/report_generator.py | 712 | 5 | 40 |
| cli/__init__.py | 18 | 1 | 0 |
| cli/commands.py | 692 | 6 | 43 |
| cli/completion.py | 217 | 3 | 28 |
| cli/error_handler.py | 198 | 3 | 34 |
| cli/format_tool.py | 144 | 4 | 33 |
| cli/formatters.py | 391 | 5 | 45 |
| cli/handlers.py | 301 | 5 | 38 |
| cli/knowledge_manager.py | 323 | 3 | 34 |
| cli/main.py | 286 | 3 | 31 |
| cli/output_formats.py | 290 | 1 | 23 |
| cli/parallel_processing.py | 375 | 3 | 34 |
| cli/standalone.py | 471 | 6 | 42 |
| cli/standalone_suite.py | 259 | 4 | 37 |
| cli/watch_mode.py | 260 | 1 | 34 |
| compat.py | 259 | 2 | 32 |
| core/__init__.py | 49 | 1 | 0 |
| core/analysis/__init__.py | 23 | 1 | 0 |
| core/analysis/dependency_analyzer.py | 697 | 7 | 43 |
| core/analysis/file_analyzer.py | 161 | 2 | 28 |
| core/analysis/framework_detector.py | 421 | 9 | 40 |
| core/analysis/framework_rules.py | 476 | 2 | 34 |
| core/analysis/import_analyzer.py | 495 | 6 | 39 |
| core/analysis/import_visualizer.py | 68 | 1 | 20 |
| core/analysis/meta_analyzer.py | 540 | 2 | 40 |
| core/analysis/metrics_aggregator.py | 161 | 1 | 27 |
| core/analysis/performance_optimizer.py | 436 | 6 | 37 |
| core/analysis/project_analyzer.py | 193 | 2 | 32 |
| core/analysis/project_meritocracy_analyzer.py | 520 | 5 | 40 |
| core/analysis/python_semantic_analyzer.py | 499 | 10 | 40 |
| core/analysis/python_version_analyzer.py | 375 | 9 | 33 |
| core/analysis/result_processor.py | 165 | 1 | 33 |
| core/analysis/semantic_output_formatter.py | 409 | 1 | 36 |
| core/analysis/semantic_rules.py | 453 | 1 | 29 |
| core/analysis/standalone_analyzer.py | 421 | 1 | 38 |
| core/analysis/tool_executor.py | 140 | 1 | 27 |
| core/analysis/type_analyzer.py | 370 | 7 | 39 |
| core/analysis/visualization/__init__.py | 47 | 1 | 0 |
| core/analysis/visualization/chart_generators.py | 257 | 1 | 30 |
| core/analysis/visualization/html_generators.py | 240 | 1 | 28 |
| core/analysis/visualization/interactive_dashboard.py | 234 | 1 | 25 |
| core/compatibility.py | 81 | 1 | 15 |
| core/config.py | 229 | 2 | 34 |
| core/dependency_manager.py | 358 | 5 | 33 |
| core/docs/__init__.py | 48 | 1 | 0 |
| core/docs/templates.py | 97 | 1 | 0 |
| core/docs/utils.py | 349 | 6 | 34 |
| core/error_handling.py | 320 | 2 | 32 |
| core/error_handling/__init__.py | 55 | 1 | 0 |
| core/error_handling/decorators.py | 126 | 1 | 23 |
| core/error_handling/error_manager.py | 115 | 1 | 25 |
| core/error_handling/exceptions.py | 193 | 1 | 13 |
| core/error_handling/handlers.py | 185 | 1 | 28 |
| core/fs_utils.py | 285 | 2 | 36 |
| core/knowledge/framework_knowledge_base.py | 626 | 3 | 35 |
| core/knowledge/rule_engine.py | 396 | 7 | 41 |
| core/logging.py | 261 | 1 | 29 |
| core/logging/__init__.py | 49 | 1 | 0 |
| core/logging/contextual_logger.py | 134 | 1 | 20 |
| core/logging/correlation.py | 214 | 1 | 26 |
| core/logging/setup.py | 94 | 1 | 22 |
| core/logging/structured_logger.py | 220 | 2 | 25 |
| core/models/__init__.py | 23 | 1 | 0 |
| core/models/directory_metrics.py | 286 | 1 | 31 |
| core/models/file_metrics.py | 400 | 3 | 33 |
| core/models/progress_tracker.py | 566 | 4 | 29 |
| core/models/project_metrics.py | 450 | 3 | 38 |
| core/progress.py | 997 | 2 | 35 |
| core/simple_analyzer.py | 144 | 2 | 28 |
| core/trend_analysis/__init__.py | 16 | 1 | 0 |
| core/trend_analysis/trend_analyzer.py | 340 | 2 | 36 |
| core/trend_analysis/trend_storage.py | 196 | 1 | 26 |
| core/trend_analysis/trend_visualizer.py | 476 | 3 | 31 |
| core/utils/__init__.py | 99 | 1 | 0 |
| core/utils/async_utils.py | 264 | 1 | 31 |
| core/utils/config_utils.py | 139 | 1 | 25 |
| core/utils/dict_utils.py | 211 | 1 | 29 |
| core/utils/error_handling.py | 127 | 1 | 19 |
| core/utils/error_utils.py | 309 | 1 | 28 |
| core/utils/file_utils.py | 175 | 3 | 31 |
| core/utils/fs_utils.py | 392 | 3 | 39 |
| core/utils/gitignore_utils.py | 75 | 1 | 24 |
| core/utils/preset_manager.py | 113 | 1 | 21 |
| core/utils/report_utils.py | 239 | 1 | 27 |
| core/utils/tool_utils.py | 145 | 1 | 29 |
| core/vcs/__init__.py | 40 | 1 | 0 |
| core/vcs/auto_fix.py | 345 | 2 | 39 |
| core/vcs/cache.py | 423 | 3 | 38 |
| core/vcs/config.py | 283 | 4 | 36 |
| core/vcs/dependency_tracker.py | 382 | 1 | 39 |
| core/vcs/engine.py | 1103 | 5 | 48 |
| core/vcs/incremental_analysis.py | 332 | 2 | 38 |
| core/vcs/integration/__init__.py | 18 | 1 | 0 |
| core/vcs/integration/ecosystem_integrator.py | 39 | 1 | 16 |
| core/vcs/integration/integration_manager.py | 311 | 3 | 33 |
| core/vcs/integration/meta_analyzer.py | 308 | 2 | 34 |
| core/vcs/integration/unified_reporter.py | 417 | 2 | 34 |
| core/vcs/memory_manager.py | 355 | 2 | 35 |
| core/vcs/models.py | 246 | 4 | 27 |
| core/vcs/performance.py | 410 | 3 | 35 |
| core/vcs/plugins/__init__.py | 20 | 1 | 0 |
| core/vcs/plugins/plugin_interface.py | 306 | 1 | 29 |
| core/vcs/plugins/plugin_loader.py | 264 | 1 | 37 |
| core/vcs/plugins/plugin_manager.py | 280 | 1 | 37 |
| core/vcs/plugins/plugin_registry.py | 301 | 2 | 38 |
| core/vcs/registry.py | 362 | 3 | 35 |
| core/vcs/rules/__init__.py | 19 | 1 | 0 |
| core/vcs/rules/advanced_python_rules.py | 412 | 1 | 38 |
| core/vcs/rules/complexity_rules.py | 350 | 24 | 31 |
| core/vcs/rules/documentation_rules.py | 337 | 10 | 32 |
| core/vcs/rules/framework_rules/__init__.py | 19 | 1 | 0 |
| core/vcs/rules/framework_rules/django_rules.py | 326 | 2 | 37 |
| core/vcs/rules/framework_rules/fastapi_rules.py | 389 | 1 | 38 |
| core/vcs/rules/framework_rules/flask_rules.py | 351 | 4 | 31 |
| core/vcs/rules/framework_rules/framework_detector.py | 322 | 3 | 38 |
| core/vcs/rules/import_rules.py | 382 | 13 | 28 |
| core/vcs/rules/performance_rules.py | 400 | 2 | 39 |
| core/vcs/rules/rule_loader.py | 445 | 2 | 34 |
| core/vcs/rules/security_rules.py | 298 | 7 | 27 |
| core/vcs/rules/style_rules.py | 300 | 5 | 31 |
| core/vcs/rules/type_rules.py | 487 | 15 | 33 |
| core/vcs/type_checking.py | 335 | 4 | 39 |
| core/version.py | 8 | 1 | 0 |
| enterprise/__init__.py | 25 | 1 | 0 |
| enterprise/api/__init__.py | 30 | 1 | 0 |
| enterprise/api/graphql.py | 523 | 2 | 38 |
| enterprise/api/models.py | 289 | 4 | 13 |
| enterprise/api/rest.py | 500 | 4 | 37 |
| enterprise/api/unified.py | 390 | 1 | 37 |
| enterprise/api/websocket.py | 369 | 2 | 35 |
| enterprise/cicd/__init__.py | 31 | 1 | 0 |
| enterprise/cicd/azure_devops.py | 549 | 1 | 32 |
| enterprise/cicd/github_actions.py | 368 | 1 | 31 |
| enterprise/cicd/gitlab_ci.py | 436 | 1 | 32 |
| enterprise/cicd/jenkins.py | 413 | 2 | 32 |
| enterprise/cicd/manager.py | 409 | 1 | 37 |
| enterprise/cicd/models.py | 274 | 5 | 18 |
| enterprise/collaboration.py | 875 | 5 | 43 |
| enterprise/collaboration/__init__.py | 31 | 1 | 0 |
| enterprise/collaboration/analysis.py | 457 | 5 | 32 |
| enterprise/collaboration/shared_config.py | 347 | 2 | 33 |
| enterprise/collaboration/team_manager.py | 365 | 3 | 36 |
| enterprise/dashboard/__init__.py | 32 | 1 | 0 |
| enterprise/dashboard/components.py | 504 | 2 | 23 |
| enterprise/dashboard/models.py | 259 | 4 | 6 |
| enterprise/dashboard/static_assets.py | 487 | 1 | 22 |
| enterprise/dashboard/templates.py | 778 | 1 | 29 |
| enterprise/dashboard/web_server.py | 519 | 2 | 38 |
| enterprise/integration.py | 30 | 1 | 12 |
| enterprise/monitoring.py | 62 | 1 | 18 |
| enterprise/monitoring/__init__.py | 35 | 1 | 0 |
| enterprise/monitoring/alerting.py | 536 | 2 | 37 |
| enterprise/monitoring/dashboards.py | 530 | 3 | 40 |
| enterprise/monitoring/models.py | 300 | 6 | 23 |
| enterprise/monitoring/monitoring.py | 486 | 3 | 39 |
| enterprise/monitoring/quality_gates.py | 515 | 3 | 35 |
| enterprise/performance/__init__.py | 30 | 1 | 0 |
| enterprise/performance/cache_manager.py | 404 | 4 | 38 |
| enterprise/performance/distributed_processor.py | 450 | 4 | 37 |
| enterprise/performance/load_balancer.py | 59 | 2 | 16 |
| enterprise/performance/performance_optimizer.py | 376 | 5 | 39 |
| enterprise/performance/scalability_manager.py | 71 | 1 | 18 |
| enterprise/reporting/__init__.py | 30 | 1 | 0 |
| enterprise/reporting/customization.py | 109 | 1 | 25 |
| enterprise/reporting/engine.py | 448 | 3 | 34 |
| enterprise/reporting/executive.py | 434 | 4 | 35 |
| enterprise/reporting/formats.py | 448 | 1 | 29 |
| enterprise/reporting/templates.py | 391 | 2 | 31 |
| enterprise/security/__init__.py | 36 | 1 | 0 |
| enterprise/security/access_control.py | 475 | 2 | 38 |
| enterprise/security/audit_trail.py | 470 | 9 | 31 |
| enterprise/security/compliance_manager.py | 409 | 4 | 31 |
| enterprise/security/encryption_manager.py | 103 | 2 | 23 |
| enterprise/security/security_monitor.py | 336 | 4 | 32 |
| plugins/__init__.py | 26 | 1 | 0 |
| plugins/base_plugin.py | 17 | 1 | 0 |
| plugins/manager.py | 536 | 6 | 42 |
| plugins/plugin_base.py | 246 | 1 | 25 |
| plugins/plugin_interface.py | 194 | 1 | 23 |
| plugins/plugin_registry.py | 256 | 1 | 30 |
| tools/custom_rules/__init__.py | 12 | 1 | 0 |
| tools/custom_rules/python_rules.py | 328 | 3 | 36 |
| tools/parsers/__init__.py | 35 | 1 | 0 |
| tools/parsers/bandit_parser.py | 185 | 1 | 24 |
| tools/parsers/base_parser.py | 81 | 1 | 20 |
| tools/parsers/complexity_parser.py | 191 | 2 | 26 |
| tools/parsers/custom_rules_parser.py | 158 | 1 | 22 |
| tools/parsers/doc_analyzer_parser.py | 259 | 2 | 33 |
| tools/parsers/mypy_parser.py | 171 | 1 | 23 |
| tools/parsers/parser_registry.py | 64 | 1 | 14 |
| tools/parsers/pyflakes_parser.py | 158 | 1 | 25 |
| tools/parsers/pylint_parser.py | 151 | 1 | 22 |
| tools/parsers/ruff_parser.py | 187 | 1 | 21 |
| tools/runners/__init__.py | 38 | 1 | 0 |
| tools/runners/bandit_runner.py | 167 | 2 | 27 |
| tools/runners/base_runner.py | 215 | 4 | 30 |
| tools/runners/complexity_runner.py | 362 | 12 | 30 |
| tools/runners/custom_rules_runner.py | 101 | 1 | 19 |
| tools/runners/doc_analyzer_runner.py | 345 | 6 | 35 |
| tools/runners/mypy_runner.py | 226 | 2 | 33 |
| tools/runners/pyflakes_runner.py | 135 | 1 | 26 |
| tools/runners/pylint_runner.py | 204 | 1 | 30 |
| tools/runners/ruff_runner.py | 209 | 3 | 30 |
| tools/runners/tool_registry.py | 119 | 1 | 22 |
| ui/cli/__init__.py | 20 | 1 | 0 |
| ui/cli/commands.py | 142 | 1 | 23 |
| ui/cli/formatter.py | 258 | 1 | 29 |
| ui/gui/__init__.py | 22 | 1 | 0 |
| ui/gui/app.py | 67 | 1 | 15 |
| ui/gui/main_window.py | 364 | 2 | 33 |
| ui/gui/simple_gui.py | 463 | 3 | 33 |
| ui/gui/themes.py | 325 | 1 | 23 |
| ui/reporting/__init__.py | 34 | 1 | 0 |
| ui/reporting/custom_report_generator.py | 185 | 2 | 28 |
| ui/reporting/formatters.py | 159 | 2 | 28 |
| ui/reporting/generators.py | 192 | 2 | 28 |
| ui/reporting/markdown.py | 141 | 1 | 23 |
| ui/reporting/report_generator.py | 577 | 1 | 22 |
| ui/reporting/templates.py | 99 | 1 | 23 |
| ui/tui/__init__.py | 22 | 1 | 0 |
| ui/tui/app.py | 256 | 4 | 35 |
| ui/tui/components.py | 70 | 1 | 21 |
| ui/tui/config_components.py | 107 | 1 | 20 |
| ui/tui/header_footer.py | 78 | 1 | 20 |
| ui/tui/menu_components.py | 102 | 1 | 20 |
| ui/tui/progress_components.py | 105 | 1 | 24 |
| ui/tui/results_components.py | 309 | 2 | 34 |
| ui/tui/state_manager.py | 269 | 3 | 30 |
| ui/visualization/__init__.py | 51 | 1 | 0 |
| ui/visualization/charts.py | 284 | 1 | 21 |
| ui/visualization/exporters.py | 135 | 1 | 18 |
| ui/visualization/generators.py | 338 | 1 | 24 |
| ui/visualization/interactive_charts.py | 1080 | 2 | 39 |
| ui/visualization/visualization_generator.py | 282 | 1 | 27 |
| ui/web/__init__.py | 23 | 1 | 0 |
| ui/web/app.py | 218 | 2 | 30 |
| ui/web/components.py | 631 | 3 | 38 |
| ui/web/run_web_ui.py | 84 | 1 | 6 |
| ui/web/state_manager.py | 265 | 3 | 29 |

## Issues

### __init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### __main__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ai/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ai/explanation/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ai/explanation/comment_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 19 |  | info | Potential anemic model: 'CommentType' has only data, no behavior |
| 30 |  | info | Potential anemic model: 'CommentQuality' has only data, no behavior |
| 107 |  | warning | Function 'extract_comments' has complexity 11, consider refactoring (max: 10) |
| 253 |  | warning | Function '_calculate_quality_score' has complexity 16, consider refactoring (max: 10) |
| 306 |  | warning | Function '_identify_issues' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |
| 230 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |
| 392 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/explanation/documentation_generator.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'DocumentationFormat' has only data, no behavior |
| 30 |  | info | Potential anemic model: 'DocumentationStyle' has only data, no behavior |
| 152 |  | warning | Function 'analyze_code_elements' has complexity 12, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |
| 324 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/explanation/explanation_engine.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'ExplanationLevel' has only data, no behavior |
| 29 |  | info | Potential anemic model: 'ExplanationType' has only data, no behavior |
| 86 |  | warning | Function 'analyze_code_structure' has complexity 13, consider refactoring (max: 10) |
| 140 |  | warning | Function 'detect_patterns' has complexity 12, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |
| 230 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/infrastructure/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ai/infrastructure/model_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 22 |  | info | Potential anemic model: 'ModelType' has only data, no behavior |
| 31 |  | info | Potential anemic model: 'ModelStatus' has only data, no behavior |
| 510 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |

### ai/infrastructure/model_optimizer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'OptimizationStrategy' has only data, no behavior |
| 300 |  | warning | Function 'optimize_model_for_target' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### ai/infrastructure/privacy_processor.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 19 |  | info | Potential anemic model: 'PrivacyLevel' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### ai/refactoring/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ai/refactoring/code_smell_detector.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'CodeSmell' has only data, no behavior |
| 44 |  | info | Potential anemic model: 'SmellSeverity' has only data, no behavior |
| 363 |  | warning | Function 'detect_smells' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |
| 375 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/refactoring/impact_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 19 |  | info | Potential anemic model: 'ImpactLevel' has only data, no behavior |
| 28 |  | info | Potential anemic model: 'ImpactType' has only data, no behavior |
| 112 |  | warning | Function 'analyze_dependencies' has complexity 17, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |
| 405 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/refactoring/pattern_recommender.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 19 |  | info | Potential anemic model: 'DesignPattern' has only data, no behavior |
| 44 |  | info | Potential anemic model: 'PatternCategory' has only data, no behavior |
| 429 |  | warning | Function 'analyze_patterns' has complexity 13, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |
| 441 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/refactoring/refactoring_engine.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'RefactoringType' has only data, no behavior |
| 37 |  | info | Potential anemic model: 'RefactoringSeverity' has only data, no behavior |
| 0 | mypy.error | LOW |  |
| 281 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/temporal/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ai/temporal/debt_predictor.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'DebtCategory' has only data, no behavior |
| 32 |  | info | Potential anemic model: 'DebtSeverity' has only data, no behavior |
| 0 | mypy.error | LOW |  |
| 426 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/temporal/productivity_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'ProductivityMetric' has only data, no behavior |
| 34 |  | info | Potential anemic model: 'DeveloperInsight' has only data, no behavior |
| 392 |  | warning | Function '_generate_trend_insights' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |
| 466 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/temporal/temporal_engine.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'EvolutionMetric' has only data, no behavior |
| 35 |  | info | Potential anemic model: 'AnalysisTimeframe' has only data, no behavior |
| 457 |  | warning | Function '_generate_insights' has complexity 12, consider refactoring (max: 10) |
| 494 |  | warning | Function '_generate_recommendations' has complexity 12, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |
| 137 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |
| 397 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/temporal/trend_visualizer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'VisualizationType' has only data, no behavior |
| 34 |  | info | Potential anemic model: 'TrendDirection' has only data, no behavior |
| 0 | mypy.error | LOW |  |
| 298 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |
| 367 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/visualization/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ai/visualization/dashboard_engine.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'DashboardType' has only data, no behavior |
| 33 |  | info | Potential anemic model: 'LayoutType' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### ai/visualization/data_aggregator.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'AggregationType' has only data, no behavior |
| 273 |  | warning | Function '_aggregate_simple' has complexity 20, consider refactoring (max: 10) |
| 448 |  | warning | Function 'create_time_series_aggregation' has complexity 13, consider refactoring (max: 10) |
| 448 |  | warning | Function 'create_time_series_aggregation' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |
| 216 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### ai/visualization/interactive_charts.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'ChartInteraction' has only data, no behavior |
| 34 |  | info | Potential anemic model: 'AnimationType' has only data, no behavior |
| 345 |  | warning | Function 'create_interactive_chart' has 8 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### ai/visualization/report_generator.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'ReportFormat' has only data, no behavior |
| 32 |  | info | Potential anemic model: 'ReportStyle' has only data, no behavior |
| 444 |  | warning | Function 'generate_report' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |
| 470 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### cli/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### cli/commands.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | warning | Function 'analyze_command' has complexity 50, consider refactoring (max: 10) |
| 20 |  | warning | Function 'analyze_command' has 10 arguments, consider reducing (max: 5) |
| 188 |  | warning | Function 'run_vcs_analysis' has complexity 12, consider refactoring (max: 10) |
| 336 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |
| 336 | B110 | LOW | Try, Except, Pass detected. |

### cli/completion.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 46 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |
| 46 | B110 | LOW | Try, Except, Pass detected. |

### cli/error_handler.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 17 |  | warning | Function 'format_error_results' has complexity 17, consider refactoring (max: 10) |
| 98 |  | warning | Function 'handle_analysis_error' has complexity 12, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### cli/format_tool.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 35 |  | warning | Function 'vibe_format' has complexity 25, consider refactoring (max: 10) |
| 35 |  | warning | Function 'vibe_format' has 8 arguments, consider reducing (max: 5) |
| 39 |  | warning | Function 'run_format' has complexity 23, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### cli/formatters.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 126 |  | warning | Function '_format_file_count' has complexity 14, consider refactoring (max: 10) |
| 161 |  | warning | Function '_format_line_count' has complexity 12, consider refactoring (max: 10) |
| 214 |  | warning | Function '_format_issue_count' has complexity 25, consider refactoring (max: 10) |
| 359 |  | warning | Function '_format_generated_reports' has complexity 12, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### cli/handlers.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 19 |  | warning | Function 'handle_analyze_command' has complexity 23, consider refactoring (max: 10) |
| 19 |  | warning | Function 'handle_analyze_command' has 18 arguments, consider reducing (max: 5) |
| 235 |  | warning | Function 'handle_debug_command' has complexity 11, consider refactoring (max: 10) |
| 235 |  | warning | Function 'handle_debug_command' has 7 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### cli/knowledge_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 136 |  | warning | Function 'add_rule' has 7 arguments, consider reducing (max: 5) |
| 275 |  | warning | Function 'validate' has complexity 14, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### cli/main.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 46 |  | warning | Function 'analyze' has 18 arguments, consider reducing (max: 5) |
| 205 |  | warning | Function 'debug' has 7 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### cli/output_formats.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### cli/parallel_processing.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 35 |  | warning | Function 'analyze_files_parallel' has complexity 11, consider refactoring (max: 10) |
| 302 |  | warning | Function 'analyze_files_parallel' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### cli/standalone.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 128 |  | warning | Function 'check' has complexity 15, consider refactoring (max: 10) |
| 128 |  | warning | Function 'check' has 8 arguments, consider reducing (max: 5) |
| 131 |  | warning | Function 'run_check' has complexity 15, consider refactoring (max: 10) |
| 227 |  | warning | Function 'fix' has complexity 20, consider refactoring (max: 10) |
| 229 |  | warning | Function 'run_fix' has complexity 20, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### cli/standalone_suite.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 43 |  | warning | Function 'vibe_check_standalone' has complexity 40, consider refactoring (max: 10) |
| 43 |  | warning | Function 'vibe_check_standalone' has 12 arguments, consider reducing (max: 5) |
| 48 |  | warning | Function 'run_analysis' has complexity 40, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### cli/watch_mode.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### compat.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |
| 135 | B108 | MEDIUM | Probable insecure usage of temp file/directory. |

### core/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/dependency_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'ImportInfo' has only data, no behavior |
| 32 |  | info | Potential anemic model: 'DependencyNode' has only data, no behavior |
| 43 |  | info | Potential anemic model: 'CircularDependency' has only data, no behavior |
| 53 |  | info | Potential anemic model: 'DependencyAnalysisResult' has only data, no behavior |
| 71 |  | info | Function 'visit_Import' should use snake_case naming |
| 83 |  | info | Function 'visit_ImportFrom' should use snake_case naming |
| 0 | mypy.error | LOW |  |

### core/analysis/file_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 33 |  | warning | Function '__init__' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### core/analysis/framework_detector.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'FrameworkSignature' has only data, no behavior |
| 35 |  | info | Potential anemic model: 'FrameworkDetection' has only data, no behavior |
| 47 |  | info | Potential anemic model: 'FrameworkAnalysisResult' has only data, no behavior |
| 221 |  | warning | Function '_evaluate_framework' has complexity 23, consider refactoring (max: 10) |
| 372 |  | info | Function 'visit_Import' should use snake_case naming |
| 378 |  | info | Function 'visit_ImportFrom' should use snake_case naming |
| 387 |  | info | Function 'visit_ClassDef' should use snake_case naming |
| 398 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 0 | mypy.error | LOW |  |

### core/analysis/framework_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 29 |  | warning | Function 'check' has complexity 12, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/analysis/import_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 30 |  | info | Potential anemic model: 'ImportInfo' has only data, no behavior |
| 42 |  | info | Potential anemic model: 'CircularDependency' has only data, no behavior |
| 51 |  | info | Potential anemic model: 'ImportAnalysisResult' has only data, no behavior |
| 256 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |
| 256 | B110 | LOW | Try, Except, Pass detected. |

### core/analysis/import_visualizer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/meta_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 25 |  | error | God class detected: 'MetaAnalyzer' has 21 methods |
| 0 | mypy.error | LOW |  |

### core/analysis/metrics_aggregator.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/performance_optimizer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 23 |  | info | Potential anemic model: 'AnalysisCache' has only data, no behavior |
| 34 |  | info | Potential anemic model: 'PerformanceMetrics' has only data, no behavior |
| 79 |  | warning | Function 'cache_result' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |
| 11 | B403 | LOW | Consider possible security implications associated with pickle module. |
| 103 | B301 | MEDIUM | Pickle and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue. |

### core/analysis/project_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 81 |  | warning | Function 'analyze_project' has complexity 16, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/analysis/project_meritocracy_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'ArchitecturalPattern' has only data, no behavior |
| 32 |  | info | Potential anemic model: 'QualityIndicator' has only data, no behavior |
| 43 |  | info | Potential anemic model: 'ProjectMeritocracyResult' has only data, no behavior |
| 424 |  | warning | Function '_analyze_success_factors' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/analysis/python_semantic_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 18 |  | info | Potential anemic model: 'SemanticContext' has only data, no behavior |
| 32 |  | info | Potential anemic model: 'SemanticIssue' has only data, no behavior |
| 44 |  | info | Potential anemic model: 'SemanticAnalysisResult' has only data, no behavior |
| 384 |  | info | Function 'visit_Import' should use snake_case naming |
| 397 |  | info | Function 'visit_ImportFrom' should use snake_case naming |
| 412 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 438 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 464 |  | info | Function 'visit_ClassDef' should use snake_case naming |
| 490 |  | info | Function 'visit_Name' should use snake_case naming |
| 0 | mypy.error | LOW |  |

### core/analysis/python_version_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'VersionFeature' has only data, no behavior |
| 31 |  | info | Potential anemic model: 'CompatibilityIssue' has only data, no behavior |
| 42 |  | info | Potential anemic model: 'VersionCompatibilityResult' has only data, no behavior |
| 152 |  | info | Function 'visit_NamedExpr' should use snake_case naming |
| 164 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 177 |  | info | Function 'visit_BinOp' should use snake_case naming |
| 187 |  | info | Function 'visit_Subscript' should use snake_case naming |
| 203 |  | info | Function 'visit_Match' should use snake_case naming |
| 0 | mypy.error | LOW |  |

### core/analysis/result_processor.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/semantic_output_formatter.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/semantic_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/standalone_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/tool_executor.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/type_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'TypeInfo' has only data, no behavior |
| 101 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 126 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 149 |  | warning | Function '_analyze_function_args' has complexity 13, consider refactoring (max: 10) |
| 188 |  | warning | Function '_all_parameters_typed' has complexity 12, consider refactoring (max: 10) |
| 307 |  | warning | Function 'check' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/analysis/visualization/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/visualization/chart_generators.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/visualization/html_generators.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/analysis/visualization/interactive_dashboard.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/compatibility.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/config.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 194 |  | warning | Function 'validate_config' has complexity 15, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/dependency_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'DependencyStatus' has only data, no behavior |
| 30 |  | info | Potential anemic model: 'DependencyInfo' has only data, no behavior |
| 0 | mypy.error | LOW |  |
| 9 | B404 | LOW | Consider possible security implications associated with the subprocess module. |
| 225 | B603 | LOW | subprocess call - check for execution of untrusted input. |

### core/docs/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/docs/templates.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/docs/utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 104 |  | warning | Function 'generate_function_docstring' has complexity 11, consider refactoring (max: 10) |
| 104 |  | warning | Function 'generate_function_docstring' has 7 arguments, consider reducing (max: 5) |
| 175 |  | warning | Function 'generate_async_function_docstring' has complexity 11, consider refactoring (max: 10) |
| 175 |  | warning | Function 'generate_async_function_docstring' has 7 arguments, consider reducing (max: 5) |
| 311 |  | warning | Function 'generate_file_header' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### core/error_handling.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 41 |  | info | Potential anemic model: 'ConfigurationError' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### core/error_handling/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/error_handling/decorators.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/error_handling/error_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/error_handling/exceptions.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/error_handling/handlers.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/fs_utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 98 |  | warning | Function 'find_files' has complexity 13, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/knowledge/framework_knowledge_base.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 22 |  | info | Potential anemic model: 'FrameworkRule' has only data, no behavior |
| 52 |  | info | Potential anemic model: 'FrameworkKnowledge' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### core/knowledge/rule_engine.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 22 |  | info | Potential anemic model: 'RuleExecutionContext' has only data, no behavior |
| 218 |  | info | Function 'visit_ClassDef' should use snake_case naming |
| 224 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 230 |  | info | Function 'visit_Call' should use snake_case naming |
| 236 |  | info | Function 'visit_Assign' should use snake_case naming |
| 285 |  | warning | Function '_check_function_rule' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/logging.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/logging/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/logging/contextual_logger.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/logging/correlation.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/logging/setup.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/logging/structured_logger.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 18 |  | info | Potential anemic model: 'LogLevel' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### core/models/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/models/directory_metrics.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/models/file_metrics.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 90 |  | warning | Function '__init__' has complexity 12, consider refactoring (max: 10) |
| 90 |  | warning | Function '__init__' has 33 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### core/models/progress_tracker.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 31 |  | error | Empty except block silently ignores exceptions |
| 165 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |
| 165 | B110 | LOW | Try, Except, Pass detected. |

### core/models/project_metrics.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 19 |  | error | God class detected: 'ProjectMetrics' has 24 methods |
| 386 |  | warning | Function 'from_dict' has complexity 17, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/progress.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 19 |  | info | Potential anemic model: 'ProgressState' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### core/simple_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 31 |  | warning | Function 'simple_analyze_project' has complexity 14, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/trend_analysis/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/trend_analysis/trend_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 223 |  | warning | Function '_generate_trend_summary' has complexity 18, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/trend_analysis/trend_storage.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/trend_analysis/trend_visualizer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 33 |  | warning | Function 'visualize_trends' has complexity 12, consider refactoring (max: 10) |
| 145 |  | warning | Function '_create_trend_chart' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### core/utils/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/utils/async_utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/utils/config_utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/utils/dict_utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/utils/error_handling.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/utils/error_utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/utils/file_utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 54 |  | error | Empty except block silently ignores exceptions |
| 92 |  | warning | Function 'list_files' has complexity 13, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/utils/fs_utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 117 |  | error | Empty except block silently ignores exceptions |
| 168 |  | warning | Function 'list_files' has complexity 13, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/utils/gitignore_utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/utils/preset_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/utils/report_utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/utils/tool_utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/auto_fix.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'FixResult' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### core/vcs/cache.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |
| 11 | B403 | LOW | Consider possible security implications associated with pickle module. |
| 100 | B301 | MEDIUM | Pickle and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue. |

### core/vcs/config.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 88 |  | warning | Function 'from_dict' has complexity 19, consider refactoring (max: 10) |
| 246 |  | error | Empty except block silently ignores exceptions |
| 251 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |

### core/vcs/dependency_tracker.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/engine.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 47 |  | error | God class detected: 'VibeCheckEngine' has 55 methods |
| 116 |  | warning | Function 'initialize' has complexity 16, consider refactoring (max: 10) |
| 211 |  | warning | Function 'stop' has complexity 14, consider refactoring (max: 10) |
| 818 |  | warning | Function 'create_analysis_request' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### core/vcs/incremental_analysis.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 23 |  | info | Potential anemic model: 'ChangeType' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### core/vcs/integration/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/integration/ecosystem_integrator.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/integration/integration_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 36 |  | warning | Function 'perform_comprehensive_analysis' has complexity 11, consider refactoring (max: 10) |
| 36 |  | warning | Function 'perform_comprehensive_analysis' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### core/vcs/integration/meta_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 223 |  | warning | Function '_generate_recommendations' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/vcs/integration/unified_reporter.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 58 |  | warning | Function 'create_unified_report' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### core/vcs/memory_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 31 |  | info | Potential anemic model: 'MemoryPressure' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### core/vcs/models.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 16 |  | info | Potential anemic model: 'EngineMode' has only data, no behavior |
| 22 |  | info | Potential anemic model: 'RuleCategory' has only data, no behavior |
| 32 |  | info | Potential anemic model: 'IssueSeverity' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### core/vcs/performance.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 23 |  | info | Potential anemic model: 'PerformanceMetric' has only data, no behavior |
| 34 |  | info | Potential anemic model: 'AnalysisPerformance' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### core/vcs/plugins/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/plugins/plugin_interface.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/plugins/plugin_loader.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/plugins/plugin_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/plugins/plugin_registry.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 22 |  | error | God class detected: 'PluginRegistry' has 23 methods |
| 0 | mypy.error | LOW |  |

### core/vcs/registry.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 30 |  | warning | Function '__init__' has 6 arguments, consider reducing (max: 5) |
| 101 |  | warning | Function 'create_issue' has 8 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### core/vcs/rules/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/rules/advanced_python_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/rules/complexity_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 34 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 47 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 58 |  | info | Function 'visit_If' should use snake_case naming |
| 65 |  | info | Function 'visit_While' should use snake_case naming |
| 69 |  | info | Function 'visit_For' should use snake_case naming |
| 73 |  | info | Function 'visit_ExceptHandler' should use snake_case naming |
| 77 |  | info | Function 'visit_With' should use snake_case naming |
| 81 |  | info | Function 'visit_Assert' should use snake_case naming |
| 85 |  | info | Function 'visit_BoolOp' should use snake_case naming |
| 90 |  | info | Function 'visit_Compare' should use snake_case naming |
| 125 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 150 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 198 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 202 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 222 |  | info | Function 'visit_If' should use snake_case naming |
| 225 |  | info | Function 'visit_While' should use snake_case naming |
| 228 |  | info | Function 'visit_For' should use snake_case naming |
| 231 |  | info | Function 'visit_With' should use snake_case naming |
| 234 |  | info | Function 'visit_Try' should use snake_case naming |
| 237 |  | info | Function 'visit_ExceptHandler' should use snake_case naming |
| 278 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 302 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 330 |  | info | Function 'visit_ClassDef' should use snake_case naming |
| 0 | mypy.error | LOW |  |

### core/vcs/rules/documentation_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 44 |  | info | Function 'visit_ClassDef' should use snake_case naming |
| 56 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 69 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 91 |  | warning | Function 'analyze' has complexity 14, consider refactoring (max: 10) |
| 96 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 102 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 105 |  | info | Function 'visit_ClassDef' should use snake_case naming |
| 283 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 318 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 0 | mypy.error | LOW |  |

### core/vcs/rules/framework_rules/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/rules/framework_rules/django_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 212 |  | warning | Function '_check_setting_security' has complexity 13, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/vcs/rules/framework_rules/fastapi_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### core/vcs/rules/framework_rules/flask_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 79 |  | warning | Function 'analyze' has complexity 13, consider refactoring (max: 10) |
| 133 |  | warning | Function 'analyze' has complexity 12, consider refactoring (max: 10) |
| 245 |  | warning | Function 'analyze' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/vcs/rules/framework_rules/framework_detector.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'FrameworkType' has only data, no behavior |
| 148 |  | warning | Function '_detect_from_ast' has complexity 13, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/vcs/rules/import_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 39 |  | info | Function 'visit_Import' should use snake_case naming |
| 44 |  | info | Function 'visit_ImportFrom' should use snake_case naming |
| 53 |  | info | Function 'visit_Name' should use snake_case naming |
| 56 |  | info | Function 'visit_Attribute' should use snake_case naming |
| 121 |  | info | Function 'visit_Import' should use snake_case naming |
| 126 |  | info | Function 'visit_ImportFrom' should use snake_case naming |
| 206 |  | info | Function 'visit_ImportFrom' should use snake_case naming |
| 242 |  | info | Function 'visit_ImportFrom' should use snake_case naming |
| 287 |  | info | Function 'visit_ImportFrom' should use snake_case naming |
| 304 |  | info | Function 'visit_Import' should use snake_case naming |
| 345 |  | info | Function 'visit_Import' should use snake_case naming |
| 348 |  | info | Function 'visit_ImportFrom' should use snake_case naming |
| 0 | mypy.error | LOW |  |

### core/vcs/rules/performance_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 157 |  | warning | Function '_check_function_calls' has complexity 12, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/vcs/rules/rule_loader.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 355 |  | warning | Function 'load_framework_rules' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/vcs/rules/security_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 73 |  | warning | Function 'analyze' has complexity 11, consider refactoring (max: 10) |
| 78 |  | info | Function 'visit_BinOp' should use snake_case naming |
| 92 |  | info | Function 'visit_Call' should use snake_case naming |
| 150 |  | info | Function 'visit_Call' should use snake_case naming |
| 207 |  | info | Function 'visit_Call' should use snake_case naming |
| 270 |  | info | Function 'visit_Call' should use snake_case naming |
| 0 | mypy.error | LOW |  |

### core/vcs/rules/style_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 151 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 163 |  | info | Function 'visit_ClassDef' should use snake_case naming |
| 175 |  | info | Function 'visit_Name' should use snake_case naming |
| 260 |  | warning | Function 'analyze' has complexity 13, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### core/vcs/rules/type_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 33 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 69 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 101 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 140 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 177 |  | warning | Function 'analyze' has complexity 13, consider refactoring (max: 10) |
| 182 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 212 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 266 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 276 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 323 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 337 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 381 |  | warning | Function 'analyze' has complexity 16, consider refactoring (max: 10) |
| 386 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 402 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 0 | mypy.error | LOW |  |

### core/vcs/type_checking.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |
| 9 | B404 | LOW | Consider possible security implications associated with the subprocess module. |
| 250 | B603 | LOW | subprocess call - check for execution of untrusted input. |
| 266 | B603 | LOW | subprocess call - check for execution of untrusted input. |

### core/version.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/api/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/api/graphql.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | error | God class detected: 'GraphQLServer' has 22 methods |
| 0 | mypy.error | LOW |  |

### enterprise/api/models.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 15 |  | info | Potential anemic model: 'APIMethod' has only data, no behavior |
| 24 |  | info | Potential anemic model: 'APIStatus' has only data, no behavior |
| 31 |  | info | Potential anemic model: 'WebSocketMessageType' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### enterprise/api/rest.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 23 |  | error | God class detected: 'RestAPIServer' has 23 methods |
| 175 |  | warning | Function '_register_endpoint' has 6 arguments, consider reducing (max: 5) |
| 220 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |

### enterprise/api/unified.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/api/websocket.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 124 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |

### enterprise/cicd/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/cicd/azure_devops.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/cicd/github_actions.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/cicd/gitlab_ci.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/cicd/jenkins.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |
| 8 | B405 | LOW | Using xml.etree.ElementTree to parse untrusted XML data is known to be vulnerable to XML attacks. Replace xml.etree.ElementTree with the equivalent defusedxml package, or make sure defusedxml.defuse_stdlib() is called. |

### enterprise/cicd/manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/cicd/models.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 15 |  | info | Potential anemic model: 'CICDPlatform' has only data, no behavior |
| 25 |  | info | Potential anemic model: 'PipelineStatus' has only data, no behavior |
| 35 |  | info | Potential anemic model: 'BuildResult' has only data, no behavior |
| 43 |  | info | Potential anemic model: 'QualityGateStatus' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### enterprise/collaboration.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'UserRole' has only data, no behavior |
| 29 |  | info | Potential anemic model: 'TeamPermission' has only data, no behavior |
| 361 |  | warning | Function 'add_team_member' has 7 arguments, consider reducing (max: 5) |
| 628 |  | warning | Function 'save_configuration' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### enterprise/collaboration/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/collaboration/analysis.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 22 |  | info | Potential anemic model: 'AnalysisStatus' has only data, no behavior |
| 31 |  | info | Potential anemic model: 'AnalysisPriority' has only data, no behavior |
| 247 |  | warning | Function 'create_analysis_request' has 11 arguments, consider reducing (max: 5) |
| 332 |  | warning | Function 'add_comment' has 8 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### enterprise/collaboration/shared_config.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 100 |  | warning | Function 'save_configuration' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### enterprise/collaboration/team_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'UserRole' has only data, no behavior |
| 29 |  | info | Potential anemic model: 'TeamPermission' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### enterprise/dashboard/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/dashboard/components.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 83 |  | warning | Function 'get_data' has complexity 15, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### enterprise/dashboard/models.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 15 |  | info | Potential anemic model: 'DashboardTheme' has only data, no behavior |
| 22 |  | info | Potential anemic model: 'ChartType' has only data, no behavior |
| 34 |  | info | Potential anemic model: 'WidgetSize' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### enterprise/dashboard/static_assets.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/dashboard/templates.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/dashboard/web_server.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 367 |  | warning | Function '_get_metrics_card_data' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### enterprise/integration.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/monitoring.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/monitoring/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/monitoring/alerting.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 533 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |

### enterprise/monitoring/dashboards.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 170 |  | warning | Function '_get_system_health_data' has complexity 13, consider refactoring (max: 10) |
| 285 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |

### enterprise/monitoring/models.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 15 |  | info | Potential anemic model: 'AlertSeverity' has only data, no behavior |
| 23 |  | info | Potential anemic model: 'AlertStatus' has only data, no behavior |
| 31 |  | info | Potential anemic model: 'QualityGateStatus' has only data, no behavior |
| 40 |  | info | Potential anemic model: 'MetricType' has only data, no behavior |
| 48 |  | info | Potential anemic model: 'ThresholdOperator' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### enterprise/monitoring/monitoring.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 53 |  | warning | Function 'record_metric' has 6 arguments, consider reducing (max: 5) |
| 211 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |

### enterprise/monitoring/quality_gates.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 378 |  | warning | Function 'create_gate' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |
| 224 | B307 | MEDIUM | Use of possibly insecure function - consider using safer ast.literal_eval. |

### enterprise/performance/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/performance/cache_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'CacheStrategy' has only data, no behavior |
| 400 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |
| 239 | B324 | HIGH | Use of weak MD5 hash for security. Consider usedforsecurity=False |

### enterprise/performance/distributed_processor.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'TaskStatus' has only data, no behavior |
| 30 |  | info | Potential anemic model: 'NodeStatus' has only data, no behavior |
| 447 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |

### enterprise/performance/load_balancer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 17 |  | info | Potential anemic model: 'LoadBalancingStrategy' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### enterprise/performance/performance_optimizer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 21 |  | info | Potential anemic model: 'OptimizationProfile' has only data, no behavior |
| 221 |  | warning | Function '_auto_optimize' has complexity 12, consider refactoring (max: 10) |
| 359 |  | error | Empty except block silently ignores exceptions |
| 366 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |

### enterprise/performance/scalability_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/reporting/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/reporting/customization.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/reporting/engine.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 22 |  | info | Potential anemic model: 'ReportFormat' has only data, no behavior |
| 32 |  | info | Potential anemic model: 'ReportType' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### enterprise/reporting/executive.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'RiskLevel' has only data, no behavior |
| 28 |  | info | Potential anemic model: 'BusinessImpact' has only data, no behavior |
| 255 |  | warning | Function '_assess_risks' has complexity 15, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### enterprise/reporting/formats.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/reporting/templates.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 19 |  | info | Potential anemic model: 'TemplateType' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### enterprise/security/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### enterprise/security/access_control.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 19 |  | info | Potential anemic model: 'Permission' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### enterprise/security/audit_trail.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 20 |  | info | Potential anemic model: 'AuditEventType' has only data, no behavior |
| 43 |  | info | Potential anemic model: 'AuditSeverity' has only data, no behavior |
| 138 |  | warning | Function 'search_events' has complexity 17, consider refactoring (max: 10) |
| 138 |  | warning | Function 'search_events' has 9 arguments, consider reducing (max: 5) |
| 295 |  | warning | Function 'log_user_event' has 12 arguments, consider reducing (max: 5) |
| 347 |  | warning | Function 'log_system_event' has 7 arguments, consider reducing (max: 5) |
| 385 |  | warning | Function 'log_api_call' has 10 arguments, consider reducing (max: 5) |
| 415 |  | warning | Function 'log_analysis_event' has 7 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### enterprise/security/compliance_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 19 |  | info | Potential anemic model: 'ComplianceFramework' has only data, no behavior |
| 29 |  | info | Potential anemic model: 'ComplianceStatus' has only data, no behavior |
| 205 |  | warning | Function 'update_control_status' has 6 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### enterprise/security/encryption_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 18 |  | info | Potential anemic model: 'EncryptionAlgorithm' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### enterprise/security/security_monitor.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 19 |  | info | Potential anemic model: 'ThreatLevel' has only data, no behavior |
| 27 |  | info | Potential anemic model: 'AlertType' has only data, no behavior |
| 156 |  | warning | Function 'create_alert' has 9 arguments, consider reducing (max: 5) |
| 0 | mypy.error | LOW |  |

### plugins/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### plugins/base_plugin.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### plugins/manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 143 |  | warning | Function '_scan_module_for_plugins' has complexity 12, consider refactoring (max: 10) |
| 167 |  | error | Empty except block silently ignores exceptions |
| 180 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |
| 167 | B110 | LOW | Try, Except, Pass detected. |
| 180 | B110 | LOW | Try, Except, Pass detected. |

### plugins/plugin_base.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### plugins/plugin_interface.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### plugins/plugin_registry.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/custom_rules/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/custom_rules/python_rules.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 85 |  | error | Empty except block silently ignores exceptions |
| 153 |  | warning | Function '_check_resource_management' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### tools/parsers/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/parsers/bandit_parser.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/parsers/base_parser.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/parsers/complexity_parser.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 30 |  | warning | Function 'parse' has complexity 12, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### tools/parsers/custom_rules_parser.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/parsers/doc_analyzer_parser.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 30 |  | warning | Function 'parse' has complexity 27, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### tools/parsers/mypy_parser.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/parsers/parser_registry.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/parsers/pyflakes_parser.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/parsers/pylint_parser.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/parsers/ruff_parser.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/runners/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/runners/bandit_runner.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 35 |  | warning | Function 'run' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### tools/runners/base_runner.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 184 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |
| 10 | B404 | LOW | Consider possible security implications associated with the subprocess module. |
| 184 | B110 | LOW | Try, Except, Pass detected. |

### tools/runners/complexity_runner.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 35 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 75 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 84 |  | info | Function 'visit_ClassDef' should use snake_case naming |
| 104 |  | info | Function 'visit_If' should use snake_case naming |
| 116 |  | info | Function 'visit_For' should use snake_case naming |
| 128 |  | info | Function 'visit_AsyncFor' should use snake_case naming |
| 140 |  | info | Function 'visit_While' should use snake_case naming |
| 152 |  | info | Function 'visit_Try' should use snake_case naming |
| 169 |  | info | Function 'visit_With' should use snake_case naming |
| 180 |  | info | Function 'visit_AsyncWith' should use snake_case naming |
| 191 |  | info | Function 'visit_BoolOp' should use snake_case naming |
| 0 | mypy.error | LOW |  |

### tools/runners/custom_rules_runner.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/runners/doc_analyzer_runner.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 36 |  | info | Function 'visit_Module' should use snake_case naming |
| 62 |  | info | Function 'visit_ClassDef' should use snake_case naming |
| 100 |  | info | Function 'visit_FunctionDef' should use snake_case naming |
| 133 |  | info | Function 'visit_AsyncFunctionDef' should use snake_case naming |
| 204 |  | warning | Function 'run' has complexity 17, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### tools/runners/mypy_runner.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 208 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |

### tools/runners/pyflakes_runner.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/runners/pylint_runner.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tools/runners/ruff_runner.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 188 |  | error | Empty except block silently ignores exceptions |
| 0 | mypy.error | LOW |  |
| 188 | B110 | LOW | Try, Except, Pass detected. |

### tools/runners/tool_registry.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/cli/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/cli/commands.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/cli/formatter.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/gui/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/gui/app.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/gui/main_window.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 22 |  | error | God class detected: 'VibeCheckGUI' has 21 methods |
| 0 | mypy.error | LOW |  |

### ui/gui/simple_gui.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |
| 15 | B404 | LOW | Consider possible security implications associated with the subprocess module. |
| 332 | B603 | LOW | subprocess call - check for execution of untrusted input. |

### ui/gui/themes.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/reporting/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/reporting/custom_report_generator.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 119 |  | warning | Function '_create_json_report' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### ui/reporting/formatters.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 97 |  | warning | Function 'format_analysis_results' has complexity 12, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### ui/reporting/generators.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 118 |  | warning | Function '_generate_top_issues' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### ui/reporting/markdown.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/reporting/report_generator.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/reporting/templates.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/tui/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/tui/app.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 43 |  | warning | Function 'handle_input' has complexity 16, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |
| 102 | B605 | HIGH | Starting a process with a shell, possible injection detected, security issue. |
| 208 | B605 | HIGH | Starting a process with a shell, possible injection detected, security issue. |

### ui/tui/components.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/tui/config_components.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/tui/header_footer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/tui/menu_components.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/tui/progress_components.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/tui/results_components.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 141 |  | warning | Function 'render_issues_view' has complexity 13, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### ui/tui/state_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 22 |  | info | Potential anemic model: 'TUIScreen' has only data, no behavior |
| 33 |  | info | Potential anemic model: 'AnalysisState' has only data, no behavior |
| 0 | mypy.error | LOW |  |

### ui/visualization/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/visualization/charts.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/visualization/exporters.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/visualization/generators.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/visualization/interactive_charts.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 703 |  | warning | Function 'create_import_dependency_graph' has complexity 17, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### ui/visualization/visualization_generator.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/web/__init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/web/app.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 30 |  | warning | Function 'create_app' has complexity 13, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### ui/web/components.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 203 |  | warning | Function 'render_issues' has complexity 13, consider refactoring (max: 10) |
| 487 |  | warning | Function 'render_dependency_graph' has complexity 11, consider refactoring (max: 10) |
| 0 | mypy.error | LOW |  |

### ui/web/run_web_ui.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### ui/web/state_manager.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 26 |  | info | Potential anemic model: 'AnalysisState' has only data, no behavior |
| 35 |  | info | Potential anemic model: 'UIState' has only data, no behavior |
| 0 | mypy.error | LOW |  |

